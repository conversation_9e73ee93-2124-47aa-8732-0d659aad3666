/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BusinessHandler: typeof import('./../components/OnlineRevenue/BusinessHandler.vue')['default']
    CardGasCalculator: typeof import('./../components/CardPayment/CardGasCalculator.vue')['default']
    CardPaymentForm: typeof import('./../components/CardPayment/CardPaymentForm.vue')['default']
    CardPaymentInfo: typeof import('./../components/CardPayment/CardPaymentInfo.vue')['default']
    CardReceipt: typeof import('./../components/CardPayment/CardReceipt.vue')['default']
    ChargePrintSelectorAndRemarks: typeof import('./../components/common/ChargePrintSelectorAndRemarks.vue')['default']
    CodePayment: typeof import('./../components/common/CodePayment.vue')['default']
    DebtDetailCard: typeof import('./../components/mechanical-meter/DebtDetailCard.vue')['default']
    DebtSummary: typeof import('./../components/mechanical-meter/DebtSummary.vue')['default']
    ExtendedInfo: typeof import('./../components/meterReplacement/ExtendedInfo.vue')['default']
    FileUploader: typeof import('./../components/common/FileUploader.vue')['default']
    FormField: typeof import('./../components/common/FormField.vue')['default']
    GasExchangeCalculator: typeof import('./../components/SellGas/GasExchangeCalculator.vue')['default']
    GasReceipt: typeof import('./../components/SellGas/GasReceipt.vue')['default']
    GasSupplementInfo: typeof import('./../components/meterReplacement/GasSupplementInfo.vue')['default']
    GridFileUploader: typeof import('./../components/common/GridFileUploader.vue')['default']
    InfoCard: typeof import('./../components/common/InfoCard.vue')['default']
    InfoDisplayCard: typeof import('./../components/common/infoDisplayCard/index.vue')['default']
    InfoHeader: typeof import('./../components/common/InfoHeader.vue')['default']
    IotGasCalculator: typeof import('./../components/IotMeter/IotGasCalculator.vue')['default']
    IotMeterOpenNewAccount: typeof import('./../components/IotMeterOpenNewAccount/index.vue')['default']
    IotMeterParamsSet: typeof import('./../components/IotMeterParamsSet/index.vue')['default']
    IotMeterParamsSetXy: typeof import('./../components/IotMeterParamsSet/IotMeterParamsSetXy.vue')['default']
    IotMeterPeriodicValveClosure: typeof import('./../components/IotMeterPeriodicValveClosure/index.vue')['default']
    IotMeterPlatformUnbind: typeof import('./../components/IotMeterPlatformUnbind/index.vue')['default']
    IotMeterValveControl: typeof import('./../components/IotMeterValveControl/index.vue')['default']
    IotMeterValveControlXy: typeof import('./../components/IotMeterValveControl/IotMeterValveControlXy.vue')['default']
    IotPaymentForm: typeof import('./../components/IotMeter/IotPaymentForm.vue')['default']
    IotPaymentInfo: typeof import('./../components/IotMeter/IotPaymentInfo.vue')['default']
    IotReceipt: typeof import('./../components/IotMeter/IotReceipt.vue')['default']
    MechanicalMeterPayment: typeof import('./../components/mechanical-meter/MechanicalMeterPayment.vue')['default']
    MechanicalPaymentInfo: typeof import('./../components/mechanical-meter/MechanicalPaymentInfo.vue')['default']
    MechanicalReceipt: typeof import('./../components/mechanical-meter/MechanicalReceipt.vue')['default']
    MeterReadingCard: typeof import('./../components/MeterReading/MeterReadingCard.vue')['default']
    MeterReadingFilter: typeof import('./../components/MeterReading/MeterReadingFilter.vue')['default']
    MeterReadingSearch: typeof import('./../components/MeterReading/MeterReadingSearch.vue')['default']
    MrExtendedInfo: typeof import('./../components/meterReset/MrExtendedInfo.vue')['default']
    MrGasSupplementInfo: typeof import('./../components/meterReset/MrGasSupplementInfo.vue')['default']
    MrNewMeterForm: typeof import('./../components/meterReset/MrNewMeterForm.vue')['default']
    MrOldMeterInfo: typeof import('./../components/meterReset/MrOldMeterInfo.vue')['default']
    NewMeterForm: typeof import('./../components/meterReplacement/NewMeterForm.vue')['default']
    NewMeterFormXy: typeof import('./../components/meterReplacement/NewMeterFormXy.vue')['default']
    NotJinKaParamSet: typeof import('./../components/NotJinKaParamSet/index.vue')['default']
    OldMeterInfo: typeof import('./../components/meterReplacement/OldMeterInfo.vue')['default']
    OtherChargeForm: typeof import('./../components/OtherCharge/OtherChargeForm.vue')['default']
    OtherChargeItem: typeof import('./../components/OtherCharge/OtherChargeItem.vue')['default']
    OtherChargeItemModal: typeof import('./../components/OtherCharge/OtherChargeItemModal.vue')['default']
    OtherChargeReceipt: typeof import('./../components/OtherCharge/OtherChargeReceipt.vue')['default']
    PaymentInfo: typeof import('./../components/SellGas/PaymentInfo.vue')['default']
    PaymentMethodSelector: typeof import('./../components/common/PaymentMethodSelector.vue')['default']
    PaymentMethodSelectorCard: typeof import('./../components/common/PaymentMethodSelectorCard.vue')['default']
    ReceiptModal: typeof import('./../components/common/ReceiptModal.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SellGasForm: typeof import('./../components/SellGas/SellGasForm.vue')['default']
    StairInfo: typeof import('./../components/common/StairInfo.vue')['default']
    UserProfile: typeof import('./../components/OnlineRevenue/UserProfile.vue')['default']
    UserSearch: typeof import('./../components/OnlineRevenue/UserSearch.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCascader: typeof import('vant/es')['Cascader']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCol: typeof import('vant/es')['Col']
    VanCollapse: typeof import('vant/es')['Collapse']
    VanCollapseItem: typeof import('vant/es')['CollapseItem']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanDialog: typeof import('vant/es')['Dialog']
    VanDivider: typeof import('vant/es')['Divider']
    VanField: typeof import('vant/es')['Field']
    VanIcon: typeof import('vant/es')['Icon']
    VanImagePreview: typeof import('vant/es')['ImagePreview']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPicker: typeof import('vant/es')['Picker']
    VanPickerGroup: typeof import('vant/es')['PickerGroup']
    VanPopup: typeof import('vant/es')['Popup']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanRow: typeof import('vant/es')['Row']
    VanStepper: typeof import('vant/es')['Stepper']
    VanSwipeCell: typeof import('vant/es')['SwipeCell']
    VanSwitch: typeof import('vant/es')['Switch']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTimePicker: typeof import('vant/es')['TimePicker']
  }
}
