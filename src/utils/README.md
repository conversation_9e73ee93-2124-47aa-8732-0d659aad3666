# 离线图标加载器使用指南

## 概述

`iconLoader.ts` 是一个专门用于按需加载 Iconify 图标的工具，解决了导入完整图标集导致的构建问题。

## 问题背景

之前项目中使用以下方式导入 fluent-emoji 图标集：

```typescript
import fluentEmojiIcons from '@iconify/json/json/fluent-emoji.json'
addCollection(fluentEmojiIcons as IconifyJSON)
```

这种方式存在以下问题：
- **文件过大**: `fluent-emoji.json` 约 100MB，包含数千个图标
- **构建失败**: 导致 Rollup 构建时出现 "Invalid array length" 错误
- **性能问题**: 加载大量不必要的图标数据
- **内存溢出**: 构建过程中可能导致内存不足

## 解决方案

使用按需加载方式，只导入项目中实际使用的图标：

### 1. 基本使用

```typescript
// main.ts
import { loadOfflineIcons } from '@/utils/iconLoader'

// 加载项目中使用的所有离线图标
loadOfflineIcons()
```

```vue
<!-- 在组件中使用 -->
<template>
  <Icon icon="fluent-emoji:file-folder" />
</template>
```

### 2. 添加新图标

如果需要添加新的 fluent-emoji 图标：

1. 访问 [Iconify 图标库](https://iconify.design/icon-sets/fluent-emoji/)
2. 找到所需图标，复制 SVG 数据
3. 在 `iconLoader.ts` 的 `PROJECT_ICONS` 中添加：

```typescript
const PROJECT_ICONS = {
  'file-folder': {
    body: '<path fill="#FFD43B" d="..."/>',
    width: 16,
    height: 16
  },
  // 添加新图标
  'smile': {
    body: '<path fill="currentColor" d="..."/>',
    width: 16,
    height: 16
  }
}
```

### 3. 动态添加图标

也可以在运行时动态添加图标：

```typescript
import { addCustomIcon } from '@/utils/iconLoader'

addCustomIcon('heart', {
  body: '<path fill="red" d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>',
  width: 24,
  height: 24
})
```

### 4. 工具函数

```typescript
import { getLoadedIcons, isIconLoaded } from '@/utils/iconLoader'

// 获取所有已加载的图标
const icons = getLoadedIcons()
console.log(icons) // ['fluent-emoji:file-folder']

// 检查图标是否已加载
const isLoaded = isIconLoaded('fluent-emoji:file-folder')
console.log(isLoaded) // true
```

## 性能对比

| 方式 | 文件大小 | 构建时间 | 内存使用 | 状态 |
|------|----------|----------|----------|------|
| 完整图标集 | ~100MB | 失败 | 高 | ❌ 构建错误 |
| 按需加载 | ~几KB | 正常 | 低 | ✅ 构建成功 |

## 注意事项

1. **图标命名**: 使用时需要包含前缀 `fluent-emoji:`
2. **SVG 数据**: 确保 SVG 数据格式正确，避免包含不必要的属性
3. **尺寸一致性**: 建议保持图标尺寸的一致性（如都使用 16x16）
4. **缓存**: 图标会被 Iconify 自动缓存，重复使用不会重复加载

## 迁移指南

如果你之前使用完整图标集导入：

### 之前的方式 ❌
```typescript
import fluentEmojiIcons from '@iconify/json/json/fluent-emoji.json'
import { addCollection } from '@iconify/vue'
addCollection(fluentEmojiIcons as IconifyJSON)
```

### 现在的方式 ✅
```typescript
import { loadOfflineIcons } from '@/utils/iconLoader'
loadOfflineIcons()
```

组件中的使用方式保持不变：
```vue
<Icon icon="fluent-emoji:file-folder" />
```

## 常见问题

### Q: 如何找到图标的 SVG 数据？
A: 访问 [Iconify 官网](https://iconify.design/)，搜索图标，点击图标后选择 "Copy SVG" 或查看 "Icon Data"。

### Q: 能否混用在线和离线图标？
A: 可以。Iconify 会优先使用离线图标，如果没有找到会尝试在线加载。

### Q: 如何确认图标是否正确加载？
A: 查看浏览器控制台，成功加载会显示 "✅ 离线图标加载完成" 消息。

## 技术细节

- 基于 `@iconify/vue` 的 `addCollection` API
- 使用 TypeScript 提供类型安全
- 支持错误处理和日志记录
- 符合 Iconify 图标数据格式规范
