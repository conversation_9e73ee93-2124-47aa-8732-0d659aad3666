import dayjs from 'dayjs'

/**
 * 格式化时间为标准格式
 * @param time 时间戳或日期字符串
 * @returns 格式化后的时间字符串 YYYY-MM-DD HH:mm:ss
 */
export function formatTime(time: string | number | Date): string {
  if (!time)
    return '--'
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化日期为简短格式
 * @param date 日期
 * @returns 格式化后的日期字符串 YYYY-MM-DD
 */
export function formatDate(date: string | number | Date): string {
  if (!date)
    return '--'
  return dayjs(date).format('YYYY-MM-DD')
}

/**
 * 获取相对于今天的日期
 * @param days 天数，正数为未来，负数为过去
 * @returns 日期对象
 */
export function getRelativeDate(days: number): Date {
  return dayjs().add(days, 'day').toDate()
}
