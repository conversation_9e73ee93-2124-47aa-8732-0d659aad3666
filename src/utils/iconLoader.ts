import { addCollection } from '@iconify/vue'

/**
 * 离线图标加载器
 *
 * 用于按需加载 Iconify 图标，避免加载整个图标集导致的构建问题
 *
 * 优势：
 * 1. 大大减少包体积 (从 ~100MB 减少到几KB)
 * 2. 避免构建时的内存溢出错误
 * 3. 提高加载性能
 * 4. 支持离线使用
 *
 * 使用方式：
 * 1. 在 main.ts 中调用 loadOfflineIcons()
 * 2. 在组件中使用 <Icon icon="fluent-emoji:file-folder" />
 */

/**
 * 项目中使用的 fluent-emoji 图标定义
 * 如需添加新图标，请访问 https://iconify.design/icon-sets/fluent-emoji/ 获取 SVG 数据
 */
const PROJECT_ICONS = {
  'file-folder': {
    body: '<path fill="#FFD43B" d="M2 5.5a2.5 2.5 0 0 1 2.5-2.5h3.379a2.5 2.5 0 0 1 1.768.732l.853.853a.5.5 0 0 0 .354.147H13.5a2.5 2.5 0 0 1 2.5 2.5v6.313a2.5 2.5 0 0 1-2.5 2.5h-9a2.5 2.5 0 0 1-2.5-2.5V5.5Z"/><path fill="#F9C23C" d="M2 6.5a2.5 2.5 0 0 1 2.5-2.5h3.379a2.5 2.5 0 0 1 1.768.732l.853.853a.5.5 0 0 0 .354.147H13.5a2.5 2.5 0 0 1 2.5 2.5v5.313a2.5 2.5 0 0 1-2.5 2.5h-9a2.5 2.5 0 0 1-2.5-2.5V6.5Z"/>',
    width: 16,
    height: 16,
  },
  // 如需添加更多图标，请在此处扩展
  // 'icon-name': {
  //   body: 'SVG_PATH_DATA',
  //   width: 16,
  //   height: 16
  // }
}

/**
 * 加载项目中实际使用的离线图标
 *
 * @returns {boolean} 是否成功加载
 */
export function loadOfflineIcons(): boolean {
  try {
    const fluentEmojiIcons = {
      prefix: 'fluent-emoji',
      icons: PROJECT_ICONS,
      width: 16,
      height: 16,
    }

    // 注册图标集到 Iconify
    addCollection(fluentEmojiIcons)

    const iconCount = Object.keys(PROJECT_ICONS).length
    console.log(`✅ 离线图标加载完成: ${iconCount} 个 fluent-emoji 图标`)
    console.log(`📦 已加载图标: ${Object.keys(PROJECT_ICONS).map(name => `fluent-emoji:${name}`).join(', ')}`)

    return true
  }
  catch (error) {
    console.error('❌ 离线图标加载失败:', error)
    return false
  }
}

/**
 * 动态添加单个图标
 *
 * @param iconName 图标名称（不包含前缀 fluent-emoji:）
 * @param iconData 图标SVG数据
 * @returns {boolean} 是否成功添加
 *
 * @example
 * addCustomIcon('smile', {
 *   body: '<path fill="currentColor" d="..."/>',
 *   width: 16,
 *   height: 16
 * })
 */
export function addCustomIcon(
  iconName: string,
  iconData: {
    body: string
    width?: number
    height?: number
  },
): boolean {
  try {
    const customIcons = {
      prefix: 'fluent-emoji',
      icons: {
        [iconName]: {
          body: iconData.body,
          width: iconData.width || 16,
          height: iconData.height || 16,
        },
      },
      width: iconData.width || 16,
      height: iconData.height || 16,
    }

    addCollection(customIcons)
    console.log(`✅ 自定义图标添加成功: fluent-emoji:${iconName}`)

    return true
  }
  catch (error) {
    console.error(`❌ 自定义图标添加失败 (${iconName}):`, error)
    return false
  }
}

/**
 * 获取已加载的图标列表
 *
 * @returns {string[]} 已加载的图标名称列表
 */
export function getLoadedIcons(): string[] {
  return Object.keys(PROJECT_ICONS).map(name => `fluent-emoji:${name}`)
}

/**
 * 检查图标是否已加载
 *
 * @param iconName 完整的图标名称 (如: 'fluent-emoji:file-folder')
 * @returns {boolean} 是否已加载
 */
export function isIconLoaded(iconName: string): boolean {
  const [prefix, name] = iconName.split(':')
  return prefix === 'fluent-emoji' && name in PROJECT_ICONS
}
