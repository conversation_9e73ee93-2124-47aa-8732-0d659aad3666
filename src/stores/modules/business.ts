import type { StairUserData } from '@/components/common/types'
import type { BusinessUser, LastCardChargeRecord } from '@/components/OnlineRevenue/types'
import type { CardReadResponse } from '@/services/cardService/types'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

/**
 * 业务相关的存储
 * 用于存储当前业务的用户信息和卡片信息
 */
const useBusinessStore = defineStore('business', () => {
  // 当前业务用户信息
  const currentUser = ref<BusinessUser | null>(null)

  // 当前业务卡片信息
  const currentCard = ref<CardReadResponse | null>(null)

  // 当前用户的阶梯信息
  const currentUserStairInfo = ref<StairUserData>({})

  // 未写卡记录
  const unWriteCardRecord = ref<LastCardChargeRecord[]>([])

  // 设置当前业务用户
  function setCurrentUser(user: BusinessUser | null) {
    currentUser.value = user
  }

  // 设置当前业务卡片
  function setCurrentCard(card: CardReadResponse | null) {
    currentCard.value = card
  }

  // 设置当前用户的阶梯信息
  function setCurrentUserStairInfo(stairInfo: StairUserData) {
    currentUserStairInfo.value = stairInfo
  }

  // 设置未写卡记录
  function setUnWriteCardRecord(record: LastCardChargeRecord[]) {
    unWriteCardRecord.value = record
  }

  // 清除当前业务信息
  function clearBusinessInfo() {
    currentUser.value = null
    currentCard.value = null
    currentUserStairInfo.value = {}
    unWriteCardRecord.value = []
  }

  // 检查是否可以进行购气撤销
  const canRefundGas = computed(() => {
    // 检查卡上是否有气量或金额的条件
    if (currentUser.value && currentCard.value) {
      const cardMatches = currentUser.value.f_card_id === currentCard.value.CardID
      const hasGasOrMoney = (currentCard.value.Gas && currentCard.value.Gas > 0) || (currentCard.value.Money && currentCard.value.Money > 0)

      return Boolean(cardMatches && hasGasOrMoney)
    }
    return false
  })

  return {
    currentUser,
    currentCard,
    currentUserStairInfo,
    canRefundGas,
    setCurrentUser,
    setCurrentCard,
    setCurrentUserStairInfo,
    clearBusinessInfo,
    unWriteCardRecord,
    setUnWriteCardRecord,
  }
}, {
  // 持久化存储配置
  persist: true,
})

export default useBusinessStore
