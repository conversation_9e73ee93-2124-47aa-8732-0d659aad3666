/**
 * 卡片读取返回的数据接口
 */
export interface CardReadResponse {
  /** 表厂商 */
  Factory?: string
  /** 卡号 */
  CardID?: string
  /** 气量 */
  Gas?: number
  /** 金额 */
  Money?: number
  /** 总金额 */
  TotalMoney?: number
  /** 购气次数 */
  Times?: number
  /** 补卡次数 */
  RenewTimes?: number
  /** 用户号 */
  Yhh?: string
  /** 表具ID */
  Meterid?: string
  /** 新参数 */
  NewParam?: {
    /** 累计购气量 */
    ljgql: number
    [key: string]: any
  }
  /** 卡片参数 */
  CardParam?: string
  /** 错误信息 */
  Err: string | null
  /** 异常信息 */
  Exception: string | null
  /** 异常信息 */
  Kmm: string | null
}

export interface CardUser {
  f_card_id: string
  f_hascard?: string
  f_share_times?: string
  f_meter_type: string
  f_gasbrand_id: string | number
  f_meter_brand: string
  f_user_name: string
  f_userinfo_code: string
  f_tag?: string
  f_user_id: number
  f_userfiles_id: number
  f_user_type?: string
  f_gasproperties?: string
  f_address?: string
  f_alias?: string
  f_card_password?: string
  f_police_gas?: string | number
  f_topup_ceil?: string | number
  f_overdr_lines?: string | number
  f_total_gas: number
  f_total_fee: number
  f_fillcard_times?: number
  f_times?: number
  f_meterid?: string | number
  f_meternumber?: string | number
  f_area_code?: string | number
  f_coding?: string | number
  f_support_purchase?: string
  ljgql?: number
  ljgqje?: number
  f_write_totalgas?: number
  f_write_totalfee?: number
  f_ispick_table?: string
}

/**
 * 补卡参数
 */
export interface FillCardParam {
  f_fill_type: string
  f_card_id: string
  f_type: string
  f_payment?: string
  f_bill_style?: string
  f_cardfees?: number
  f_serial_id?: string
  f_comments?: string
  f_ispick_table?: string
  f_times?: number
  files?: string[]
}

/**
 * 补气参数
 */
export interface FillGasParam {
  cardNumber?: string
  f_collection?: string
  f_comments?: string
  f_payment?: string
  f_preamount?: number
  f_pregas?: number
  f_print?: string
  f_total_fee?: number
  f_total_gas?: number
  f_voucher_number?: string
  f_totalcost?: number
  f_write_totalfee?: number
  f_write_totalgas?: number
}

/**
 * 转气参数
 */
export interface MoveGasParam {
  userfilesid?: string
  f_userinfo_code?: string
  f_move_fee?: number
  f_meter_base?: number
  f_move_gas?: number
  f_comments?: string
  files?: string[]
  f_from_userfiles_id?: string
  f_to_userfiles_id?: string
  f_from_gasbrand_id?: string
  f_to_gasbrand_id?: string
  f_from_stariprice_id?: string
  f_to_stariprice_id?: string
}

/**
 * 过户参数
 */
export interface TransferSaveParam {
  f_bill_style?: string
  f_comments?: string
  f_new_people_num?: number
  f_newaddress_phone?: string
  f_newcredentials?: string
  f_newemail?: string
  f_newidnumber?: string
  f_newpaper_account?: string
  f_newpaper_name?: string
  f_newrent_phone?: string
  f_newtaxpayer_id?: string
  f_newuser_name?: string
  f_newuser_phone?: string
  f_payment?: string
  f_transfer_fees?: string
  f_contract_id?: string
}

/**
 * 写卡参数接口
 */
export interface WriteCardParams {
  csql: any
  lastmoney: any
  ccsql: any
  lastlastmoney: any
  cccsql: any
  lastlastlastmoney: any
  ljgql: any
  ljyql: any
  yhh: any
  sxrq: any
  totalmoney: any
  stairversion: any
  pricedate: any
  pricetype: any
  stairgas1?: string | number
  stairgas2?: string | number
  stairgas3?: string | number
  stairprice1?: string | number
  stairprice2?: string | number
  stairprice3?: string | number
  cnpricedateend?: string
  cnpricedatestart?: string
  cnstairgas1?: string | number
  cnstairgas2?: string | number
  cnstairgas3?: string | number
  [key: string]: any
}
