import type { BusinessUser } from '@/components/OnlineRevenue'
import type { GasRefund } from '@/components/OnlineRevenue/types'
import type { CardReadResponse, FillCardParam, MoveGasParam, WriteCardParams } from './types'
/**
 * 卡片服务相关类型定义
 */
import {
  addGasRoolBack,
  cardCharge,
  cardChargeRollback,
  cardIssuanceGasSales,
  fillCard,
  getCardNumber,
  getCardParam,
  moveGas,
  moveGasRollback,
  offGasAddGasApi,
  remanent,
  remanentRollback,
  repairCardRoolBack,
  rollbackGen,
  salecardrollback,
  updateCardPassword,
} from '@/services/api/business'
import useBusinessStore from '@/stores/modules/business'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import { post } from '@af-mobile-client-vue3/services/restTools'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { mobileUtil } from '@af-mobile-client-vue3/utils/mobileUtil'
import axios from 'axios'
import dayjs from 'dayjs'

// 是否使用模拟卡服务函数（开发环境用）
const USE_MOCK_CARD_SERVICE = import.meta.env.MODE === 'development'

/**
 * 判断是否存在卡片
 * @returns 是否存在卡片
 */
export async function hasCard(): Promise<boolean> {
  if (USE_MOCK_CARD_SERVICE) {
    return mockHasCard()
      .then(res => !!res)
      .catch(() => {
        console.log('模拟插卡：默认返回true')
        return true
      })
  }

  return new Promise((resolve) => {
    mobileUtil.execute({
      funcName: 'hasCard',
      param: {},
      callbackFunc: (result) => {
        const json_data = (result as any).data.message
        resolve(!!json_data)
      },
    })
  })
}

/**
 * 读取卡片数据
 * @returns Promise<CardReadResponse> 卡片数据
 */
export async function readCard(): Promise<CardReadResponse> {
  if (USE_MOCK_CARD_SERVICE) {
    try {
      return await mockReadCard()
    }
    catch (error) {
      console.error('读卡失败', error)
      // 返回模拟数据，符合CardReadResponse接口
      return { Err: '读卡失败', Exception: '读卡失败', Kmm: '98765' } as any
    }
  }

  return new Promise((resolve) => {
    mobileUtil.execute({
      funcName: 'readCard',
      param: {
        f_org_id: useUserStore().getLogin().f.resources.orgid,
      },
      callbackFunc: (result) => {
        const json_data = JSON.parse((result as any).data.message)
        console.log(`json_data = ${json_data}`)
        if (json_data.code === 200) {
          resolve(json_data.msg as any)
        }
        else {
          const result = { Err: json_data.msg, Exception: '', Kmm: null }
          resolve(result)
        }
      },
    })
  })
}

/**
 * 写新卡
 */
export async function writeNewCard(param: any): Promise<{ Err: string, Exception: string, Kmm: string }> {
  const currUser = useUserStore().getLogin().f
  param.f_org_id = currUser.resources.orgid

  if (USE_MOCK_CARD_SERVICE) {
    try {
      const res = await mockWriteNewCard(param)
      return res.data
    }
    catch (error) {
      console.error('写卡失败', error)
      return { Err: '写卡失败', Exception: '写卡失败', Kmm: '98765' }
    }
  }

  return new Promise((resolve) => {
    mobileUtil.execute({
      funcName: 'WriteNewCard',
      param,
      callbackFunc: (result) => {
        const json_data = JSON.parse((result as any).data.message)
        console.log('json_data = ', json_data)
        if (json_data.code === 200) {
          resolve(json_data.msg as any)
        }
        else {
          const result = { Err: json_data.msg, Exception: '', Kmm: null }
          resolve(result)
        }
      },
    })
  })
}

/**
 * 写气卡
 */
export async function writeGasCard(param: any): Promise<{ Err: string, Exception: string, Kmm: string }> {
  const currUser = useUserStore().getLogin().f
  param.f_org_id = currUser.resources.orgid

  if (USE_MOCK_CARD_SERVICE) {
    try {
      const res = await mockWriteGasCard(param)
      return res.data
    }
    catch (error) {
      console.error('写气卡失败', error)
      return { Err: '', Exception: '', Kmm: '98765' }
    }
  }
  param.ulen = 0
  return new Promise((resolve) => {
    mobileUtil.execute({
      funcName: 'WriteGasCard',
      param,
      callbackFunc: (result) => {
        const json_data = JSON.parse((result as any).data.message)
        console.log('json_data = ', json_data)
        if (json_data.code === 200) {
          resolve(json_data.msg as any)
        }
        else {
          const result = { Err: json_data.msg, Exception: '', Kmm: null }
          resolve(result)
        }
      },
    })
  })
}

// ========= 模拟卡服务函数 =========

/**
 * 创建新的axios实例
 * 重新创建是为了避免axios的拦截器影响写卡读卡请求
 * 请求如果携带了Cookie，token到卡服务会导致请求失败
 */
function newInstancePost(url: string, data: any, config = {}) {
  // 重新创建是为了避免axios的拦截器影响写卡读卡请求
  // 请求如果携带了Cookie，token到卡服务会导致请求失败
  // 需要注意的是因为没有增加响应拦截器，所以返回结果的res里面会多一层data
  const instance = axios.create()
  return instance.post(url, data, {
    withCredentials: false,
    headers: {
      'Content-Type': 'text/plain;charset=UTF-8',
      'Accept': '*/*',
    },
    ...config,
  })
}

/**
 * 模拟判断是否插卡
 */
function mockHasCard() {
  try {
    return newInstancePost('http://127.0.0.1:8003/HasCard', {})
  }
  catch (error) {
    console.log('卡服务请求失败，返回默认值', error)
    return Promise.resolve(true)
  }
}

/**
 * 模拟读卡
 */
async function mockReadCard(): Promise<CardReadResponse> {
  try {
    const res = await newInstancePost('http://127.0.0.1:8003/ReadCard', { data: {} })
    if (res.data.Err || res.data.Exception) {
      console.error(`卡服务错误：${res.data.Err}${res.data.Exception}，请联系开发人员处理！！`)
      throw new Error(`卡服务错误：${res.data.Err}${res.data.Exception}，请联系开发人员处理！！`)
    }
    else {
      return res.data
    }
  }
  catch (error) {
    console.error('读卡失败，返回模拟数据', error)
    throw error
  }
}

/**
 * 模拟写新卡
 */
function mockWriteNewCard(param: any) {
  try {
    return newInstancePost('http://127.0.0.1:8003/WriteNewCard', param, { timeout: 80000 })
  }
  catch (error) {
    console.error('写新卡失败，返回模拟数据', error)
    throw error
  }
}

/**
 * 模拟写气卡
 */
function mockWriteGasCard(param: any) {
  try {
    return newInstancePost('http://127.0.0.1:8003/WriteGasCard', param, { timeout: 80000 })
  }
  catch (error) {
    console.error('写气卡失败，返回模拟数据', error)
    throw error
  }
}

export async function initCard(formData, user: BusinessUser, operator) {
  let result = null
  try {
    if (await hasCard()) {
      let CardNumber = { cardNumber: null }
      if (!user.f_card_id) {
        CardNumber = await getCardNumber(user)
      }
      else {
        CardNumber.cardNumber = user.f_card_id
      }
      if (CardNumber.cardNumber) {
        // 当卡的别名是泰鹏普通卡的时候卡号和表号一致
        if (user.f_alias && (user.f_alias.includes('SmartCard_TP') || user.f_alias.includes('LiHengSystem'))) {
          formData.cardNumber = user.f_meternumber
        }
        else if (user.f_alias && user.f_alias.includes('LaiDeRF')) {
          formData.cardNumber = user.f_card_id
        }
        else {
          formData.cardNumber = `${CardNumber.cardNumber}`
        }
      }
      else {
        throw new Error('获取卡号失败！请核实气表管理中的卡号重新发卡售气。')
      }
      result = await cardIssuanceGasSales(formData, user, operator)
      // 获取写卡参数
      const cardParams = await getCardParam(user.f_userfiles_id, formData.cardNumber)
      // 组织写卡数据
      let params = {
        factory: user.f_alias,
        kmm: '0', // 2019-12-22 查旭说的。给一个0 卡密码，写卡后返回新密码, 不论补卡还是先开户都没有密码
        kzt: '0', // 卡状态，0开户卡，1用户卡（0新开户，1补卡）
        kh: result.cardnumber, // 卡号
        yhh: user.f_meterid ? user.f_meterid : 0, // 用户号，档案中自己输
        ql: formData.gas, // 预购气量
        csql: '0', // 上次购气量，新开户为0
        ccsql: '0', // 上上次购气量，新开户为0
        cs: '1', // 求购气次数，先开户为1
        bkcs: '0', // 新开户卡，为0
        ljyql: '0', // 累积用气量，有些表要累加原来用气量,新开户卡，为0
        bjql: user.f_police_gas, // 报警气量,t_gasbrand表中
        czsx: user.f_topup_ceil, // 充值上线，t_gasbrand表中
        tzed: user.f_overdr_lines, // 透支额度，t_gasbrand表中
        cssqrq: '0', // 上次售气日期，格式为YYYYMMDD
        meterid: user.f_meternumber, // 表号
        money: formData.money, // 购气金额
        dqdm: user.f_area_code, // 精益工业地区代码
        klx: user.f_coding,
        ljgql: (user.f_write_totalgas - 0) + (formData.gas - 0), // 当前累计购气量
        ljgqje: (user.f_write_totalfee - 0) + (formData.money - 0), // 累计购气金额
      }
      // 天源表发卡售气时，cs默认给0
      if (user.f_alias && user.f_alias.includes('TianYuan')) {
        params.cs = '0'
      }
      params = Object.assign({}, writeCardParams(cardParams), params)
      const cardRes = await writeNewCard(params)
      if (!cardParams || !result || cardRes.Err || cardRes.Exception) {
        // 写卡异常
        // 数据回滚操作
        throw new Error(`卡服务错误：${cardRes.Err}${cardRes.Exception}，请联系开发人员处理！！`)
      }
      else {
        // 写卡成功 更新卡密码
        await updateCardPassword(user, cardRes)
        return result
      }
    }
  }
  catch (error) {
    if (result) {
      // 出错进行回滚
      user.f_userfiles_id = `${user.f_userfiles_id}`
      if (result) {
        await salecardrollback(result.id, user)
      }
    }
    throw error
  }
}

/**
 * 补气开户发卡
 */
export async function offGasAddGas(formData, user: BusinessUser, operator) {
  let result = null
  try {
    if (await hasCard()) {
      const getLastOper = await runLogic<{ ispick: string, lastsell_oper: string }>('sale_getLastOper', { f_userfiles_id: user.f_userfiles_id })

      if (getLastOper.ispick === '已过表') {
        throw new Error('客户已过表,禁止掉气补气！！')
      }
      // 获取卡号
      formData.f_card_id = user.f_card_id
      // 存库
      result = await offGasAddGasApi(formData, user, operator)
      // 获取写卡参数
      const cardParams = await getCardParam(user.f_userfiles_id, user.f_card_id)
      // 组织写卡数据
      let params = {
        factory: user.f_alias, // 气表厂家
        kh: user.f_card_id, // 卡号
        kmm: user.f_card_password, // 卡密码，写卡后返回新密码, 不论补卡还是先开户都没有密码
        bjql: user.f_police_gas, // 报警气量,t_gasbrand表中
        czsx: user.f_topup_ceil, // 充值上线，t_gasbrand表中
        tzed: user.f_overdr_lines, // 透支额度，t_gasbrand表中
        bkcs: user.f_fillcard_times, // 补卡次数，从补卡表里通过表编号获得
        ql: formData.f_pregas, // 预购气量
        cs: user.f_times, // 求购气次数，先开户为1
        money: formData.f_preamount, // 购气金额
        klx: user.f_coding,
        kzt: '1',
        dqdm: user.f_area_code, // 精益工业地区代码
        meterid: user.f_meternumber, // 表号
        meternumber: user.f_meternumber,
        ljgql: (user.f_write_totalgas - 0),
        ljgqje: (user.f_write_totalfee - 0),
      }
      params = Object.assign({}, writeCardParams(cardParams), params)

      params.kzt = getLastOper.ispick === '未过表' && getLastOper.lastsell_oper === '发卡售气' ? '0' : '1'

      const cardRes = await writeNewCard(params)
      if (!cardParams || !result || cardRes.Err || cardRes.Exception) {
        // 写卡异常
        // 数据回滚操作
        throw new Error(`卡服务错误：${cardRes.Err}${cardRes.Exception}，请联系开发人员处理！！`)
      }
    }
    else {
      throw new Error('请检查卡是否插好！！')
    }
    return result
  }
  catch (error) {
    console.error(error)
    if (result) {
      await addGasRoolBack({
        userfile: {
          f_user_id: user.f_user_id,
          f_userfiles_id: user.f_userfiles_id,
          f_card_id: user.f_card_id,
          version: 0,
        },
        fillgas_id: result.id,
      })
    }
    throw error
  }
}

/**
 * 线下写卡
 */
export async function offlineWrite(formData, user: BusinessUser, operator) {
  let result = null
  try {
    if (await hasCard()) {
      result = await post<{ id: number }>('/af-revenue/entity/save/t_sellinggas', {
        id: formData.id,
        f_write_comments: formData.f_comments,
        f_write_operator: operator.f_operator,
        f_operat_type: formData.f_canceltype,
        f_write_card: '已写卡',
        f_write_date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      })
      // 获取写卡参数
      const cardParams = await getCardParam(user.f_userfiles_id, user.f_card_id)
      let params = {
        factory: user.f_alias,
        kh: user.f_card_id,
        cs: user.f_times,
        yhh: user.f_meterid ? user.f_meterid : 0,
        meterid: user.f_meternumber ? user.f_meternumber : 0,
        meternumber: user.f_meternumber ? user.f_meternumber : 0,
        kmm: user.f_card_password,
        ljgql: (user.f_write_totalgas - 0), // 当前累计购气量
        ljgqje: (user.f_write_totalfee - 0), // 累计购气金额
        bjql: user.f_police_gas,
        czsx: user.f_topup_ceil,
        tzed: user.f_overdr_lines,
        bkcs: user.f_fillcard_times,
        dqdm: user.f_area_code,
        klx: user.f_coding,
        ql: formData.gas,
        money: formData.money,
      }
      params = Object.assign({}, writeCardParams(cardParams), params)
      const cardRes = await writeNewCard(params)
      if (!cardParams || !result || cardRes.Err || cardRes.Exception) {
        // 写卡异常
        // 数据回滚操作
        throw new Error(`卡服务错误：${cardRes.Err}${cardRes.Exception}，请联系开发人员处理！！`)
      }
    }
  }
  catch (error) {
    result = await post('/af-revenue/entity/save/t_sellinggas', {
      id: formData.id,
      f_operat_type: formData.f_canceltype,
      f_write_card: '写卡失败',
    })
    throw error
  }
}
/**
 * 补气开户发卡
 */
export async function remanentinitCard(formData, user: BusinessUser, operator) {
  let result = null
  try {
    if (await hasCard()) {
      // 获取卡号
      const CardNumber = await getCardNumber(user)
      formData.f_card_id = CardNumber.cardNumber
      // 存库
      result = await remanent(formData, user, operator)
      // 获取写卡参数
      const cardParams = await getCardParam(user.f_userfiles_id, CardNumber.cardNumber)
      // 组织写卡数据
      let params = {
        factory: user.f_alias,
        kmm: '0', // 2019-12-22 查旭说的。给一个0 卡密码，写卡后返回新密码, 不论补卡还是先开户都没有密码
        kzt: '0', // 卡状态，0开户卡，1用户卡（0新开户，1补卡）
        kh: formData.f_card_id, // 卡号
        yhh: user.f_meterid ? user.f_meterid : 0, // 用户号，档案中自己输
        ql: formData.writeCard_gas, // 预购气量
        csql: '0', // 上次购气量，新开户为0
        ccsql: '0', // 上上次购气量，新开户为0
        cs: '1', // 求购气次数，先开户为1
        ljgql: formData.f_total_gas, // 当前累计购气量
        bkcs: '0', // 新开户卡，为0
        ljyql: '0', // 累积用气量，有些表要累加原来用气量,新开户卡，为0
        bjql: user.f_police_gas, // 报警气量,t_gasbrand表中
        czsx: user.f_topup_ceil, // 充值上线，t_gasbrand表中
        tzed: user.f_overdr_lines, // 透支额度，t_gasbrand表中
        cssqrq: '0', // 上次售气日期，格式为YYYYMMDD
        meterid: user.f_meternumber, // 表号
        money: formData.writeCard_fee, // 购气金额
        dqdm: user.f_area_code, // 精益工业地区代码
        klx: user.f_coding,
      }
      params = Object.assign({}, writeCardParams(cardParams), params)
      const cardRes = await writeNewCard(params)
      if (!cardParams || !result || cardRes.Err || cardRes.Exception) {
        // 写卡异常
        // 数据回滚操作
        throw new Error(`卡服务错误：${cardRes.Err}${cardRes.Exception}，请联系开发人员处理！！`)
      }
    }
    else {
      throw new Error('请检查卡是否插好！！')
    }
    return result
  }
  catch (error) {
    console.error(error)
    if (result) {
      // 出错进行回滚
      user.f_userfiles_id = `${user.f_userfiles_id}`
      if (result) {
        await remanentRollback(result.fillgasid, result.sellid, user)
      }
    }
    throw error
  }
}
/**
 * 卡表收费
 */
export async function sellGas(formData, user: BusinessUser, operator) {
  let result = null
  let cardpost = null
  let cardparam = null
  try {
    if (await hasCard()) {
      if (!(user.f_hascard == null) && !(user.f_share_times == null) && user.f_hascard === '是' && user.f_share_times === '是') {
        useBusinessStore().currentCard.Times = useBusinessStore().currentCard.Times ? useBusinessStore().currentCard.Times : user.f_times
        formData.f_times = user.f_times + 1
      }
      else {
        formData.f_times = useBusinessStore().currentCard.Times + 1
      }
      // 将回写表号或者用户号
      formData.f_meterid = useBusinessStore().currentCard.Yhh ? useBusinessStore().currentCard.Yhh : user.f_meterid
      let cardid = user.f_card_id
      if (user.f_tag) {
        // 截取用户编号后四位
        cardid = formData.f_card_id = user.f_tag + user.f_card_id
      }
      result = await cardCharge(formData, user, operator)
      // 获取写卡参数
      const cardParams = await getCardParam(user.f_userfiles_id, cardid)
      // 组织写卡数据
      let params = {
        factory: user.f_alias, // 气表厂家
        kh: cardid, // 卡号
        yhh: user.f_meterid ? user.f_meterid : 0, // 用户号，档案中自己输
        kmm: user.f_card_password, // 卡密码，写卡后返回新密码, 不论补卡还是先开户都没有密码
        bjql: user.f_police_gas,
        ljgql: (user.f_write_totalgas - 0) + (formData.gas - 0), // 当前累计购气量
        ljgqje: (user.f_write_totalfee - 0) + (formData.money - 0), // 累计购气金额
        czsx: user.f_topup_ceil, // 充值上线，t_gasbrand表中
        tzed: user.f_overdr_lines, // 透支额度，t_gasbrand表中
        bkcs: user.f_fillcard_times, // 补卡次数，从补卡表里通过表编号获得
        ql: formData.gas, // 预购气量
        cs: useBusinessStore().currentCard.Times + 1, // 求购气次数，先开户为1
        money: formData.money, // 购气金额
        dqdm: user.f_area_code, // 精益工业地区代码
        klx: user.f_coding,
        meterid: user.f_meternumber ? user.f_meternumber : 0, // 表号
        kzt: null,
      }
      params = Object.assign({}, writeCardParams(cardParams), params)
      if (!(user.f_hascard == null) && !(user.f_share_times == null) && user.f_meter_type === '物联网表' && user.f_hascard === '是' && user.f_share_times === '是') {
        params.cs = user.f_times + 1
      }
      let cardRes = null
      if (result.last_oper === '补卡') {
        // 此次验证开户之后未过表补卡验证问题
        params.kzt = result.ispick === '未过表' && result.lastsell_oper === '发卡售气' ? '0' : '1'
        cardpost = 'WriteNewCard'
        cardRes = await writeNewCard(params)
      }
      else if (result.last_oper === '换表') {
        params.cs = 1
        cardparam = JSON.stringify(params)
        cardpost = 'WriteNewCard'
        cardRes = await writeNewCard(params)
      }
      else {
        cardparam = JSON.stringify(params)
        cardpost = 'WriteGasCard'
        cardRes = await writeGasCard(params)
      }
      if (!cardParams || !result || cardRes.Err || cardRes.Exception) {
        // 写卡异常
        // 数据回滚操作
        if (result) {
          const data = {
            cardparam,
            cardpost,
            id: result.id,
            userinfo: {
              f_userinfo_id: user.f_userinfo_id,
              version: user.user_version + 1,
              f_balance: user.f_balance,
              userfiles: {
                f_user_id: user.f_user_id,
                f_userfiles_id: user.f_userfiles_id,
                f_total_gas: user.f_total_gas,
                f_write_totalgas: user.f_write_totalgas,
                f_write_totalfee: user.f_write_totalfee,
                f_total_fee: user.f_total_fee,
                f_card_id: user.f_card_id,
                f_times: useBusinessStore().currentCard.Times,
                version: user.version + 1,
                f_tag: null,
              },
            },
          }
          if (user.f_tag) {
            data.userinfo.userfiles.f_tag = user.f_tag
          }
          if (!(user.f_hascard == null) && !(user.f_share_times == null) && user.f_meter_type === '物联网表' && user.f_hascard === '是' && user.f_share_times === '是') {
            data.userinfo.userfiles.f_times = user.f_times
          }
          await cardChargeRollback(data)
        }
        throw new Error(`卡服务错误：${cardRes.Err}${cardRes.Exception}，请联系开发人员处理！！`)
      }
      // 写卡成功 更新卡密码
      await updateCardPassword(user, cardRes)
      return result
    }
    else {
      throw new Error(`请检查卡是否插好！！`)
    }
  }
  catch (error) {
    console.error(error)
    throw error
  }
}

/**
 * 卡表撤销
 */
export async function cancelCard(param: any, user: BusinessUser, cardInfo: CardReadResponse, operator: any) {
  let result = null
  try {
    if (await hasCard()) {
      // 获取写卡参数
      const cardParams = await getCardParam(user.f_userfiles_id, user.f_card_id)
      // 存库
      result = await runLogic<GasRefund>('mobile_cardMeterChargeCancel', {
        model: param,
        user,
        operator,
        cardInfo,
      })
      // 写卡参数
      let params: any = {
        factory: user.f_alias, // 气表厂家
        kh: user.f_card_id, // 卡号
        yhh: cardInfo.Yhh, // 用户号，档案中自己输
        kmm: user.f_card_password, // 卡密码，写卡后返回新密码, 不论补卡还是先开户都没有密码
        klx: user.f_coding,
        bjql: user.f_police_gas, // 报警气量,t_gasbrand表中
        czsx: user.f_topup_ceil, // 充值上线，t_gasbrand表中
        tzed: user.f_overdr_lines, // 透支额度，t_gasbrand表中
        bkcs: user.f_fillcard_times, // 补卡次数，从补卡表里通过表编号获得
        ql: '0', // 预购气量
        ljgql: (user.f_total_gas - 0) - (param.f_pregas - 0), // 当前累计购气量
        totalmoney: (user.f_total_fee - 0) - (param.f_preamount - 0),
        cs: cardInfo.Times - 1, // 求购气次数，先开户为1
        dqdm: user.f_area_code, // 精益工业地区代码
        meterid: user.f_meternumber ? user.f_meternumber : 0, // 表号
        money: '0', // 购气金额
      }
      params = Object.assign({}, writeCardParams(cardParams), params)
      const cardRes = await writeGasCard(params)
      if (!cardParams || !result || cardRes.Err || cardRes.Exception) {
        throw new Error(`卡服务错误：${cardRes.Err}${cardRes.Exception}，请联系开发人员处理！！`)
      }
      else {
        await updateCardPassword(user, cardRes)
        return result
      }
    }
    else {
      throw new Error('请插卡后再操作')
    }
  }
  catch (error) {
    if (result) {
      await rollbackGen({
        correct_id: result.id,
        correct_data: result.record,
      })
    }
    throw error
  }
}

// 转气写卡
export async function moveGasWritCard(cardId: string, f_alias: string, param: MoveGasParam, curUser: BusinessUser, targetUser: BusinessUser) {
  if (await hasCard()) {
    // 读卡
    const cardInfo = await readCard()
    if (!cardInfo) {
      // showFailToast('请插卡后再操作')
      throw new Error('请插卡后再操作')
    }
    else {
      if (cardInfo.Gas > 0 || cardInfo.Money > 0) {
        throw new Error('当前卡号有气量或金额，请先上表之后在进行转气')
      }
      if (!cardId || cardInfo.CardID !== cardId || cardInfo.Factory !== f_alias) {
        // showFailToast('转入用户与当前卡号不匹配，请核实转入用户卡号')
        throw new Error('转入用户与当前卡号不匹配，请核实转入用户卡号')
      }
    }
    let result = null
    try {
      result = await moveGas(param, curUser)
      const cardParams = await getCardParam(targetUser.f_userfiles_id, targetUser.f_card_id)
      // 写卡
      let params: any = {
        factory: targetUser.f_alias, // 气表厂家
        kh: cardInfo.CardID, // 卡号
        yhh: targetUser.f_meterid ? targetUser.f_meterid : 0, // 用户号，档案中自己输
        kmm: targetUser.f_card_password, // 卡密码，写卡后返回新密码, 不论补卡还是先开户都没有密码
        bjql: targetUser.f_police_gas, // 报警气量,t_gasbrand表中
        czsx: targetUser.f_topup_ceil, // 充值上线，t_gasbrand表中
        tzed: targetUser.f_overdr_lines, // 透支额度，t_gasbrand表中
        bkcs: targetUser.f_fillcard_times, // 补卡次数，从补卡表里通过表编号获得
        ql: param.f_move_gas, // 预购气量
        cs: cardInfo.Times + 1, // 求购气次数，先开户为1
        money: param.f_move_fee, // 购气金额
        dqdm: targetUser.f_area_code, // 精益工业地区代码
        klx: targetUser.f_coding,
      }
      // 判断是否是累购表
      params = Object.assign({}, writeCardParams(cardParams), params)
      const cardRes = await writeGasCard(params)
      if (!cardParams || !result || cardRes.Err || cardRes.Exception) {
        throw new Error(`卡服务错误：${cardRes.Err}${cardRes.Exception}，请联系开发人员处理！！`)
      }
      else {
        await updateCardPassword(targetUser, cardRes)
        return result
      }
    }
    catch (error) {
      if (result) {
        const data = {
          f_userfiles_id: curUser.f_userfiles_id, // 转出方
          out_gas: curUser.f_total_gas,
          out_fee: curUser.f_total_fee,
          out_sell_id: result.out_sell_id,
          out_userinfo_id: curUser.f_userinfo_id,
          out_balance: curUser.f_balance,
          out_balance_amount: curUser.f_balance_amount,
          out_deduction_gas: curUser.f_deduction_gas,
          userfilesid: targetUser.f_userfiles_id, // 转入方
          total_gas: targetUser.f_total_gas,
          total_fee: targetUser.f_total_fee,
          sell_id: result.id,
          f_userinfo_id: targetUser.f_userinfo_id,
          f_balance: targetUser.f_balance,
          f_balance_amount: targetUser.f_balance_amount,
          f_deduction_gas: targetUser.f_deduction_gas,
        }
        await moveGasRollback(data)
      }
      throw error
    }
  }
  else {
    throw new Error('请插卡后再操作')
  }
}

/**
 * 补卡
 */
export async function replaceCard(param: FillCardParam, user: BusinessUser) {
  if (await hasCard()) {
    let result = null
    try {
      let cardId = user.f_card_id
      // 如果是带卡物联网表
      if (!(user.f_hascard == null) && !(user.f_share_times == null) && user.f_meter_type === '物联网表' && user.f_hascard === '是' && user.f_share_times === '是' && (user.f_card_id == null || user.f_card_id === '')) {
        // 根据API实际参数格式，适当调整
        const CardNumber = await getCardNumber(user)
        console.log('获取卡号、、', CardNumber)
        if (CardNumber.cardNumber) {
          cardId = CardNumber.cardNumber
          user.f_card_id = CardNumber.cardNumber
        }
        else {
          throw new Error('获取卡号失败！请核实参数管理中的卡号重新发卡售气。')
        }
      }
      if (user.f_tag && user.f_tag !== 'NULL' && user.f_tag !== 'null') {
        // 截取用户编号后四位
        cardId = user.f_tag + param.f_card_id
      }
      param.f_card_id = cardId
      // 补卡记录存库
      result = await fillCard(param, user)
      const cardParams = await getCardParam(user.f_userfiles_id, user.f_card_id)
      // 写卡
      let params: any = {
        factory: user.f_alias,
        kh: cardId, // 卡号
        kmm: user.f_card_password, // 卡密码，写卡后返回新密码, 不论补卡还是先开户都没有密码
        bjql: user.f_police_gas, // 报警气量,t_gasbrand表中
        czsx: user.f_topup_ceil, // 充值上线，t_gasbrand表中
        tzed: user.f_overdr_lines, // 透支额度，t_gasbrand表中
        ljgql: (user.f_total_gas - 0), // 当前累计购气量
        ljgqje: (user.f_total_fee - 0), // 累计购气金额
        kzt: '1', // 卡状态，0开户卡，1用户卡（0新开户，1补卡）
        bkcs: user.f_fillcard_times + 1, // 补卡次数，从补卡表里通过表编号获得
        cs: param.f_times, // 求购气次数，先开户为1
        yhh: user.f_meterid ? user.f_meterid : 0, // 用户号
        meterid: user.f_meternumber, // 表号
        meternumber: user.f_meternumber,
        dqdm: user.f_area_code, // 精益工业地区代码
        money: 0, // 购气金额
        ql: 0,
        klx: user.f_coding,
      }
      // 判断是否是累购表
      if (user.f_support_purchase === '是') {
        user.ljgql = (user.f_write_totalgas - 0)
        user.ljgqje = (user.f_write_totalfee - 0)
      }
      // 判断是否接表，如果未接表（次数减一）已过表（次数为档案里面的次数） 针对不补气的
      if (param.f_ispick_table) {
        params.cs = (param.f_ispick_table === '否' ? user.f_times - 1 : user.f_times)
      }
      params = Object.assign({}, writeCardParams(cardParams), params)
      const cardRes = await writeNewCard(params)
      if (!cardParams || !result || cardRes.Err || cardRes.Exception) {
        throw new Error(`卡服务错误：${cardRes.Err}${cardRes.Exception}，请联系开发人员处理！！`)
      }
      else {
        await updateCardPassword(user, cardRes)
        return result
      }
    }
    catch (e) {
      if (result) {
        repairCardRoolBack({
          id: result.id,
          f_card_id: user.f_card_id,
          f_user_id: user.f_user_id,
          f_tag: user.f_tag,
        })
      }
      throw e
    }
  }
  else {
    throw new Error('请插卡后再操作')
  }
}

/**
 * 处理写卡参数
 * @param cardParams 卡片参数
 * @returns 处理后的写卡参数
 */
export function writeCardParams(cardParams: any): WriteCardParams {
  const result: WriteCardParams = {
    csql: cardParams.sc,
    lastmoney: cardParams.scje,
    ccsql: cardParams.ssc,
    lastlastmoney: cardParams.sscje,
    cccsql: cardParams.sssc ? cardParams.sssc : null,
    lastlastlastmoney: cardParams.ssscje ? cardParams.ssscje : null,
    ljgql: cardParams.f_pregas,
    ljyql: cardParams.f_pregasall,
    yhh: cardParams.f_meterid,
    sxrq: cardParams.f_perform_date,
    totalmoney: cardParams.f_collection,
    stairversion: cardParams.f_stairversion,
    pricedate: cardParams.pricedate,
    pricetype: cardParams.pricetype,
  }

  // 对阶梯进行处理
  if (cardParams.stairprice.includes(';') && cardParams.stairamount.includes(';')) {
    const gases = cardParams.stairamount.split(';')
    const prices = cardParams.stairprice.split(';')
    if (gases.length >= 2) {
      result.stairgas1 = gases[0] !== '' ? gases[0] : 0
      result.stairgas2 = gases[1] !== '' ? gases[1] : 0
      result.stairgas3 = '99999999'
    }
    if (prices.length >= 2) {
      result.stairprice1 = prices[0] !== '' ? prices[0] : 0
      result.stairprice2 = prices[1] !== '' ? prices[1] : 0
      result.stairprice3 = prices[2] !== '' ? prices[2] : 0
    }
  }
  else {
    // 从固定气价的表换金额表的时候，气量随便写，金额用之前的固定单价
    result.stairgas1 = '77777777'
    result.stairgas2 = '88888888'
    result.stairgas3 = '99999999'

    result.stairprice1 = cardParams.stairprice
    result.stairprice2 = cardParams.stairprice
    result.stairprice3 = cardParams.stairprice
  }
  if (cardParams.cnpricedatestart && (cardParams.isHeat === '是')) {
    // 组织采暖参数
    let cnpricedatestart = cardParams.cnpricedatestart
    const tmp = `${new Date().getFullYear()}-${cnpricedatestart}`
    const standardDate = new Date(tmp)
    const end = new Date(standardDate.setMonth(standardDate.getMonth() + cardParams.f_heat_cycle))
    let endDate = dayjs(end).format('YYYY-MM-DD HH:mm:ss')
    endDate = endDate.replace(/-/g, '')
    cnpricedatestart = tmp.replace(/-/g, '').substr(2, tmp.length)
    const cnpricedateend = endDate.substr(2, 7)

    result.cnpricedateend = cnpricedateend
    result.cnpricedatestart = cnpricedatestart
    if (cardParams.heatamount.includes(';')) {
      const heatamounts = cardParams.heatamount.split(';')
      if (heatamounts.length >= 2) {
        result.cnstairgas1 = heatamounts[0] !== '' ? Number.parseInt(heatamounts[0]) + Number.parseInt(result.stairgas1 as string) : result.stairgas1
        result.cnstairgas2 = heatamounts[1] !== '' ? Number.parseInt(heatamounts[1]) + Number.parseInt(result.stairgas2 as string) : result.stairgas2
        result.cnstairgas3 = heatamounts[2] !== '' ? Number.parseInt(heatamounts[2]) + Number.parseInt(result.stairgas3 as string) : result.stairgas3
      }
    }
  }
  return result
}
