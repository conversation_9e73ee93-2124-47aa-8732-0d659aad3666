import type {
  MeterRecord,
  OtherChargeRecord,
  PaymentRecord,
  RecordQueryParams,
  ReplacementRecord,
  SafetyRecord,
  TransactionRecord,
  TransferRecord,
} from '@/views/userRecords/types'
import { get } from '@af-mobile-client-vue3/services/restTools'

/**
 * 获取用户缴费记录
 */
export function getPaymentRecords(params: RecordQueryParams): Promise<PaymentRecord[]> {
  return get('/api/user/payment-records', { params })
}

/**
 * 获取用户抄表记录
 */
export function getMeterRecords(params: RecordQueryParams): Promise<MeterRecord[]> {
  return get('/api/user/meter-records', { params })
}

/**
 * 获取用户过户记录
 */
export function getTransferRecords(params: RecordQueryParams): Promise<TransferRecord[]> {
  return get('/api/user/transfer-records', { params })
}

/**
 * 获取用户安检记录
 */
export function getSafetyRecords(params: RecordQueryParams): Promise<SafetyRecord[]> {
  return get('/api/user/safety-records', { params })
}

/**
 * 获取用户换表记录
 */
export function getReplacementRecords(params: RecordQueryParams): Promise<ReplacementRecord[]> {
  return get('/api/user/replacement-records', { params })
}

/**
 * 获取用户其他收费记录
 */
export function getOtherChargeRecords(params: RecordQueryParams): Promise<OtherChargeRecord[]> {
  return get('/api/user/other-charge-records', { params })
}

/**
 * 获取用户流水记录
 */
export function getTransactionRecords(params: RecordQueryParams): Promise<TransactionRecord[]> {
  return get('/api/user/transaction-records', { params })
}
