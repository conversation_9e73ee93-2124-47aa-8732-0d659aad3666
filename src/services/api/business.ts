import type { BusinessUser, LastCardChargeRecord, userSearchModel } from '@/components/OnlineRevenue/types'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import { post } from '@af-mobile-client-vue3/services/restTools'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'

/**
 * 获取用户信息 列表
 */
export function getUserInfo(params: userSearchModel) {
  return post('/af-revenue/logic/mobile_getUser', params)
}

/**
 * 查询近期交易记录
 */
export function getRecentTransactions(params: {
  f_userinfo_id?: string // 用户ID（必需）
  f_userinfo_code?: string // 用户ID（必需）
  startDate?: string // 开始日期（可选） yyyy-mm-dd
  endDate?: string // 结束日期（可选） yyyy-mm-dd
  pageNo?: number // 页码（可选，默认1）
  pageSize?: number // 每页记录数（可选，默认10）
}) {
  return post('/af-revenue/logic/mobile_transactionsList', params)
}

/**
 * 获取用户可用的操作权限
 */
export function getUseOperations(params: {
  f_userinfo_id?: string // 用户ID（必需）
  f_userinfo_code?: string // 用户ID（必需）
  f_userfiles_id?: string // 用户ID（必需）
  cardInfo?: object
}) {
  return post('/af-revenue/logic/mobile_getOperBtns', params)
}

/**
 * 定义阶梯气价计算返回结果接口
 */
export interface ChargePrice {
  f_gas: number
  f_price: number
  f_money: number
  [key: string]: any
}

export interface CommonCalResponse {
  money: string // 金额
  gas: string // 气量
  calArray: any[] // 计算数组
  crossStepCount: number // 跨阶梯次数
  calText: string // 计算公式文本
  sumAmount: number // 总金额
  gasPrice: string // 气价
  chargePrice: ChargePrice[] // 阶梯气价明细

  // 总量
  f_stair1ceiling?: number
  f_stair2ceiling?: number
  f_stair3ceiling?: number
  f_stair4ceiling?: number

  // 本次划价用量
  f_stair1amount?: number
  f_stair2amount?: number
  f_stair3amount?: number
  f_stair4amount?: number

  // 剩余量
  f_stair1surplus?: number
  f_stair2surplus?: number
  f_stair3surplus?: number
  f_stair4surplus?: number

  // 单价
  f_stair1price?: number
  f_stair2price?: number
  f_stair3price?: number
  f_stair4price?: number

  // 周期
  stairBeginDate?: string
  stairEndDate?: string

  [key: string]: any // 其他可能的字段
}

/**
 * 划价
 */
export function commonCal(params: {
  f_userinfo_id?: string // 用户ID（必需）
  f_card_id?: string // 用户ID（必需）
  f_meternumber?: string // 用户ID（必需）
  f_userfiles_id?: string // 用户ID（必需）
  gas?: string | number
  money?: string | number
}): Promise<CommonCalResponse> {
  return post<CommonCalResponse>('/af-revenue/logic/mobile_CommonCal', params)
}

/**
 *  获取用户最近各种业务的时间
 *
 */
export function getRecentBusinessTime<T>(params: {
  f_userinfo_id: string // 用户ID（必需）
}) {
  return post<T>('/af-revenue/logic/mobile_getRecentBusinessTime', params)
}

/**
 * 获取卡表收费最后一条收费记录
 */
export function getLastCardCharge(params: {
  f_userinfo_id: string // 用户ID（必需）
}) {
  return post<LastCardChargeRecord | null>('/af-revenue/logic/mobile_getLastCardCharge', params)
}
/**
 * 获取未写卡的数据
 */
export function getNoWriteCardRecord(params: {
  f_userinfo_id: string // 用户ID（必需）
}) {
  return post<LastCardChargeRecord[]>('/af-revenue/logic/mobile_getNoWriteCardRecord', params)
}

/**
 * 获取开卡费
 */
export function getOpenFee(params: {
  f_user_type?: string
}) {
  return post('/af-revenue/logic/mobile_getOpenFee', params)
}
/**
 * 发卡售气
 */
export function cardIssuanceGasSales(model, user, operator) {
  return post('/af-revenue/logic/mobile_CardIssuanceGasSales', { model, user, operator })
}
/**
 *  换表补气购气
 */
export function remanent(model, user, operator) {
  return post('/af-revenue/logic/mobile_remanent', { model, user, operator })
}

/**
 * 掉气补气
 */

export function offGasAddGasApi(data, user, operator) {
  return runLogic('mobile_offGasAddGas', { data, user, operator })
}
// 发卡售气回滚
export function salecardrollback(id, user) {
  return runLogic('salecardrollback', { id, row: user })
}
// 卡表收费回滚
export function cardChargeRollback(data) {
  return runLogic('cardChargeRollback', data)
}

export function repairCardRoolBack(data: {
  id: number
  f_card_id: string
  f_user_id: string
  f_tag?: string
}) {
  return runLogic('repairCardRoolBack', data)
}

// 掉气补气回滚
export function addGasRoolBack(data) {
  return runLogic('addGasRoolBack', data)
}

// 卡表补气回滚
export function remanentRollback(fillgasid, sellid, user) {
  return runLogic('remanentRollback', { fillgasid, sellid, row: user })
}

// 卡表撤销回滚
export function rollbackGen(result) {
  return runLogic('mobile_cancelRollBack', result)
}

// 转气回滚
export function moveGasRollback(data) {
  return runLogic('moveGasRollback', data)
}

/**
 * 物联网表收费
 */
export function iotMeterCharge(model, user, operator) {
  return post('/af-revenue/logic/mobile_iotMeterCharge', { model, user, operator })
}

/**
 * 卡表收费
 */
export function cardCharge(model, user, operator) {
  return post('/af-revenue/logic/mobile_cardCharge', { model, user, operator })
}

/**
 * 停启用
 */
export function tableStatusChange(model, user, operator) {
  return post('/af-revenue/logic/mobile_tableStatusChange', { model, user, operator })
}

/**
 * 机表收费
 */
export function mechanicalCharge(model, user, floor, operator) {
  return post('/af-revenue/logic/mobile_mechanicalCharge', { model, user, floor, operator })
}

/**
 * 其他收费
 */
export function otherCharge(model, user, operator) {
  return post('/af-revenue/logic/mobile_otherCharge', { model, user, operator })
}

/**
 * 获取限购接口
 */
export function businessGetLimitGas(userinfo_id: string, user_id: string, stairprice_id: string) {
  return post('/af-revenue/logic/sale_getLimitGas', {
    f_userinfo_id: userinfo_id,
    f_user_id: user_id,
    f_stairprice_id: stairprice_id,
  })
}

/**
 * 过户
 */
export function transferSave(param, user) {
  const currUser = useUserStore().getLogin().f
  return runLogic('mobile_transferSave', {
    param,
    user,
    operator: {
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
    },
  })
}

/**
 * 转气
 */
export function moveGas(param, user) {
  const currUser = useUserStore().getLogin().f
  return runLogic<{ id: number }>('mobile_movegas', {
    param,
    user,
    operator: {
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
    },
  })
}

/**
 * 获取卡号
 */
export function getCardNumber(user: BusinessUser) {
  return runLogic<{ cardNumber: string }>('mobile_getCardNumber', user)
}

/**
 * 补卡
 */
export function fillCard(param, user) {
  const currUser = useUserStore().getLogin().f
  return runLogic<{ id: number }>('mobile_fillCard', {
    param,
    user,
    operator: {
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
    },
  })
}

/**
 * 补气
 */
export function fillGas(param, user) {
  const currUser = useUserStore().getLogin().f
  return runLogic('mobile_fillGas', {
    param,
    user,
    operator: {
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
    },
  })
}

/**
 * 超用收费
 */
export function overUsage(param, user) {
  const currUser = useUserStore().getLogin().f
  return runLogic<{ value: number }>('mobile_overUsageCharge', {
    param,
    user,
    operator: {
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
    },
  })
}

/**
 * 获取写卡参数
 */
export function getCardParam(f_userfiles_id, f_card_id) {
  return runLogic('stairCard', { f_userfiles_id, f_card_id })
}

/**
 * 更新卡密码
 * @param user
 * @param cardRes
 */
export function updateCardPassword(user, cardRes) {
  return runLogic('updateCardPassword', {
    f_userfiles_id: user.f_userfiles_id,
    f_card_password: cardRes.Kmm,
    // f_card_password: cardRes.data.Kmm,
  })
}

/**
 * 获取抄表册
 */
export function getMeterBookList() {
  return post('/af-revenue/logic/getMeterBookName', {})
}

/**
 * 在线抄表查询用户
 */
export function onlineMeteReadingSearchUser(params) {
  return post('/af-revenue/logic/mobile_onlineMeteReadingSearchUser', params)
}

/**
 * 机表收费
 */
export function meteReadingHandSubmit(params) {
  return post('/af-revenue/logic/mobile_meteReadingHandSubmit', params)
}

let resOptions = []
export async function getUserOrgList() {
  const currUser = useUserStore().getLogin().f
  const result = await post('/af-system/search', {
    source: `tool.getFullTree(this.getRights().where(row.getType()==$organization$))`,
    userid: currUser.resources.id,
  })
  await dealData(result[0])
  return resOptions
}

async function dealData(resObj: any) {
  resOptions = []
  findById(resObj)
}
// 树形结构变成 list
function findById(val: any) {
  if (val) {
    treeToList(val, '')
  }
}

// 树形结构变成 list
function treeToList(val: any, parentName: any) {
  for (const value of val.children) {
    ergodicList(value, parentName)
  }
}

// 找到根节点
function ergodicList(val: any, parentName: any) {
  if (val.children && val.children.length > 0) {
    parentName = parentName + val.name
    treeToList(val, parentName)
  }
  else {
    resOptions.push(val.id)
  }
}
