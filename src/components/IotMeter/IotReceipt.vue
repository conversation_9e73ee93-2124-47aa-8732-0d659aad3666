<script setup lang="ts">
const props = defineProps({
  userInfo: {
    type: Object,
    required: true,
  },
  companyName: {
    type: String,
    default: '城市燃气服务有限公司',
  },
  servicePhone: {
    type: String,
    default: '************',
  },
  transactionId: {
    type: String,
    required: true,
  },
  datetime: {
    type: String,
    required: true,
  },
  amount: {
    type: String,
    required: true,
  },
  gasAmount: {
    type: String,
    required: true,
  },
  receivedAmount: {
    type: String,
    required: true,
  },
  balance: {
    type: String,
    default: '0',
  },
  previousBalance: {
    type: String,
    default: '0',
  },
  paymentMethod: {
    type: String,
    default: '现金缴费',
  },
  remarks: {
    type: String,
    default: '',
  },
  operator: {
    type: String,
    default: '营业员',
  },
})
</script>

<template>
  <div class="iot-receipt">
    <!-- 收据标题 -->
    <div class="iot-receipt__header">
      <h2 class="iot-receipt__title">
        物联网表充值电子凭证
      </h2>
      <p class="iot-receipt__company">
        {{ companyName }}
      </p>
    </div>

    <!-- 收据信息 -->
    <div class="iot-receipt__content">
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">交易单号:</span>
        <span class="iot-receipt__value">{{ transactionId }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">用户姓名:</span>
        <span class="iot-receipt__value">{{ props.userInfo.f_user_name }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">用户编号:</span>
        <span class="iot-receipt__value">{{ props.userInfo.f_userinfo_code }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">表号:</span>
        <span class="iot-receipt__value">{{ props.userInfo.f_meternumber }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">上期余额:</span>
        <span class="iot-receipt__value">¥{{ previousBalance }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">购气量:</span>
        <span class="iot-receipt__value">{{ gasAmount }} m³</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">购气金额:</span>
        <span class="iot-receipt__value iot-receipt__value--highlight">¥{{ amount }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">支付方式:</span>
        <span class="iot-receipt__value">{{ paymentMethod }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">实收金额:</span>
        <span class="iot-receipt__value">¥{{ receivedAmount }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">本期余额:</span>
        <span class="iot-receipt__value">¥{{ balance }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">备注信息:</span>
        <span class="iot-receipt__value">{{ remarks }}</span>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="iot-receipt__footer">
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">操作员:</span>
        <span class="iot-receipt__value">{{ operator }}</span>
      </div>
      <div class="iot-receipt__row">
        <span class="iot-receipt__label">交易时间:</span>
        <span class="iot-receipt__value">{{ datetime }}</span>
      </div>
    </div>

    <!-- 二维码 -->
    <div class="iot-receipt__qrcode">
      <van-icon name="qr" size="24" />
    </div>

    <!-- 服务信息 -->
    <p class="iot-receipt__service">
      感谢您使用我们的服务，如有疑问请拨打客服热线<br>
      {{ servicePhone }}
    </p>
  </div>
</template>

<style lang="less" scoped>
.iot-receipt {
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;

  &__header {
    text-align: center;
    margin-bottom: 16px;
  }

  &__title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0 0 4px;
  }

  &__company {
    font-size: 14px;
    color: #666;
    margin: 0;
  }

  &__content {
    border-top: 1px dashed #ddd;
    border-bottom: 1px dashed #ddd;
    padding: 16px 0;
    margin-bottom: 16px;
  }

  &__row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    color: #666;
  }

  &__value {
    color: #333;
    font-weight: 400;

    &--highlight {
      font-weight: 500;
    }
  }

  &__footer {
    margin-bottom: 16px;
  }

  &__qrcode {
    width: 96px;
    height: 96px;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__service {
    text-align: center;
    font-size: 12px;
    color: #999;
    line-height: 1.5;
    margin: 0;
  }
}
</style>
