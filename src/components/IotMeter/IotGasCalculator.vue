<script setup lang="ts">
import useBusinessStore from '@/stores/modules/business'
import { computed, ref, watch } from 'vue'
import { FormField } from '../common'

const props = defineProps({
  minAmount: {
    type: Number,
    default: 0,
  },
  maxAmount: {
    type: Number,
    default: 99999999,
  },
  minGasAmount: {
    type: Number,
    default: 0,
  },
  maxGasAmount: {
    type: Number,
    default: 99999999,
  },
  gas: {
    type: String,
    default: '',
  },
  limitGas: {
    type: Boolean,
    default: false,
  },
  limitMoney: {
    type: Boolean,
    default: false,
  },
  amount: {
    type: String,
    default: '',
  },
  limitReason: {
    type: String,
    default: '',
  },
  allLimit: {
    type: Number,
    default: null,
  },
})

const emit = defineEmits([
  'calculateByGas',
  'calculateByAmount',
])

const gasAmount = ref(props.gas)
const payAmount = ref(props.amount)
const animationClass = ref('')

const userInfo = useBusinessStore().currentUser

const isGasCollection = computed(() => userInfo.f_collection_type === '按气量')

// 计算金额，根据气量
function calculateAmountFromGas() {
  if (!gasAmount.value)
    return

  let gas = Number(gasAmount.value)

  // 验证气量限制
  if (gas < props.minGasAmount) {
    gas = props.minGasAmount
    gasAmount.value = gas.toString()
  }
  else if (gas > props.maxGasAmount) {
    gas = props.maxGasAmount
    gasAmount.value = gas.toString()
  }

  // 重置动画类，然后设置为向右旋转
  animationClass.value = ''
  setTimeout(() => {
    animationClass.value = 'rotate-right'
  }, 10)

  // 触发父组件的气量计算业务逻辑
  emit('calculateByGas', gas)
}

// 计算气量，根据金额
function calculateGasFromAmount() {
  if (!payAmount.value)
    return

  let amount = Number(payAmount.value)

  // 验证金额限制
  if (amount < props.minAmount) {
    amount = props.minAmount
    payAmount.value = amount.toString()
  }
  else if (amount > props.maxAmount) {
    amount = props.maxAmount
    payAmount.value = amount.toString()
  }

  // 重置动画类，然后设置为向左旋转
  animationClass.value = ''
  setTimeout(() => {
    animationClass.value = 'rotate-left'
  }, 10)

  // 触发父组件的金额计算业务逻辑
  emit('calculateByAmount', amount)
}

// 监听props变化
watch(() => props.gas, (newVal) => {
  gasAmount.value = newVal
}, { immediate: true })

watch(() => props.amount, (newVal) => {
  payAmount.value = newVal
}, { immediate: true })
</script>

<template>
  <div class="iot-gas-calculator">
    <div class="iot-gas-calculator__fields">
      <div class="iot-gas-calculator__field" :class="{ 'field-active': isGasCollection, 'field-readonly': !isGasCollection }">
        <FormField label="购气量 (m³)">
          <van-field
            v-model="gasAmount"
            type="number"
            :min="minGasAmount"
            :max="maxGasAmount"
            input-align="right"
            :placeholder="isGasCollection ? '请输入' : ''"
            :readonly="!isGasCollection"
            :class="{ 'readonly-field': !isGasCollection, 'active-field': isGasCollection }"
            @change="isGasCollection ? calculateAmountFromGas() : null"
          >
            <template #right-icon>
              <span class="iot-gas-calculator__unit">m³</span>
            </template>
          </van-field>
        </FormField>
      </div>

      <div class="iot-gas-calculator__exchange">
        <i class="i-fa-solid-sync-alt" :class="animationClass" />
      </div>

      <div class="iot-gas-calculator__field" :class="{ 'field-active': !isGasCollection, 'field-readonly': isGasCollection }">
        <FormField label="购气金额 (元)">
          <van-field
            v-model="payAmount"
            type="number"
            :min="minAmount"
            :max="maxAmount"
            :step="1"
            input-align="right"
            :placeholder="!isGasCollection ? '请输入' : ''"
            :readonly="isGasCollection"
            :class="{ 'readonly-field': isGasCollection, 'active-field': !isGasCollection }"
            @change="!isGasCollection ? calculateGasFromAmount() : null"
          >
            <template #left-icon>
              <span class="iot-gas-calculator__unit iot-gas-calculator__unit--left">¥</span>
            </template>
          </van-field>
        </FormField>
      </div>
    </div>

    <p v-if="limitGas" class="iot-gas-calculator__hint">
      {{ allLimit ? `本月规模量：${allLimit}m³。本月可购量：${maxGasAmount}m³。限购原因：${limitReason}` : `单次购气范围：${minGasAmount}-${maxGasAmount}m³` }}
    </p>
    <p v-if="limitMoney" class="iot-gas-calculator__hint">
      {{ allLimit ? `本月规模量：${allLimit}。本月可购量：${maxAmount}元。限购原因：${limitReason}` : `单次购气范围：${minAmount}-${maxAmount}元` }}
    </p>
    <p v-if="!limitGas && !limitMoney" class="iot-gas-calculator__hint">
      单次购气范围：{{ minGasAmount }}-{{ maxGasAmount }}m³，单次购气金额范围：{{ minAmount }}-{{ maxAmount }}元，
      {{ isGasCollection ? '输入购气量，系统自动计算金额' : '输入购气金额，系统自动计算气量' }}
    </p>
  </div>
</template>

<style lang="less" scoped>
.iot-gas-calculator {
  &__fields {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__field {
    flex: 1;
    transition: all 0.3s ease;

    // 活跃字段样式
    &.field-active {
      transform: scale(1.02);

      :deep(.van-cell) {
        border: 2px solid var(--van-primary-color);
        border-radius: 8px;
        box-shadow: 0 0 0 3px rgba(25, 137, 250, 0.1);
        background: #fafbff;
      }

      :deep(.van-field__label) {
        color: var(--van-primary-color);
        font-weight: 600;
      }
    }

    // 只读字段样式
    &.field-readonly {
      :deep(.van-cell) {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
      }

      :deep(.van-field__control) {
        color: #6c757d;
      }

      :deep(.van-field__label) {
        color: #6c757d;
      }
    }
  }

  // 输入框样式
  :deep(.active-field) {
    .van-field__control {
      font-weight: 600;
      color: #1a1a1a;
    }
  }

  :deep(.readonly-field) {
    .van-field__control {
      background: transparent;
      color: #6c757d !important;
      cursor: not-allowed;
    }
  }

  &__exchange {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 8px 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }

    i {
      font-size: 18px;
      color: var(--van-primary-color);
    }

    .rotate-right {
      animation: rotateRightAnimation 0.5s ease forwards;
    }

    .rotate-left {
      animation: rotateLeftAnimation 0.5s ease forwards;
    }
  }

  &__unit {
    color: #666;
    font-size: 14px;

    &--left {
      margin-right: 4px;
    }
  }

  &__hint {
    margin-top: 12px;
    padding: 8px 12px;
    font-size: 12px;
    color: #666;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid var(--van-primary-color);
  }
}

@keyframes rotateRightAnimation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(180deg);
  }
}

@keyframes rotateLeftAnimation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-180deg);
  }
}
</style>
