<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByNameAsync, runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { showConfirmDialog, showDialog, showFailToast, showToast } from 'vant'
import { computed, onMounted, reactive, ref } from 'vue'
import { businessGetLimitGas, commonCal, iotMeterCharge } from '@/services/api/business'
import useBusinessStore from '@/stores/modules/business'
import { ChargePrintSelectorAndRemarks, CodePayment, FileUploader, GridFileUploader, InfoCard, PaymentMethodSelectorCard, ReceiptModal } from '../common'
import IotGasCalculator from './IotGasCalculator.vue'
import IotPaymentInfo from './IotPaymentInfo.vue'

interface FormData {
  payment: string
  gas: string
  money: string
  totalCost: string
  balance: string
  collection: string
  print: string
  serial_number: string
  serial_id: string
  comments: string
  fileList: any[]
  // 添加原始业务数据字段
  dyMoney: string
  allDue_fee: number
  curBalance: string // 本期结余
  voucherNumber: string // 凭证号
}

interface ReceiptData {
  transactionId: string
  datetime: string
}

// 定义配置选项接口
interface ConfigOptions {
  hasPrint: boolean
  floor: boolean
  printType: string
  allowedMethods: string[] | null
  payment: string
  hasArrearsChange: boolean
  calculatePreByCollection: false
  printLogic: string
  fileTypes?: {
    userType: string
    picMinNum: number
    description?: string
  }[]
}

const emit = defineEmits(['closeOperation', 'complete'])

// 定义内部config配置
const config = reactive<ConfigOptions>({
  hasPrint: false,
  floor: true,
  printType: '普通收据',
  allowedMethods: null,
  payment: '现金缴费',
  hasArrearsChange: false,
  calculatePreByCollection: false,
  printLogic: '',
  fileTypes: [],
})

// 子组件的响应组件引用
const printRef = ref()

// 表单数据
const formData = reactive<FormData>({
  payment: '现金缴费',
  gas: '',
  money: '',
  totalCost: '',
  balance: '0',
  collection: null,
  print: '',
  serial_number: '',
  serial_id: '',
  comments: '',
  fileList: [],
  dyMoney: '',
  allDue_fee: 0,
  curBalance: '0',
  voucherNumber: '',
})

// 初始化状态
const loading = ref(true)

// 限购相关状态
const hasLimit = ref(false)
const maxGas = ref(99999999)
const maxMoney = ref(99999999)
const allLimit = ref(null)
const limitReason = ref('')
const limitGas = ref(false)
const limitMoney = ref(false)
const arrears_fee = ref(0)
const owes = ref([])
const dyMoney = ref('0')
const gasPrice = ref('')
// 当前用户
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
// 收款验证状态
const paymentValid = ref(false)

const showQrCodePayment = ref(false)
const receiptData = ref<ReceiptData>({
  transactionId: '',
  datetime: '',
})
// 文件上传相关数据
const fileList = ref<FileItem[]>([])
const gridFileUploaderRef = ref()
// 计算公式显示
const calculateDetail = ref('')
// 其他状态
const clickConfirm = ref(false)

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// 输入气量，换算金额
async function calculateByGas(gas: number) {
  try {
    if (gas && gas > 0) {
      // 验证最少购买1方气
      if (gas < 1) {
        showFailToast('最少购买1方气')
        formData.gas = ''
        formData.money = ''
        paymentValid.value = false
        return
      }

      // 验证气量限制
      if (gas > Number(maxGas.value)) {
        showFailToast(`购气量超过限制: ${maxGas.value}m³，请重新输入`)
        cleanGasAndMoney()
        paymentValid.value = false
        return
      }
      if (userInfo.value.f_isdecimal === '是') {
        formData.gas = gas.toFixed(userInfo.value.f_gas_decimal || 4)
      }
      else {
        formData.gas = gas.toFixed(0)
      }
      // 调用接口获取计算结果
      const getAmount = await commonCal({
        f_userinfo_id: userInfo.value.f_userinfo_id,
        f_card_id: userInfo.value.f_card_id,
        f_meternumber: userInfo.value.f_meternumber,
        f_userfiles_id: userInfo.value.f_userfiles_id,
        gas,
      })
      formData.money = getAmount.money
      // 验证金额限制
      if (Number(formData.money) > Number(maxMoney.value)) {
        showFailToast(`购气金额超过限制: ${maxMoney.value}元，请重新输入`)
        cleanGasAndMoney()
        paymentValid.value = false
        return
      }
      formData.totalCost = (Number(userInfo.value.f_user_balance) > Number(getAmount.money)) ? '0' : (Number(getAmount.money) - Number(userInfo.value.f_user_balance)).toFixed(userInfo.value.f_fee_decimal || 4)
      formData.totalCost = (Number(formData.totalCost) + Number(arrears_fee.value)).toFixed(userInfo.value.f_fee_decimal || 4)
      if (config.floor) {
        formData.totalCost = Math.ceil(Number(formData.totalCost)).toString()
      }
      formData.collection = formData.totalCost
      // 阶梯价格展示
      calculateDetail.value = getAmount.calText
      // 跨阶梯提示
      if (getAmount.crossStepCount > 1) {
        showToast('本次购气已跨阶梯')
      }
      paymentValid.value = true
      // 计算本期结余
      calculateCurrentBalance()
    }
    else {
      formData.gas = ''
      paymentValid.value = false
    }
  }
  catch (error) {
    console.error('气量计算失败', error)
    showFailToast('气量计算失败')
  }
}

// 输入金额，换算气量
async function calculateByAmount(amount: number) {
  try {
    if (amount && amount > 0) {
      // 验证金额限制
      if (Number(amount) > Number(maxMoney.value)) {
        showFailToast(`购气金额超过限制: ${maxMoney.value}元，请重新输入`)
        cleanGasAndMoney()
        paymentValid.value = false
        return
      }
      if (userInfo.value.f_isdecimal === '是') {
        formData.money = amount.toFixed(userInfo.value.f_gas_decimal || 4)
      }
      else {
        formData.money = amount.toFixed(0)
      }
      // 调用接口获取计算结果
      const getGas = await commonCal({
        f_userinfo_id: userInfo.value.f_userinfo_id,
        f_card_id: userInfo.value.f_card_id,
        f_meternumber: userInfo.value.f_meternumber,
        f_userfiles_id: userInfo.value.f_userfiles_id,
        money: amount,
      })
      if (getGas.gas) {
        if (userInfo.value.f_isdecimal === '是') {
          if (userInfo.value.f_alias === 'QiaoSong') {
            formData.gas = (Number(getGas.gas)).toFixed(1)
          }
          else {
            formData.gas = (Number(getGas.gas)).toFixed(userInfo.value.f_gas_decimal || 4)
          }
          dyMoney.value = '0'
        }
        else {
          const tempNum = Math.floor(Number(getGas.gas))
          if (Number(getGas.gas) > tempNum) {
            const redundant = await commonCal({
              f_userinfo_id: userInfo.value.f_userinfo_id,
              f_card_id: userInfo.value.f_card_id,
              f_meternumber: userInfo.value.f_meternumber,
              f_userfiles_id: userInfo.value.f_userfiles_id,
              gas: tempNum,
            })
            if (redundant.money) {
              dyMoney.value = (Number(getGas.money) - Number(redundant.money)).toFixed(2)
            }
          }
          else {
            dyMoney.value = '0'
          }
          formData.gas = Math.floor(Number(getGas.gas)).toString()
        }

        if (Number(formData.gas) > Number(maxGas.value)) {
          showFailToast(`购气量超过限制: ${maxGas.value}m³，请重新输入`)
          cleanGasAndMoney()
          paymentValid.value = false
          return
        }
        // 验证最少购买1方气
        if (Number(formData.gas) < 1) {
          showFailToast('最少购买1方气，请增加购气金额')
          formData.money = null
          formData.gas = null
          paymentValid.value = false
          return
        }
      }
      formData.totalCost = Number(userInfo.value.f_user_balance) > Number(formData.money) ? '0' : (Number(formData.money) - Number(userInfo.value.f_user_balance)).toFixed(userInfo.value.f_fee_decimal || 4)
      formData.totalCost = (Number(formData.totalCost) - Number(dyMoney.value) + Number(arrears_fee.value)).toFixed(userInfo.value.f_fee_decimal || 4)
      if (config.floor) {
        formData.totalCost = Math.ceil(Number(formData.totalCost)).toString()
      }
      formData.collection = formData.totalCost
      formData.money = (Number(getGas.money) - Number(dyMoney.value)).toString()
      // 跨阶梯提示
      if (getGas.crossStepCount > 1) {
        showToast('本次购气已跨阶梯')
      }
      // 计算本期结余
      calculateCurrentBalance()
      paymentValid.value = true
    }
    else {
      formData.money = ''
      paymentValid.value = false
    }
  }
  catch (error) {
    console.error('金额计算失败', error)
    showFailToast('金额计算失败')
  }
}

async function calculatePreByCollection() {
  if (Number(arrears_fee.value) > (Number(formData.collection) + Number(userInfo.value.f_user_balance))) {
    showToast('收款不能小于旧表机表欠费，请重新输入')
    formData.collection = '0'
    return
  }
  try {
    formData.collection = Number(formData.collection).toString()
    const calFee = (Number(formData.collection) + Number(userInfo.value.f_user_balance) - Number(arrears_fee.value)).toFixed(4)
    const getGas = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      money: calFee,
    })
    if (userInfo.value.f_isdecimal === '是') {
      formData.gas = (Number(getGas.gas)).toFixed(userInfo.value.f_gas_decimal || 4)
    }
    else {
      formData.gas = Number.parseInt(getGas.gas).toString()
    }

    // 验证最少购买1方气
    if (Number(formData.gas) < 1) {
      showFailToast('最少购买1方气，请增加收款金额')
      formData.collection = null
      paymentValid.value = false
      return
    }

    const redundant = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      gas: formData.gas,
    })
    formData.totalCost = redundant.money
    formData.money = redundant.money
    // 阶梯价格展示
    calculateDetail.value = redundant.calText
    // 跨阶梯提示
    if (redundant.crossStepCount > 1) {
      showToast('本次购气已跨阶梯')
    }
    paymentValid.value = true
  }
  catch (error) {
    showToast(`划价错误，错误类型：${error}`)
  }
}

async function receivedAmountChange(value: string): Promise<void> {
  formData.collection = value

  // 验证金额限制
  if (Number(formData.collection) > Number(maxMoney.value)) {
    showFailToast(`购气金额超过限制: ${maxMoney.value}元，请重新输入`)
    cleanGasAndMoney()
    paymentValid.value = false
    return
  }

  // 验证收款金额必须大于1
  if (!formData.collection || Number(formData.collection) < 1) {
    paymentValid.value = false
    return
  }

  // 如果配置了通过收款金额反向计算，则执行反向计算
  if (config.calculatePreByCollection) {
    await calculatePreByCollection()
  }
  // 计算本期结余
  await calculateCurrentBalance()
}

function calculateCurrentBalance(): void {
  if (config.calculatePreByCollection) {
    formData.curBalance = (Number(formData.collection) + Number(formData.balance) - Number(formData.money)).toFixed(4)
    return
  }
  if (formData.money) {
    formData.curBalance = (Number(formData.collection) - Number(formData.money) + Number(formData.balance)).toFixed(4)
  }
  else {
    formData.curBalance = formData.balance
  }
}

// 抽取支付处理逻辑为单独函数
async function processPayment(tradeNo = ''): Promise<any> {
  try {
    formData.allDue_fee = arrears_fee.value
    // 提取文件结果数组
    const fileResults = fileList.value
      .filter(file => file.result) // 过滤出有result属性的文件
      .map(file => file.result.id) // 提取result属性

    formData.fileList = fileResults.length > 0 ? fileResults : null

    // 组织操作人信息
    const operator = {
      orgId: currUser.resources.orgid,
      orgName: currUser.resources.orgs,
      depId: currUser.resources.depids,
      depName: currUser.resources.deps,
      operator: currUser.resources.name,
      operatorId: currUser.resources.id,
    }

    // 如果有凭证号，则设置
    if (tradeNo) {
      formData.serial_id = tradeNo
    }

    const result = await iotMeterCharge(formData, userInfo.value, operator)

    if (config.hasPrint) {
      // 显示电子凭证
      receiptData.value = {
        transactionId: result.id,
        datetime: new Date().toLocaleString(),
      }
      await printRef.value.printParams(config.printLogic, receiptData.value.transactionId)
    }
    else {
      clickConfirm.value = false
      printRef.value.close()
    }
  }
  catch (error) {
    console.error(error)
    showToast(`物联网收费失败${error.message || error.msg || '未知错误'}`)
    clickConfirm.value = false
    throw error
  }
}

function cleanGasAndMoney() {
  formData.gas = ''
  formData.money = ''
  formData.totalCost = ''
  formData.collection = ''
}

async function onSubmit(): Promise<void> {
  try {
    // 验证必填字段
    if (!formData.payment) {
      showToast('请选择付款方式')
      return
    }
    if (!formData.print) {
      showToast('请选择打印格式')
      return
    }

    // 验证收款金额必须大于1
    if (!formData.collection || Number(formData.collection) < 1) {
      showToast('收款金额必须大于1元')
      return
    }

    // 验证最少购买1方气
    if (!formData.gas || Number(formData.gas) < 1) {
      showToast('最少购买1方气')
      return
    }

    // 验证文件上传要求
    if (useGridUploader.value && gridFileUploaderRef.value) {
      const isValid = gridFileUploaderRef.value.validateAll()
      if (!isValid) {
        return
      }
    }

    clickConfirm.value = true
    showConfirmDialog({
      title: '确认',
      message: `确定对客户${userInfo.value.f_user_name}进行物联网表收费吗？`,
    }).then(async () => {
      // 微信/支付宝支付走单独的流程，通过事件回调处理支付结果
      if (formData.payment.includes('支付宝') || formData.payment.includes('微信')) {
        showQrCodePayment.value = true
      }
      // 其他支付方式直接调用收费接口进行处理
      else {
        await processPayment()
      }
    }).catch((error) => {
      showToast(`物联网收费失败${error.message || error.msg || '未知错误'}`)
      clickConfirm.value = false
    })
  }
  catch (error) {
    console.error(error)
    clickConfirm.value = false
  }
}

async function handleQrCodePaymentSuccess(paymentResult) {
  try {
    // 使用公共处理函数处理支付
    await processPayment(paymentResult.tradeNo)
    showQrCodePayment.value = false
    clickConfirm.value = false
  }
  catch (error) {
    console.error(error)
  }
}

function handleQrCodePaymentCancel() {
  showQrCodePayment.value = false
  clickConfirm.value = false
}

// 取消操作
function handleCancel() {
  showDialog({
    title: '确认取消',
    message: '是否确认取消当前收费操作？',
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '返回',
    confirmButtonColor: '#2563eb',
  }).then((result) => {
    if (result === 'confirm') {
      emit('closeOperation')
    }
  })
}

async function getLimitGas() {
  try {
    const response = await businessGetLimitGas(
      userInfo.value.f_userinfo_id,
      userInfo.value.f_user_id,
      userInfo.value.f_stairprice_id,
    )
    // 处理限购逻辑
    hasLimit.value = response.hasLimit
    if (hasLimit.value) {
      if (response.f_limit_value || response.f_limit_amount) {
        if (response.f_limit_value < 0 || response.f_limit_amount < 0) {
          maxMoney.value = 0
          allLimit.value = response.all_limit ? Number(response.all_limit) : null
          limitReason.value = response?.f_limit_comments
          limitMoney.value = true
          showToast(response.msg || '您已达到限购上限')
        }
        else {
          if (response.f_limit_value) {
            allLimit.value = response.all_limit ? Number(response.all_limit) : null
            limitReason.value = response?.f_limit_comments
            maxGas.value = Number(response.f_limit_value)
            limitGas.value = true
          }
          if (response.f_limit_amount) {
            allLimit.value = response.all_limit ? Number(response.all_limit) : null
            limitReason.value = response?.f_limit_comments
            maxMoney.value = Number(response.f_limit_amount)
            limitMoney.value = true
          }
        }
      }
    }
  }
  catch (error) {
    console.error('获取限购值失败', error)
    showFailToast('获取限购值失败')
  }
}

// 文件上传相关
function onFileAdded(): void {
}

function onFileRemoved(): void {
}

async function initConfig() {
  try {
    // 使用 Promise API 获取配置
    const res = await getConfigByNameAsync('mobile_IOTMeterCenter')

    // 更新配置
    Object.assign(config, res)

    // 初始化表单数据
    formData.print = config.printType
    formData.payment = config.payment
    formData.balance = String(userInfo.value.f_user_balance)
  }
  catch (error) {
    console.error('初始化配置信息失败', error)
  }
}

async function getArrears() {
  try {
    const res = await runLogic('sale_getOwe', { userinfo_id: userInfo.value.f_userinfo_id }, 'af-revenue')
    if (Array.isArray(res)) {
      for (let i = 0; i < res.length; i++) {
        if (Array.isArray(res[i].rows)) {
          for (let j = 0; j < res[i].rows.length; j++) {
            owes.value.push(res[i].rows[j])
          }
        }
      }
    }
    arrears_fee.value = owes.value.reduce((total, item) => {
      return total + (item.f_oughtfee + item.overdue - item.f_debt_money)
    }, 0)
  }
  catch (error) {
    console.error('获取欠费信息失败', error)
  }
}

async function getPrice() {
  try {
    const result = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      gas: 0,
    })
    console.log('获取气价信息：', result)
    gasPrice.value = result.gasPrice
  }
  catch (error) {
    console.error('获取气价失败', error)
    showFailToast('获取气价失败')
  }
}

// 初始化
onMounted(async () => {
  try {
    await initConfig()
    await getPrice()
    await getLimitGas()
    if (config.hasArrearsChange) {
      await getArrears()
    }
  }
  catch (error) {
    console.error('网表收费初始化失败', error)
  }
  finally {
    loading.value = false
  }
})
</script>

<template>
  <div v-if="loading" class="loading-overlay">
    <van-loading size="24px">
      加载中...
    </van-loading>
  </div>

  <div v-else class="iot-payment-form">
    <!-- 头部提示信息 -->
    <InfoCard
      type="info"
      main-text="请填写购气信息，系统将自动为当前用户进行充值操作。"
      :sub-text="`当前气价：${gasPrice} 元/m³${calculateDetail ? `(计算公式：${calculateDetail})` : ''}`"
    />

    <form @submit.prevent="onSubmit">
      <div class="iot-payment-form__content">
        <!-- 气量和购气金额计算组件 -->
        <CardContainer class="iot-payment-form__section-margin">
          <CardHeader title="购气数量" />
          <IotGasCalculator
            :gas="formData.gas"
            :amount="formData.money"
            :max-amount="maxMoney"
            :max-gas-amount="maxGas"
            :all-limit="allLimit"
            :limit-reason="limitReason"
            :limit-gas="limitGas"
            :limit-money="limitMoney"
            @calculate-by-gas="calculateByGas"
            @calculate-by-amount="calculateByAmount"
          />
        </CardContainer>

        <div class="iot-payment-form__row">
          <CardContainer>
            <CardHeader title="收款信息" />
            <!-- 收款信息组件 -->
            <IotPaymentInfo
              :amount="formData.money"
              :collection="formData.collection"
              :previous-balance="formData.balance"
              :cur-balance="formData.curBalance"
              @received-amount-change="receivedAmountChange"
              @valid="paymentValid = $event"
            />
          </CardContainer>
          <!-- 打印及备注 -->
          <ChargePrintSelectorAndRemarks
            v-model="formData.print"
            v-model:remarks="formData.comments"
          />
        </div>

        <!-- 支付方式 - 使用PaymentMethodSelectorCard组件 -->
        <PaymentMethodSelectorCard
          v-model="formData.payment"
          title="支付方式"
          :allowed-methods="config.allowedMethods"
          class="iot-payment-form__section-margin"
        />

        <!-- 宫格上传组件（多种文件类型时使用） -->
        <GridFileUploader
          v-if="useGridUploader"
          ref="gridFileUploaderRef"
          v-model:file-list="fileList"
          :file-types="config.fileTypes"
          title="上传附件"
        />
        <CardContainer
          v-else
          class="iot-payment-form__section-margin"
        >
          <!-- 普通上传组件（单一文件类型或无配置时使用） -->
          <FileUploader
            v-model:file-list="fileList"
            title="上传附件"
            :multiple="true"
            :max-size="10 * 1024 * 1024"
            :user-type="config.fileTypes.length > 0 ? config.fileTypes[0].userType : '物联网表收费'"
            :allowed-types="['image/png', 'image/jpeg', 'application/pdf']"
            @file-added="onFileAdded"
            @file-removed="onFileRemoved"
          />
        </CardContainer>
      </div>
      <!-- 实收金额和购气信息区域 -->
      <div class="iot-payment-form__summary">
        <div class="iot-payment-form__summary-content">
          <p class="iot-payment-form__summary-main">
            实收金额：<span class="iot-payment-form__summary-amount">¥ {{ formData.collection }}</span>
          </p>
          <p class="iot-payment-form__summary-sub">
            购气：¥{{ formData.money }} / {{ formData.gas }}m³
          </p>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="iot-payment-form__actions">
        <van-button
          type="default"
          :loading="clickConfirm"
          size="normal"
          @click="handleCancel"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          native-type="submit"
          size="normal"
          :loading="clickConfirm"
          :disabled="!paymentValid"
        >
          确认收费
        </van-button>
      </div>
    </form>

    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" />

    <!-- 二维码支付弹窗 -->
    <CodePayment
      v-if="showQrCodePayment"
      v-model:show="showQrCodePayment"
      :payment="formData.payment"
      :collection="formData.collection"
      type="物联网收费"
      @payment-success="handleQrCodePaymentSuccess"
      @payment-cancel="handleQrCodePaymentCancel"
    />
  </div>
</template>

<style lang="less" scoped>
.iot-payment-form {
  &__content {
    margin-bottom: 16px;
  }

  &__row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 16px;
    margin-bottom: 16px;

    @media screen and (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  &__hint {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
  }

  &__section-margin {
    margin-bottom: 16px;
  }

  &__summary {
    background: linear-gradient(to right, #e6f7ff, #f0f7ff);
    border: 1px solid #d6e8ff;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
  }

  &__summary-content {
    width: 100%;
  }

  &__summary-main {
    font-size: 16px;
    font-weight: 500;
    color: #4b5563;
    margin: 0 0 4px 0;
  }

  &__summary-amount {
    font-size: 20px;
    font-weight: 600;
    color: #2563eb;
  }

  &__summary-sub {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
  }
}
.loading-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style>
