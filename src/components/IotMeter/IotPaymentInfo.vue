<script setup lang="ts">
import { ref, watch } from 'vue'
import { FormField } from '../common'

const props = defineProps({
  amount: {
    type: String,
    required: true,
  },
  collection: {
    type: String,
    required: true,
  },
  previousBalance: {
    type: String,
    default: '0',
  },
  curBalance: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['receivedAmountChange', 'balance-change', 'valid'])

// 本地状态
const receivedAmount = ref(props.collection)
const isValid = ref(false)
const showWarning = ref(false)

// 验证收款金额
function validateReceivedAmount() {
  isValid.value = Number(receivedAmount.value) + Number(props.previousBalance) >= Number(props.amount)
  showWarning.value = !isValid.value
  // 通知父组件状态变化
  emit('receivedAmountChange', receivedAmount.value)
  emit('valid', isValid.value)
}
// 监听amount变化，自动更新收款金额
watch(() => props.collection, (newVal) => {
  // 只有当收款金额小于新金额或未设置收款金额时才更新
  // if (Number(receivedAmount.value) < Number(newVal) || receivedAmount.value === '') {
  receivedAmount.value = newVal
  // }
  validateReceivedAmount()
}, { immediate: true })
</script>

<template>
  <div class="iot-payment-info">
    <FormField label="收款金额">
      <van-field
        v-model="receivedAmount"
        type="number"
        input-align="right"
        placeholder="0.00"
        @input="validateReceivedAmount"
      >
        <template #left-icon>
          <span class="iot-payment-info__unit">¥</span>
        </template>
        <template #right-icon>
          <span class="iot-payment-info__unit">元</span>
        </template>
      </van-field>

      <div v-if="showWarning" class="iot-payment-info__warning">
        <van-icon name="warning-o" color="#ee0a24" />
        <span class="iot-payment-info__warning-text">收款金额不能小于购气金额</span>
      </div>
    </FormField>

    <FormField label="上期结余">
      <van-field
        :model-value="props.previousBalance"
        readonly
        input-align="right"
        :disabled="true"
        class="iot-payment-info__readonly"
      >
        <template #left-icon>
          <span class="iot-payment-info__unit">¥</span>
        </template>
        <template #right-icon>
          <span class="iot-payment-info__unit">元</span>
        </template>
      </van-field>
    </FormField>

    <FormField label="本期结余">
      <van-field
        :model-value="props.curBalance"
        readonly
        input-align="right"
        :disabled="true"
        class="iot-payment-info__readonly"
      >
        <template #left-icon>
          <span class="iot-payment-info__unit">¥</span>
        </template>
        <template #right-icon>
          <span class="iot-payment-info__unit">元</span>
        </template>
      </van-field>
    </FormField>
  </div>
</template>

<style lang="less" scoped>
.iot-payment-info {
  &__unit {
    color: #666;
    font-size: 14px;
  }

  &__warning {
    display: flex;
    align-items: center;
    margin-top: 4px;
    color: #ee0a24;
  }

  &__warning-text {
    font-size: 12px;
    margin-left: 4px;
  }

  &__readonly {
    background-color: #f5f5f5;
  }
}
</style>
