<script setup lang="ts">
interface ChargeItem {
  id: number
  type: string
  item: string
  unitPrice: number
  quantity: number
  total: string
}

defineProps<{
  item: ChargeItem
  index: number
}>()

defineEmits<{
  (e: 'remove', id: number): void
}>()

function formatPrice(price: number): string {
  return price.toFixed(2)
}
</script>

<template>
  <div class="other-charge-item">
    <div class="other-charge-item__header">
      <h5 class="other-charge-item__title">
        费用项 #{{ index + 1 }}
      </h5>
      <van-icon
        name="cross"
        class="other-charge-item__delete"
        @click="$emit('remove', item.id)"
      />
    </div>

    <div class="other-charge-item__info">
      <div class="other-charge-item__field">
        <p class="other-charge-item__label">
          收费类型
        </p>
        <p class="other-charge-item__value">
          {{ item.type }}
        </p>
      </div>
      <div class="other-charge-item__field">
        <p class="other-charge-item__label">
          具体项目
        </p>
        <p class="other-charge-item__value">
          {{ item.item }}
        </p>
      </div>
    </div>

    <div class="other-charge-item__details">
      <div class="other-charge-item__field">
        <p class="other-charge-item__label">
          单价
        </p>
        <p class="other-charge-item__value">
          ¥{{ formatPrice(item.unitPrice) }}
        </p>
      </div>
      <div class="other-charge-item__field">
        <p class="other-charge-item__label">
          数量
        </p>
        <p class="other-charge-item__value">
          {{ item.quantity }}
        </p>
      </div>
      <div class="other-charge-item__field">
        <p class="other-charge-item__label">
          小计
        </p>
        <p class="other-charge-item__value--highlight">
          ¥{{ item.total }}
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.other-charge-item {
  background-color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  margin-bottom: 12px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
  }

  &__title {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    margin: 0;
  }

  &__delete {
    color: #ef4444;
    font-size: 16px;

    &:active {
      color: #b91c1c;
    }
  }

  &__info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 8px;
  }

  &__details {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;
  }

  &__field {
    margin-bottom: 0;
  }

  &__label {
    font-size: 12px;
    color: #6b7280;
    margin: 0 0 2px 0;
  }

  &__value {
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
    margin: 0;

    &--highlight {
      font-size: 14px;
      font-weight: 500;
      color: #2563eb;
      margin: 0;
    }
  }
}
</style>
