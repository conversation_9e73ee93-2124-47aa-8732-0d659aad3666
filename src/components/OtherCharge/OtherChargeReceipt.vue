<script setup lang="ts">
import { computed } from 'vue'

interface ChargeItem {
  id: number
  type: string
  item: string
  unitPrice: number
  quantity: number
  total: string
}

interface ReceiptData {
  transactionId: string
  userName: string
  userId: string
  cardNo: string
  items: ChargeItem[]
  totalAmount: string
  paymentMethod: string
  remarks: string
  operator: string
  dateTime: string
}

const props = defineProps<{
  show: boolean
  receiptData: ReceiptData
}>()

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void
  (e: 'complete'): void
  (e: 'saveScreenshot'): void
}>()

const show = computed({
  get: () => props.show,
  set: value => emit('update:show', value),
})

function formatPrice(price: number): string {
  return price.toFixed(2)
}

function getPaymentMethodName(method: string): string {
  const methods: Record<string, string> = {
    cash: '现金缴费',
    wechat: '微信支付',
    alipay: '支付宝支付',
    unionpay: '银联支付',
  }
  return methods[method] || method
}

function saveScreenshot() {
  emit('saveScreenshot')
}

function closeReceipt() {
  show.value = false
  emit('complete')
}
</script>

<template>
  <van-popup
    v-model:show="show"
    :close-on-click-overlay="false"
    :close-on-popstate="true"
    :style="{ width: '90%', maxWidth: '400px', borderRadius: '8px' }"
    class="other-charge-receipt"
  >
    <div class="other-charge-receipt__content">
      <!-- 小票标题 -->
      <div class="other-charge-receipt__header">
        <h2 class="other-charge-receipt__title">
          其他收费电子凭证
        </h2>
        <p class="other-charge-receipt__subtitle">
          城市燃气服务有限公司
        </p>
      </div>

      <!-- 小票信息 -->
      <div class="other-charge-receipt__info">
        <div class="other-charge-receipt__row">
          <span class="other-charge-receipt__label">交易单号:</span>
          <span class="other-charge-receipt__value">{{ receiptData.transactionId }}</span>
        </div>
        <div class="other-charge-receipt__row">
          <span class="other-charge-receipt__label">用户姓名:</span>
          <span class="other-charge-receipt__value">{{ receiptData.userName }}</span>
        </div>
        <div class="other-charge-receipt__row">
          <span class="other-charge-receipt__label">用户编号:</span>
          <span class="other-charge-receipt__value">{{ receiptData.userId }}</span>
        </div>
        <div class="other-charge-receipt__row">
          <span class="other-charge-receipt__label">卡号/表号:</span>
          <span class="other-charge-receipt__value">{{ receiptData.cardNo }}</span>
        </div>

        <!-- 收费项目列表 -->
        <div class="other-charge-receipt__items">
          <div v-for="item in receiptData.items" :key="item.id" class="other-charge-receipt__item">
            <div class="other-charge-receipt__item-row">
              <span class="other-charge-receipt__item-name">{{ item.type }} - {{ item.item }}</span>
              <span class="other-charge-receipt__item-price">¥{{ item.total }}</span>
            </div>
            <div class="other-charge-receipt__item-details">
              {{ item.quantity }} × ¥{{ formatPrice(item.unitPrice) }}
            </div>
          </div>
        </div>

        <div class="other-charge-receipt__total-row">
          <span class="other-charge-receipt__total-label">合计金额:</span>
          <span class="other-charge-receipt__total-value">¥{{ receiptData.totalAmount }}</span>
        </div>

        <div class="other-charge-receipt__row">
          <span class="other-charge-receipt__label">支付方式:</span>
          <span class="other-charge-receipt__value">{{ getPaymentMethodName(receiptData.paymentMethod) }}</span>
        </div>

        <div v-if="receiptData.remarks" class="other-charge-receipt__row">
          <span class="other-charge-receipt__label">备注信息:</span>
          <span class="other-charge-receipt__value">{{ receiptData.remarks }}</span>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="other-charge-receipt__footer">
        <div class="other-charge-receipt__row">
          <span class="other-charge-receipt__label">操作员:</span>
          <span class="other-charge-receipt__value">{{ receiptData.operator }}</span>
        </div>
        <div class="other-charge-receipt__row">
          <span class="other-charge-receipt__label">交易时间:</span>
          <span class="other-charge-receipt__value">{{ receiptData.dateTime }}</span>
        </div>
      </div>

      <!-- 二维码 -->
      <div class="other-charge-receipt__qrcode">
        <div class="other-charge-receipt__qrcode-placeholder">
          <van-icon name="qr" class="other-charge-receipt__qrcode-icon" />
        </div>
      </div>

      <!-- 提示文字 -->
      <p class="other-charge-receipt__note">
        感谢您使用我们的服务，如有疑问请拨打客服热线 <br>
        400-123-4567
      </p>
    </div>

    <!-- 操作按钮 -->
    <div class="other-charge-receipt__actions">
      <div class="other-charge-receipt__action-btn" @click="saveScreenshot">
        <van-icon name="photograph-o" class="other-charge-receipt__btn-icon" />
        <span>保存截图</span>
      </div>
      <van-button
        type="primary"
        class="other-charge-receipt__confirm-btn"
        @click="closeReceipt"
      >
        完成
      </van-button>
    </div>
  </van-popup>
</template>

<style scoped lang="less">
.other-charge-receipt {
  &__content {
    padding: 24px;
    background-color: #ffffff;
  }

  &__header {
    text-align: center;
    margin-bottom: 16px;
  }

  &__title {
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 4px 0;
  }

  &__subtitle {
    font-size: 14px;
    color: #6b7280;
    margin: 4px 0 0 0;
  }

  &__info {
    border-top: 1px dashed #d1d5db;
    border-bottom: 1px dashed #d1d5db;
    padding: 16px 0;
    margin-bottom: 16px;
  }

  &__row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
  }

  &__label {
    color: #6b7280;
  }

  &__value {
    color: #1f2937;
    font-weight: 500;
  }

  &__items {
    margin: 8px 0;
  }

  &__item {
    margin-bottom: 8px;
  }

  &__item-row {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
  }

  &__item-name {
    color: #6b7280;
  }

  &__item-price {
    color: #1f2937;
  }

  &__item-details {
    font-size: 12px;
    color: #9ca3af;
    padding-left: 8px;
  }

  &__total-row {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;
    padding-top: 8px;
    border-top: 1px solid #e5e7eb;
    font-size: 14px;
    font-weight: 500;
  }

  &__total-label {
    color: #6b7280;
  }

  &__total-value {
    color: #1f2937;
  }

  &__footer {
    margin-bottom: 16px;
  }

  &__qrcode {
    display: flex;
    justify-content: center;
    margin: 16px 0;
  }

  &__qrcode-placeholder {
    width: 96px;
    height: 96px;
    background-color: #f3f4f6;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__qrcode-icon {
    font-size: 48px;
    color: #9ca3af;
  }

  &__note {
    font-size: 12px;
    color: #9ca3af;
    text-align: center;
    margin: 12px 0 0 0;
    line-height: 1.5;
  }

  &__actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
    border-radius: 0 0 8px 8px;
  }

  &__action-btn {
    display: flex;
    align-items: center;
    color: #2563eb;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;

    &:active {
      color: #1d4ed8;
    }
  }

  &__btn-icon {
    margin-right: 4px;
  }

  &__confirm-btn {
    background-color: #2563eb;
    border-radius: 6px;
    padding: 0 16px;
    height: 36px;
    font-size: 14px;
  }
}
</style>
