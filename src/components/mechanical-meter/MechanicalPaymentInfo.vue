<script setup lang="ts">
import { FormField } from '@/components/common'
import { ref, watch } from 'vue'

const props = defineProps<{
  previousBalance: number
  payableAmount: number
  curBalance: number
  receivedAmount: string | number
}>()

const emit = defineEmits(['receivedAmountChange', 'valid'])

const localReceivedAmount = ref(props.receivedAmount)
const amountWarning = ref(false)

// 验证收款金额
function validateAmount() {
  amountWarning.value = Number(localReceivedAmount.value) < props.payableAmount
  emit('valid', !amountWarning.value)
  return !amountWarning.value
}

// 监听收款金额变化
watch(localReceivedAmount, (newValue) => {
  validateAmount()
  emit('receivedAmountChange', newValue)
})

// 监听props变化
watch(() => props.receivedAmount, (newValue) => {
  localReceivedAmount.value = newValue
})
</script>

<template>
  <div class="mechanical-payment-info">
    <FormField label="账户余额(¥)">
      <van-field
        :model-value="props.previousBalance"
        readonly
        input-align="right"
        :disabled="true"
        class="mechanical-payment-info__readonly"
      >
        <template #left-icon>
          <span class="mechanical-payment-info__unit">¥</span>
        </template>
        <template #right-icon>
          <span class="mechanical-payment-info__unit">元</span>
        </template>
      </van-field>
    </FormField>

    <FormField label="应交金额(¥)">
      <van-field
        :model-value="props.payableAmount"
        readonly
        input-align="right"
        :disabled="true"
        class="mechanical-payment-info__readonly"
      >
        <template #left-icon>
          <span class="mechanical-payment-info__unit">¥</span>
        </template>
        <template #right-icon>
          <span class="mechanical-payment-info__unit">元</span>
        </template>
      </van-field>

      <div class="mechanical-payment-info__warning">
        <span class="mechanical-payment-info__hint">用气金额 + 违约金</span>
      </div>
    </FormField>

    <FormField label="收款金额">
      <van-field
        v-model="localReceivedAmount"
        type="number"
        input-align="right"
        placeholder="0.00"
        @blur="validateAmount"
      >
        <template #left-icon>
          <span class="mechanical-payment-info__unit">¥</span>
        </template>
        <template #right-icon>
          <span class="mechanical-payment-info__unit">元</span>
        </template>
      </van-field>

      <div v-if="amountWarning" class="mechanical-payment-info__warning">
        <van-icon name="warning-o" color="#ee0a24" />
        <span class="mechanical-payment-info__warning-text">收款金额必须大于等于应交金额</span>
      </div>
    </FormField>

    <FormField label="本期余额(¥)">
      <van-field
        :model-value="props.curBalance"
        readonly
        input-align="right"
        :disabled="true"
        class="mechanical-payment-info__readonly"
      >
        <template #left-icon>
          <span class="mechanical-payment-info__unit">¥</span>
        </template>
        <template #right-icon>
          <span class="mechanical-payment-info__unit">元</span>
        </template>
      </van-field>

      <div class="mechanical-payment-info__warning">
        <span class="mechanical-payment-info__hint">收款金额 - 应交金额</span>
      </div>
    </FormField>
  </div>
</template>

<style lang="less" scoped>
.mechanical-payment-info {
  &__unit {
    color: #666;
    font-size: 14px;
  }

  &__warning {
    display: flex;
    align-items: center;
    margin-top: 4px;
    color: #ee0a24;
  }

  &__warning-text {
    font-size: 12px;
    margin-left: 4px;
  }

  &__readonly {
    background-color: #f5f5f5;
  }
  &__hint {
    font-size: 12px;
    color: #969799;
  }
}
</style>
