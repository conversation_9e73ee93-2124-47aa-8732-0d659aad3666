<script setup lang="ts">
import { Icon as VanIcon } from 'vant'

interface DebtSummaryProps {
  totalGasConsumption: number
  totalPenalty: number
  totalGasAmount: number
  totalDebt: number
  monthsCount: number
  showDetails: boolean
}

const props = defineProps<DebtSummaryProps>()
const emit = defineEmits(['update:showDetails'])

const toggleDetails = () => {
  emit('update:showDetails', !props.showDetails)
}
</script>

<template>
  <div class="debt-summary">
    <div class="debt-summary__grid">
      <div class="debt-summary__item">
        <van-icon name="balance-o" class="debt-summary__icon debt-summary__icon--blue" />
        <div>
          <p class="debt-summary__label">总用气量</p>
          <p class="debt-summary__value">{{ totalGasConsumption }} m³</p>
        </div>
      </div>
      <div class="debt-summary__item">
        <van-icon name="warning-o" class="debt-summary__icon debt-summary__icon--orange" />
        <div>
          <p class="debt-summary__label">总违约金</p>
          <p class="debt-summary__value">¥{{ totalPenalty.toFixed(2) }}</p>
        </div>
      </div>
      <div class="debt-summary__item">
        <van-icon name="gold-coin-o" class="debt-summary__icon debt-summary__icon--red" />
        <div>
          <p class="debt-summary__label">总用气金额</p>
          <p class="debt-summary__value debt-summary__value--blue">¥{{ totalGasAmount.toFixed(2) }}</p>
        </div>
      </div>
      <div class="debt-summary__item">
        <van-icon name="calendar-o" class="debt-summary__icon debt-summary__icon--blue" />
        <div>
          <p class="debt-summary__label">欠费月份</p>
          <p class="debt-summary__value">{{ monthsCount }}个月</p>
        </div>
      </div>
      <div class="debt-summary__item">
        <van-icon name="bill-o" class="debt-summary__icon debt-summary__icon--red" />
        <div>
          <p class="debt-summary__label">应缴金额</p>
          <p class="debt-summary__value debt-summary__value--red">¥{{ totalDebt.toFixed(2) }}</p>
        </div>
      </div>
      <div class="debt-summary__button-container">
        <button
          type="button"
          class="debt-summary__toggle-button"
          @click="toggleDetails"
        >
          <span class="debt-summary__toggle-text">{{ showDetails ? '收起详情' : '查看详情' }}</span>
          <van-icon
            name="arrow-down"
            class="debt-summary__toggle-icon"
            :class="{ 'debt-summary__toggle-icon--rotated': showDetails }"
          />
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.debt-summary {
  &__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 12px;
    background-color: #ebf5ff;
    border-radius: 8px;
    border: 1px solid #d0e5ff;
    margin-bottom: 12px;

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &__item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__icon {
    font-size: 16px;

    &--blue {
      color: #1989fa;
    }

    &--orange {
      color: #ff976a;
    }

    &--red {
      color: #ee0a24;
    }
  }

  &__label {
    font-size: 12px;
    color: #646566;
    margin: 0;
  }

  &__value {
    font-size: 14px;
    font-weight: 500;
    color: #323233;
    margin: 0;

    &--blue {
      color: #1989fa;
      font-weight: 600;
    }

    &--red {
      color: #ee0a24;
      font-weight: 600;
    }
  }

  &__button-container {
    display: flex;
    align-items: center;
    justify-content: center;
    grid-column: span 2;

    @media (min-width: 768px) {
      grid-column: span 3;
    }
  }

  &__toggle-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 16px;
    background-color: #fff;
    border: 1px solid #ddecff;
    color: #1989fa;
    border-radius: 999px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f0f9ff;
      border-color: #bbdfff;
    }

    &:active {
      background-color: #e6f4ff;
    }
  }

  &__toggle-icon {
    font-size: 14px;
    color: #1989fa;
    transition: transform 0.2s;

    &--rotated {
      transform: rotate(180deg);
    }
  }
}
</style>