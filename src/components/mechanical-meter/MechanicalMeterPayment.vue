<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByName, runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { showDialog, showToast } from 'vant'
import { computed, onMounted, reactive, ref } from 'vue'
import { ChargePrintSelectorAndRemarks, CodePayment, FileUploader, GridFileUploader, PaymentMethodSelectorCard, ReceiptModal } from '@/components/common'
import { mechanicalCharge } from '@/services/api/business'
import useBusinessStore from '@/stores/modules/business'
import DebtDetailCard from './DebtDetailCard.vue'
import DebtSummary from './DebtSummary.vue'
import MechanicalPaymentInfo from './MechanicalPaymentInfo.vue'

interface DebtDetail {
  inputDate: string
  lastTableBase: string
  tableBase: string
  gas: string
  money: string
  debtMoney: string
  overdue: string
  actuallyOwing: string
  [key: string]: any
}

interface ReceiptData {
  transactionId: string
  datetime: string
}

interface FormData {
  payment: string
  balance: number
  gas: number
  money: number
  totalCost: number
  curBalance: number
  collection: string | number
  garbageFee: number
  overdue: number
  lowest: number
  print: string
  serial_id: string
  selectedIds: any[]
  comments: string
  [key: string]: any
}

interface ConfigInterface {
  hasPrint: boolean
  floor: boolean
  printType: string
  payment: string
  allowedMethods: string[] | null
  [key: string]: any
  printLogic: string
  fileTypes?: {
    userType: string
    picMinNum: number
    description?: string
  }[]
}

const emit = defineEmits(['closeOperation', 'complete'])

const formData = reactive<FormData>({
  payment: '现金缴费',
  balance: 0,
  gas: 0,
  money: 0,
  totalCost: 0,
  curBalance: 0,
  collection: 0,
  garbageFee: 0,
  overdue: 0,
  lowest: 0,
  print: '',
  serial_id: '',
  selectedIds: [],
  comments: '',
})

const config = reactive<ConfigInterface>({
  hasPrint: false,
  floor: false,
  printType: '普通收据',
  payment: '现金缴费',
  allowedMethods: null,
  printLogic: '',
  fileTypes: [],
})

// 子组件的响应组件引用
const printRef = ref()

// 欠费明细数据
const debtDetails = ref<DebtDetail[]>([])
const showDebtDetails = ref(false)
// 计算总用气量
const totalGasConsumption = computed(() => {
  return debtDetails.value.reduce((sum, item) => sum + Number.parseFloat(item.gas), 0)
})

// 计算总违约金
const totalPenalty = computed(() => {
  return debtDetails.value.reduce((sum, item) => sum + Number.parseFloat(item.overdue), 0)
})

// 计算总用气金额
const totalGasAmount = computed(() => {
  return debtDetails.value.reduce((sum, item) => sum + Number.parseFloat(item.money), 0)
})

// 计算总欠费金额
const totalDebtAmount = computed(() => {
  return debtDetails.value.reduce((sum, item) => sum + Number.parseFloat(item.actuallyOwing), 0)
})
// 收款验证状态
const paymentValid = ref(false)
// 当前用户
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
console.log('============================', userInfo)
// 其他状态
const clickConfirm = ref(false)
const showQrCodePayment = ref(false)
const receiptData = ref<ReceiptData>({
  transactionId: '',
  datetime: '',
})
// 文件上传相关数据
const fileList = ref<FileItem[]>([])
const gridFileUploaderRef = ref()

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

async function getArrears() {
  try {
    const res = await runLogic('sale_getOwe', { f_userinfo_id: userInfo.value.f_userinfo_id }, 'af-revenue')
    console.log(res)
    res[0]?.rows.forEach((item) => {
      formData.money += (item.f_oughtfee - 0) + (item.overdue - 0)
      formData.totalCost = (item.f_oughtfee - 0) + (item.overdue - 0)
      if (config.floor) {
        formData.collection = Math.floor(formData.totalCost - formData.balance)
      }
      else if (config.ceil) {
        formData.collection = Math.ceil(formData.totalCost - formData.balance)
      }
      else {
        formData.collection = formData.totalCost - formData.balance
      }
      formData.collection = formData.collection < 0 ? 0 : formData.collection
      formData.selectedIds.push({ id: item.handplan_id })
      debtDetails.value.push({
        inputDate: item.f_input_date.toString().substring(0, 10),
        lastTableBase: item.f_last_tablebase.toString(),
        tableBase: item.f_tablebase.toString(),
        gas: item.f_oughtamount.toString(),
        money: item.f_allfee.toString(),
        debtMoney: item.f_debt_money.toString(),
        overdue: item.overdue.toString(),
        actuallyOwing: ((item.f_oughtfee - 0) + (item.overdue - 0)).toString(),
      })
    })

    // 如果没有欠费记录，设置默认值
    if (!res[0]?.rows || res[0].rows.length === 0) {
      formData.collection = 0
      formData.totalCost = 0
      formData.money = 0
    }
  }
  catch (error) {
    console.error('获取欠费信息失败', error)
    // 异常情况下也设置默认值
    formData.collection = 0
    formData.totalCost = 0
    formData.money = 0
  }
}

function initConfig() {
  try {
    console.log('初始化配置信息')
    getConfigByName('mobile_MachineCharge', (res) => {
      Object.assign(config, res)
      formData.print = config.printType
      formData.payment = config.payment
    })
    formData.balance = Number(userInfo.value.f_user_balance)
  }
  catch (error) {
    console.error('初始化配置信息失败', error)
  }
}

// 初始化
onMounted(async () => {
  await initConfig()
  await getArrears()
})

// 表单提交
function onSubmit() {
  // 验证必填字段
  if (!formData.payment) {
    showToast('请选择付款方式')
    return
  }
  if (!formData.print) {
    showToast('请选择打印格式')
    return
  }

  // 验证收款金额
  if (formData.collection === null || formData.collection === undefined) {
    showToast('收款金额不能为空')
    return
  }
  if (Number(formData.collection) < 0) {
    showToast('收款金额必须为有效的非负数')
    return
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return
    }
  }
  clickConfirm.value = true

  showDialog({
    title: '确认',
    message: `确定对客户${userInfo.value.f_user_name}进行机表收费吗？`,
    showCancelButton: true,
  }).then(async () => {
    // 微信/支付宝支付走单独的流程，通过事件回调处理支付结果
    if (formData.payment.includes('支付宝') || formData.payment.includes('微信')) {
      showQrCodePayment.value = true
    }
    // 其他支付方式直接调用收费接口进行处理
    else {
      await processPayment()
    }
  }).catch(() => {
    clickConfirm.value = false
  })
}

// 抽取支付处理逻辑为单独函数
async function processPayment(tradeNo = ''): Promise<any> {
  try {
    formData.overdue = totalPenalty.value
    // 提取文件结果数组
    const fileResults = fileList.value
      .filter(file => file.result) // 过滤出有result属性的文件
      .map(file => file.result.id) // 提取result属性
    formData.fileList = fileResults.length > 0 ? fileResults : []
    // 组织操作人信息
    const operator = {
      orgId: currUser.resources.orgid,
      orgName: currUser.resources.orgs,
      depId: currUser.resources.depids,
      depName: currUser.resources.deps,
      operator: currUser.resources.name,
      operatorId: currUser.resources.id,
    }

    // 如果有凭证号，则设置
    if (tradeNo) {
      formData.serial_id = tradeNo
    }

    const result = await mechanicalCharge(formData, userInfo.value, config.floor, operator)
    if (config.hasPrint) {
      // 显示电子凭证
      receiptData.value = {
        transactionId: result.value,
        datetime: new Date().toLocaleString(),
      }
      await printRef.value.printParams(config.printLogic, receiptData.value.transactionId)
    }
    else {
      clickConfirm.value = false
      printRef.value.close()
    }
  }
  catch (error) {
    console.error(error)
    showToast(`机表收费失败${error.message || error.msg || '未知错误'}`)
    clickConfirm.value = false
    throw error
  }
}

async function handleQrCodePaymentSuccess(paymentResult) {
  try {
    // 使用公共处理函数处理支付
    await processPayment(paymentResult.tradeNo)
    showQrCodePayment.value = false
    clickConfirm.value = false
  }
  catch (error) {
    console.error(error)
    clickConfirm.value = false
  }
}

function handleQrCodePaymentCancel() {
  showQrCodePayment.value = false
  clickConfirm.value = false
}

// 文件上传相关
function onFileAdded(): void {
}

function onFileRemoved(): void {
}

function receivedAmountChange(value) {
  formData.collection = value
  calculateCurrentBalance()
}

function calculateCurrentBalance() {
  const collection = Number(formData.collection)
  const balance = Number(formData.balance)
  const totalCost = Number(formData.totalCost)

  if ((collection + balance) > totalCost) {
    formData.curBalance = Number(((collection + balance) - totalCost).toFixed(4))
  }
  else if (config.floor) {
    formData.curBalance = Number(((collection + balance) - totalCost).toFixed(4))
  }
  else {
    return 0
  }
}

// 取消操作
function cancel() {
  showDialog({
    title: '确认取消',
    message: '是否确认取消当前收费操作？',
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '返回',
    confirmButtonColor: '#2563eb',
  }).then((result) => {
    if (result === 'confirm') {
      emit('closeOperation')
    }
  })
}

// 重置表单
</script>

<template>
  <div class="mechanical-meter-payment">
    <div class="mechanical-meter-payment__header">
      <i class="fas fa-money-bill-wave mechanical-meter-payment__header-icon" />
      <div>
        <p class="mechanical-meter-payment__header-title">
          机表收费 - 欠费缴纳
        </p>
        <p class="mechanical-meter-payment__header-subtitle">
          当前用户: <span class="mechanical-meter-payment__header-highlight">{{ userInfo.f_user_name }}</span>
          (<span>{{ userInfo.f_meternumber }}</span>)
        </p>
      </div>
    </div>

    <form @submit.prevent="onSubmit">
      <CardContainer class="mechanical-meter-payment__section-margin">
        <CardHeader title="欠费明细" />
        <!-- 欠费汇总 -->
        <DebtSummary
          v-model:show-details="showDebtDetails"
          :total-gas-consumption="totalGasConsumption"
          :total-penalty="totalPenalty"
          :total-gas-amount="totalGasAmount"
          :total-debt="totalDebtAmount"
          :months-count="debtDetails.length"
        />

        <!-- 欠费明细卡片 -->
        <div v-show="showDebtDetails" class="mechanical-meter-payment__debt-details">
          <DebtDetailCard
            v-for="(detail, index) in debtDetails"
            :key="index"
            :detail="detail"
          />
        </div>
      </CardContainer>

      <div class="mechanical-meter-payment__row">
        <!-- 收款信息 -->
        <CardContainer>
          <CardHeader title="收款信息" />
          <MechanicalPaymentInfo
            v-model:received-amount="formData.collection"
            :previous-balance="formData.balance"
            :payable-amount="formData.money"
            :cur-balance="formData.curBalance"
            @received-amount-change="receivedAmountChange"
            @valid="paymentValid = $event"
          />
        </CardContainer>
        <!-- 打印及备注 -->
        <ChargePrintSelectorAndRemarks
          v-model="formData.print"
          v-model:remarks="formData.comments"
          class="mechanical-meter-payment__section-margin"
        />
      </div>
      <!-- 支付方式 -->
      <PaymentMethodSelectorCard
        v-model="formData.payment"
        title="支付方式"
        :allowed-methods="config.allowedMethods"
        class="mechanical-meter-payment__section-margin"
      />

      <!-- 宫格上传组件（多种文件类型时使用） -->
      <GridFileUploader
        v-if="useGridUploader"
        ref="gridFileUploaderRef"
        v-model:file-list="fileList"
        :file-types="config.fileTypes"
        title="上传附件"
        class="mechanical-meter-payment__section-margin"
      />
      <!-- 普通上传组件（单一文件类型或无配置时使用） -->
      <CardContainer
        v-else
        class="mechanical-meter-payment__section-margin"
      >
        <FileUploader
          v-model:file-list="fileList"
          title="上传附件"
          :multiple="true"
          :user-type="config.fileTypes && config.fileTypes.length > 0 ? config.fileTypes[0].userType : '机表收费'"
          :max-size="10 * 1024 * 1024"
          :allowed-types="['image/png', 'image/jpeg', 'application/pdf']"
          @file-added="onFileAdded"
          @file-removed="onFileRemoved"
        />
      </CardContainer>

      <!-- 按钮区域 -->
      <div class="mechanical-meter-payment__actions">
        <van-button
          type="default"
          :loading="clickConfirm"
          size="normal"
          @click="cancel"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          native-type="submit"
          size="normal"
          :loading="clickConfirm"
          :disabled="!paymentValid"
        >
          确认收费
        </van-button>
      </div>
    </form>
    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" />
    <!-- 二维码支付弹窗 -->
    <CodePayment
      v-if="showQrCodePayment"
      v-model:show="showQrCodePayment"
      :payment="formData.payment"
      :collection="formData.collection"
      type="机表收费"
      @payment-success="handleQrCodePaymentSuccess"
      @payment-cancel="handleQrCodePaymentCancel"
    />
  </div>
</template>

<style lang="less" scoped>
.mechanical-meter-payment {
  &__header {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    margin-bottom: 16px;
    background: linear-gradient(to right, #ebf5ff, #e6f4ff);
    border-left: 4px solid #1989fa;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    &-icon {
      color: #1989fa;
      font-size: 18px;
      margin-top: 4px;
      margin-right: 12px;
    }

    &-title {
      font-size: 14px;
      font-weight: 500;
      color: #1d4ed8;
      margin: 0 0 4px 0;
    }

    &-subtitle {
      font-size: 13px;
      color: #1989fa;
      margin: 0;
    }

    &-highlight {
      font-weight: 600;
    }
  }

  &__debt-details {
    margin-top: 16px;
  }

  &__buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-bottom: 24px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
  }
  &__section-margin {
    margin-bottom: 16px;
  }
  &__row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 16px;

    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }
  }
}
</style>
