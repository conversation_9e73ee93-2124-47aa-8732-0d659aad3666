<script setup lang="ts">
interface DebtDetail {
  inputDate: string
  lastTableBase: string
  tableBase: string
  gas: string
  money: string
  debtMoney: string
  overdue: string
  actuallyOwing: string
}
defineProps<{
  detail: DebtDetail
}>()
</script>

<template>
  <div class="debt-detail-card">
    <div class="debt-detail-card__header">
      <h4 class="debt-detail-card__title">
        {{ detail.inputDate }} 账单
      </h4>
      <span class="debt-detail-card__badge">
        欠费 ¥{{ detail.actuallyOwing }}
      </span>
    </div>

    <div class="debt-detail-card__grid">
      <div class="debt-detail-card__item">
        <p class="debt-detail-card__label">
          上期底数
        </p>
        <p class="debt-detail-card__value">
          {{ detail.lastTableBase }} m³
        </p>
      </div>
      <div class="debt-detail-card__item">
        <p class="debt-detail-card__label">
          本期底数
        </p>
        <p class="debt-detail-card__value">
          {{ detail.tableBase }} m³
        </p>
      </div>
      <div class="debt-detail-card__item">
        <p class="debt-detail-card__label">
          用气量
        </p>
        <p class="debt-detail-card__value">
          {{ detail.gas }} m³
        </p>
      </div>
      <div class="debt-detail-card__item">
        <p class="debt-detail-card__label">
          用气金额
        </p>
        <p class="debt-detail-card__value">
          ¥{{ detail.money }}
        </p>
      </div>
      <div class="debt-detail-card__item">
        <p class="debt-detail-card__label">
          已交金额
        </p>
        <p class="debt-detail-card__value">
          ¥{{ detail.debtMoney }}
        </p>
      </div>
      <div class="debt-detail-card__item">
        <p class="debt-detail-card__label">
          违约金
        </p>
        <p class="debt-detail-card__value">
          ¥{{ detail.overdue }}
        </p>
      </div>
      <div class="debt-detail-card__item">
        <p class="debt-detail-card__label">
          实欠金额
        </p>
        <p class="debt-detail-card__value debt-detail-card__value--blue">
          ¥{{ detail.actuallyOwing }}
        </p>
      </div>
      <div class="debt-detail-card__item">
        <p class="debt-detail-card__label">
          欠费状态
        </p>
        <p class="debt-detail-card__value debt-detail-card__value--red">
          未缴清
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.debt-detail-card {
  background-color: #fff;
  border: 1px solid #ebedf0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 12px;
    margin-bottom: 12px;
    border-bottom: 1px solid #ebedf0;
  }

  &__title {
    font-size: 14px;
    font-weight: 500;
    color: #323233;
    margin: 0;
  }

  &__badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px 10px;
    font-size: 12px;
    font-weight: 500;
    color: #1989fa;
    background-color: #e6f7ff;
    border-radius: 999px;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__item {
    display: flex;
    flex-direction: column;
  }

  &__label {
    font-size: 12px;
    color: #646566;
    margin: 0 0 4px 0;
  }

  &__value {
    font-size: 14px;
    font-weight: 500;
    color: #323233;
    margin: 0;

    &--blue {
      color: #1989fa;
    }

    &--red {
      color: #ee0a24;
    }
  }
}
</style>
