<script setup lang="ts">
import { getConfigByNameAsync } from 'af-mobile-client-vue3/src/services/api/common'
import { reactive } from 'vue'
import IotMeterValveControl from '@/components/IotMeterValveControl/IotMeterValveControl.vue'
import IotMeterValveControlXy from '@/components/IotMeterValveControl/IotMeterValveControlXy.vue'

const emit = defineEmits(['closeOperation', 'complete'])

let config = reactive({
  $globalProp: {
    tenantAlias: 'standard',
  },
})

onMounted(async () => {
  // 获取有欠费是否能换表的配置
  const configResponse = await getConfigByNameAsync('ChangeMeterConfig')
  config = Object.assign(config, configResponse)
})
</script>

<template>
  <!-- 咸阳特殊业务 目前表单没配置暂时只能这么处理 -->
  <div v-if="config?.$globalProp?.tenantAlias === 'Xianyang'" class="info-card">
    <IotMeterValveControlXy @complete="emit('complete', { status: true })" />
  </div>
  <div v-else class="info-card">
    <IotMeterValveControl @complete="emit('complete', { status: true })" />
  </div>
</template>
