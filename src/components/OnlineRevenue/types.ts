export interface Transaction {
  date: string
  type: string
  amount: string
  gas: string
}

export interface BusinessUser {
  f_userinfo_id: string
  user_version: number
  f_userfiles_id?: string
  f_userinfo_code: string
  f_user_name: string
  f_user_phone: string
  f_user_type: string
  f_address: string
  f_meter_type: string
  f_meternumber: string
  f_balance: number
  f_balance_amount: number
  f_user_state: string
  f_last_gas_date?: string
  f_meteread_date?: string
  f_card_id?: string
  f_isdecimal?: string
  f_topup_ceil?: number
  f_gas_decimal?: number
  f_fee_decimal?: number
  f_gas_type?: number
  f_project_money?: number
  f_alias?: string
  f_user_id?: string
  f_stairprice_id?: string
  f_user_balance?: number
  f_remanent_type?: number
  f_remanent_gas?: number
  f_remanent_money?: number
  f_remanent_price?: number
  f_collection_type?: string
  f_table_state?: string
  f_total_fee?: number
  f_price_id?: number
  version: number
  transactions?: {
    date: string
    type: string
    amount: string
    gas: string
  }[]
  f_hascard?: string
  f_share_times?: string
  f_gasbrand_id?: string | number
  f_meter_brand?: string
  f_tag?: string
  f_gasproperties?: string
  f_card_password?: string
  f_police_gas?: string | number
  f_overdr_lines?: string | number
  f_total_gas?: number
  f_fillcard_times?: number
  f_times?: number
  f_meterid?: string | number
  f_area_code?: string | number
  f_coding?: string | number
  f_support_purchase?: string
  ljgql?: number
  ljgqje?: number
  f_write_totalgas?: number
  f_write_totalfee?: number
  f_ispick_table?: string
  f_price_name?: string
  f_meter_base?: number
  f_balance_gas?: number
  f_capacity?: number
  f_price_type?: string
  f_stair_use?: number
  f_meter_book_num?: number
  f_orgid?: string
  f_orgname?: string
  f_imei?: string
  f_valve_state?: string
  f_network_valve?: string
  f_deduction_gas?: number
}

export type searchType = 'f_card_id' | 'f_userinfo_code' | 'f_user_name' | 'f_address' | 'f_user_phone' | 'f_userinfo_id' | 'f_meternumber'

export interface userSearchModel {
  type: searchType
  keyword: string
  keyword2?: string
  pageNo: number
  orgList: any[]
}

/**
 * 最后一次购气记录接口
 */
export interface LastCardChargeRecord {
  /** 记录ID */
  id: number
  /** 用户ID */
  f_user_id: string
  /** 预购气量 */
  f_pregas: number
  /** 预购金额 */
  f_preamount: number
  /** 实收金额 */
  f_collection: number
  /** 当前余额 */
  f_curbalance: number
  /** 余额 */
  f_balance: number
  /** 用户余额 */
  f_user_balance: number
  /** 支付方式 */
  f_payment: string
  /** 补气ID */
  f_fillgas_id: string | null
  /** 票据样式 */
  f_bill_style: string
  /** 撤销类型 */
  f_canceltype: string
  /** 交易日期 */
  f_date: string
  /** 操作员 */
  f_operator: string
  /** 流水号ID */
  f_serial_id: string | null
  /** 表底数 */
  f_meter_base: number
  /** 剩余气量 */
  f_remanent_gas: number
  /** 购气次数 */
  f_times: number
  /** 关联的补气记录 */
  fillgas: LastCardChargeRecord | null
}

/**
 * 购气撤销接口
 */
export interface GasRefund {
  /** 冲正记录id */
  record: number
  /** 冲正记录id */
  id: string
  /** 冲正记录类型 */
  type: string
}
