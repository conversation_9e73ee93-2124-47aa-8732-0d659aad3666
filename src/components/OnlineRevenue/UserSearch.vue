<script setup lang="ts">
import type { ConfigItem } from '@af-mobile-client-vue3/components/data/InfoDisplay/index.vue'
import type { BusinessUser, searchType, userSearchModel } from './types'
import type { CardReadResponse } from '@/services/cardService/types'
import InfoDisplay from '@af-mobile-client-vue3/components/data/InfoDisplay/index.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import { mobileUtil } from '@af-mobile-client-vue3/utils/mobileUtil'
import useUserStore from 'af-mobile-client-vue3/src/stores/modules/user'
import { showDialog, showToast } from 'vant'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { getUserInfo, getUserOrgList } from '@/services/api/business'
import { readCard } from '@/services/cardService'
import useBusinessStore from '@/stores/modules/business'

// 定义模式类型
type ModeType = 'search' | 'card' | 'meter' | 'nfc'

// 定义 props
interface Props {
  modes?: {
    showSearch?: boolean
    showCard?: boolean
    showMeter?: boolean
    showNFC?: boolean
  }
}

const props = withDefaults(defineProps<Props>(), {
  modes: () => ({
    showSearch: false,
    showCard: false,
    showMeter: false,
    showNFC: false,
  }),
})

const emit = defineEmits<{
  (e: 'viewUserDetail', user: BusinessUser, cardInfo?: CardReadResponse | null): void
  (e: 'closeUserDetail'): void
  (e: 'businessHandler', user: BusinessUser, cardInfo: CardReadResponse | null): void
}>()

// 内部模式配置
const internalModes = [
  { id: 'search' as ModeType, name: '搜索模式', icon: 'search', iconType: 'vanIcon' },
  { id: 'nfc' as ModeType, name: 'NFC识别', icon: 'material-symbols:wifi', iconType: 'iconIfy' },
  { id: 'card' as ModeType, name: '读卡模式', icon: 'i-fa6-regular-credit-card', iconType: 'i' },
  { id: 'meter' as ModeType, name: '扫码模式', icon: 'scan', iconType: 'vanIcon' },
]

// 获取启用的模式
const enabledModes = computed(() => {
  return internalModes.filter((mode) => {
    switch (mode.id) {
      case 'nfc':
        return props.modes.showNFC
      case 'search':
        return props.modes.showSearch
      case 'card':
        return props.modes.showCard
      case 'meter':
        return props.modes.showMeter
      default:
        return false
    }
  })
})

// 活动标签，根据启用的模式设置默认值
const activeTab = ref<ModeType>('search')

// 计算默认的活动标签
const defaultActiveTab = computed(() => {
  const firstEnabledMode = enabledModes.value[0]
  return firstEnabledMode ? firstEnabledMode.id : 'search'
})

// 在组件挂载时设置默认活动标签
onMounted(() => {
  activeTab.value = defaultActiveTab.value
})

// 搜索相关状态
const searchParams = ref<userSearchModel>({
  type: 'f_userinfo_code',
  keyword: '',
  keyword2: '',
  pageNo: 1,
  orgList: [],
})

// 设置活动标签
function setActiveTab(tab: ModeType) {
  // 检查该模式是否启用
  const mode = enabledModes.value.find(m => m.id === tab)
  if (mode) {
    activeTab.value = tab
    // 切换标签清空数据
    searchParams.value.keyword = ''
  }
}

const isSearching = ref(false)
const showNoUserFound = ref(false)
const usersList = ref<BusinessUser[]>([])
const isReading = ref(false)

// 搜索类型选择器
const showSearchTypePicker = ref(false)
const searchTypeOptions = [
  { text: '用户编号', value: 'f_userinfo_code', iconName: 'search', iconFunction: () => {} },
  { text: '用户姓名', value: 'f_user_name', iconName: 'search', iconFunction: () => {} },
  { text: '用户地址', value: 'f_address', iconName: 'search', iconFunction: () => {} },
  { text: '联系电话', value: 'f_user_phone', iconName: 'search', iconFunction: () => {} },
  { text: '表号', value: 'f_meternumber', iconName: 'search', iconFunction: () => {} },
]

// 搜索类型显示文本
const searchTypeText = computed(() => {
  const option = searchTypeOptions.find(option => option.value === searchParams.value.type)
  return option ? option.text : ''
})

// 获取当前搜索类型的图标配置
const currentSearchType = computed(() => {
  return searchTypeOptions.find(option => option.value === searchParams.value.type) || searchTypeOptions[0]
})

// 处理图标点击事件
function handleIconClick() {
  // if (currentSearchType.value.iconFunction) {
  //   currentSearchType.value.iconFunction()
  // }
}

// 加载更多相关状态
const isLoadingMore = ref(false)
const hasMoreData = ref(true)
const cardInfo = ref<CardReadResponse | null>(null)
const listContainerRef = ref<HTMLElement | null>(null)
const observerTarget = ref<HTMLElement | null>(null)
const orgList = ref([])
const currUser = useUserStore().getLogin().f

// 用户信息配置
let userInfoConfig = reactive([
  { label: '联系电话', field: 'f_user_phone' },
  { label: '用户类型', field: 'f_user_type' },
  { label: '用户地址', field: 'f_address', full: true },
  { label: '表具编号', field: 'f_meternumber' },
  { label: '账户余额', field: 'f_balance', format: value => `¥ ${value}` },
  { label: '气表类型', field: 'f_meter_type' },
  { label: '气表品牌', field: 'f_meter_brand' },
])

// 执行搜索
function searchUser(deleteCardInfo = true) {
  // 先对关键字进行trim操作并更新回model
  searchParams.value.keyword = searchParams.value.keyword.trim()

  if (searchParams.value.keyword === '') {
    showToast('请输入搜索关键词')
    return
  }

  useBusinessStore().clearBusinessInfo()

  if (deleteCardInfo) {
    searchParams.value.keyword2 = ''
    cardInfo.value = null
  }

  // 重置搜索相关状态
  usersList.value = []
  showNoUserFound.value = false
  searchParams.value.pageNo = 1 // 重新搜索时重置为第1页
  hasMoreData.value = true // 重置加载更多状态

  // 直接发起API请求，而不是设置isSearching
  const params = {
    type: searchParams.value.type,
    keyword: searchParams.value.keyword,
    keyword2: searchParams.value.keyword2,
    pageNo: searchParams.value.pageNo,
    orgList: orgList.value,
  }

  console.log('搜索用户：', params)
  isSearching.value = true

  getUserInfo(params)
    .then((res: any) => {
      console.log('搜索结果：', res)
      isSearching.value = false

      if (res && Array.isArray(res) && res.length > 0) {
        usersList.value = res
      }
      else {
        usersList.value = []
        showNoUserFound.value = true
      }
    })
    .catch((error) => {
      console.error('获取用户数据失败:', error)
      isSearching.value = false
      showNoUserFound.value = true
      showToast('获取用户数据失败，请重试')
    })
    .finally(() => {
      // 读卡之后清空参数
      if (!deleteCardInfo) {
        searchParams.value.keyword = ''
        searchParams.value.type = 'f_userinfo_code'
      }
    })
}

// 加载用户数据 (仅用于加载更多，不用于首次搜索)
function loadUsers() {
  // 防止重复请求
  if (isLoadingMore.value || isSearching.value) {
    console.log('请求已经在进行中，避免重复请求')
    return
  }

  isLoadingMore.value = true

  const params = {
    type: searchParams.value.type,
    keyword: searchParams.value.keyword,
    pageNo: searchParams.value.pageNo,
    orgList: orgList.value,
  }

  console.log('加载更多数据：', params)

  // 发起API请求
  getUserInfo(params)
    .then((res: any) => {
      console.log('加载更多结果：', res)
      isLoadingMore.value = false

      // 如果有数据则追加到列表中
      if (res && Array.isArray(res) && res.length > 0) {
        usersList.value = [...usersList.value, ...res]
      }
      else {
        // 如果没有更多数据
        hasMoreData.value = false
        if (usersList.value.length > 10) {
          showToast('没有更多数据了')
        }
      }
    })
    .catch((error) => {
      console.error('获取更多数据失败:', error)
      isLoadingMore.value = false
      showToast('获取更多数据失败，请重试')
    })
}

// 加载下一页
function loadNextPage() {
  if (!hasMoreData.value || isLoadingMore.value || isSearching.value)
    return

  searchParams.value.pageNo += 1
  loadUsers()
}

// 创建交叉观察器
let observer: IntersectionObserver | null = null

// 设置观察器
function setupIntersectionObserver() {
  // 确保先销毁之前的观察器
  if (observer) {
    observer.disconnect()
    observer = null
  }

  // 延迟执行，确保DOM已渲染
  setTimeout(() => {
    if (!observerTarget.value) {
      const target = document.querySelector('.observer-target')
      if (target) {
        observerTarget.value = target as HTMLElement
      }
    }

    // 创建新的观察器
    try {
      observer = new IntersectionObserver((entries) => {
        if (entries[0]?.isIntersecting && hasMoreData.value && !isLoadingMore.value && !isSearching.value) {
          loadNextPage()
        }
      }, {
        root: null, // 改用视口作为根元素，避免listContainerRef问题
        threshold: 0.1,
        rootMargin: '150px',
      })

      if (observerTarget.value) {
        observer.observe(observerTarget.value)
      }
    }
    catch (err) {
      console.error('创建观察器失败:', err)
    }
  }, 1000)
}

async function getOrgCondition() {
  orgList.value = [currUser.resources.orgid, ...await getUserOrgList()]
  console.log('orgList', orgList.value)
}

// 挂载时设置观察器
onMounted(() => {
  console.log('组件挂载完成，准备设置观察器')
  getOrgCondition()
  // 获取查询列表配置
  getConfigByNameAsync('查询列表字段配置Config').then(
    (res) => {
      userInfoConfig = res.value
    },
  )
  // 延迟设置观察器，确保DOM已渲染
  setTimeout(() => {
    setupIntersectionObserver()
  }, 1000)
})

// 卸载时清理观察器
onUnmounted(() => {
  console.log('组件卸载，清理观察器')
  if (observer) {
    observer.disconnect()
    observer = null
  }
})

// 监听用户列表变化，在列表更新后重新设置观察器
watch(usersList, (newVal) => {
  console.log('用户列表已更新，数量:', newVal.length)
  if (newVal.length > 0) {
    // 延迟设置观察器，等待DOM更新
    setTimeout(() => {
      console.log('列表更新后重新设置观察器')
      setupIntersectionObserver()
    }, 500)
  }
})

// 读卡操作
function readCardGen() {
  isReading.value = true
  usersList.value = []
  showNoUserFound.value = false

  readCard().then((res) => {
    // 如果没有读取到卡号
    if (!res.CardID) {
      showDialog({
        message: '该卡号未绑定用户',
      })
      isReading.value = false
      return
    }

    cardInfo.value = res
    isReading.value = false
    searchParams.value.keyword = cardInfo.value?.CardID || ''
    searchParams.value.type = 'f_card_id'
    searchParams.value.keyword2 = cardInfo.value?.Factory
    searchUser(false)
  })
}

// 查看用户详情
function viewUserDetail(user: BusinessUser) {
  emit('viewUserDetail', user, cardInfo.value)
}

// 打开业务办理
function openBusinessHandler(user: BusinessUser) {
  emit('businessHandler', user, cardInfo.value)
}

// 确认搜索类型选择
function onSearchTypeChange(value: any) {
  console.log('搜索类型选择：', value)
  if (value && value.selectedValues && value.selectedValues.length > 0) {
    searchParams.value.type = value.selectedValues[0]
  }
  else if (value && typeof value.value === 'string') {
    // 直接处理列选择的情况
    searchParams.value.type = value.value as searchType
  }
}

// 表号扫码操作
function scanMeter() {
  mobileUtil.execute({
    funcName: 'scanBarcode',
    param: {},
    callbackFunc: (result: any) => {
      if (result.status === 'success' && result.data) {
        searchParams.value.keyword = result.data.displayValue
        searchParams.value.type = 'f_meternumber'
        searchUser()
      }
    },
  })
}

function nfc() {
  showToast('nfc')
}
</script>

<template>
  <div class="user-search">
    <!-- 选项卡 -->
    <div v-if="enabledModes.length > 0" class="tab-header">
      <div
        v-for="mode in enabledModes"
        :key="mode.id"
        class="tab-item"
        :class="{ active: activeTab === mode.id }"
        @click="setActiveTab(mode.id)"
      >
        <Icon v-if="mode.iconType === 'iconIfy'" icon="material-symbols:wifi" :width="18" style="margin-right: 8px;" />
        <van-icon v-else-if="mode.iconType === 'vanIcon'" :name="mode.icon" class="tab-icon" />
        <i v-else-if="mode.iconType === 'i'" :class="mode.icon" class="tab-icon" />
        <span class="tab-text">{{ mode.name }}</span>
      </div>
    </div>

    <!-- 搜索模式内容 -->
    <div v-if="activeTab === 'search'" class="user-search__form">
      <div class="search-field">
        <label for="searchType" class="search-field__label">搜索类型</label>
        <van-field
          v-model="searchTypeText"
          readonly
          is-link
          name="searchType"
          label=""
          class="search-field__input"
          placeholder="请选择搜索类型"
          @click="showSearchTypePicker = true"
        />
        <van-popup v-model:show="showSearchTypePicker" position="bottom">
          <van-picker
            :columns="searchTypeOptions"
            show-toolbar
            title="选择搜索类型"
            :option-height="50"
            @change="onSearchTypeChange"
            @confirm="showSearchTypePicker = false"
            @cancel="showSearchTypePicker = false"
          />
        </van-popup>
      </div>
      <div class="search-field">
        <label for="searchKeyword" class="search-field__label">关键词</label>
        <van-field
          v-model="searchParams.keyword"
          name="searchKeyword"
          placeholder="请输入搜索关键词"
          class="search-field__input"
        >
          <template #right-icon>
            <van-icon
              :name="currentSearchType.iconName"
              class="search-icon"
              :class="{ 'scan-icon': currentSearchType.iconName === 'scan' }"
              @click="handleIconClick"
            />
          </template>
        </van-field>
      </div>
      <van-button
        type="primary"
        block
        class="search-button"
        @click="searchUser"
      >
        搜索用户
      </van-button>
    </div>

    <!-- 读卡模式内容 -->
    <div v-if="activeTab === 'card'" class="user-search__card-mode">
      <div class="card-reader">
        <div class="card-reader__icon">
          <van-icon name="card" />
        </div>
        <p class="card-reader__text">
          请将IC卡插入POS机读卡槽<br>点击下方按钮开始读取卡片信息
        </p>

        <div v-if="isReading" class="loading-bar">
          <div class="shimmer-effect" />
          <p class="loading-text">
            正在读取卡片数据，请勿移除卡片...
          </p>
        </div>

        <van-button type="primary" block class="card-button" @click="readCardGen">
          <i class="i-fa-solid-sync-alt" />
          读取卡片
        </van-button>
      </div>
    </div>

    <!-- 表号扫描模式内容 -->
    <div v-if="activeTab === 'meter'" class="user-search__meter-mode">
      <div class="meter-scanner">
        <div class="meter-scanner__icon">
          <Icon icon="solar:qr-code-bold" :width="48" style="color: rgb(22 163 74)" />
        </div>
        <p class="meter-scanner__text">
          请使用扫码枪或摄像头扫描表具上的二维码
          <br>
          系统将自动查找用户信息
        </p>

        <van-button type="primary" color="rgb(22 163 74)" block class="scan-button" @click="scanMeter">
          <div class="flex items-center space-x-2">
            <Icon icon="solar:qr-code-bold" :width="16" />
            <span style="margin-left: 5px">开始扫码</span>
          </div>
        </van-button>
      </div>
    </div>

    <!-- NFC -->
    <div v-if="activeTab === 'nfc'" class="user-search__meter-mode">
      <div class="meter-scanner">
        <div class="meter-scanner__icon">
          <Icon icon="material-symbols:wifi" :width="48" style="color: rgb(147 51 234)" />
        </div>
        <p class="meter-scanner__text">
          请将NFC设备靠近表具
          <br>
          系统将自动识别用户信息
        </p>

        <van-button type="primary" color="rgb(147 51 234)" block class="scan-button" @click="nfc">
          <div class="flex items-center space-x-2">
            <Icon icon="fa7-solid:sync-alt" :width="16" />
            <span style="margin-left: 5px">开始识别</span>
          </div>
        </van-button>
      </div>
    </div>

    <!-- 搜索加载指示器 -->
    <div v-if="isSearching" class="loading-container">
      <div class="shimmer-effect" />
      <p class="loading-text">
        正在搜索用户，请稍候...
      </p>
    </div>

    <!-- 用户档案列表 -->
    <div v-if="usersList.length > 0" ref="listContainerRef" class="user-list">
      <h3 class="user-list__title">
        查询结果 ({{ usersList.length }})
      </h3>

      <div
        v-for="user in usersList"
        :key="user.f_userinfo_code"
        class="user-card"
      >
        <div class="user-card__header">
          <div class="user-card__user-info">
            <div class="user-avatar">
              <van-icon name="contact" />
            </div>
            <div class="user-info">
              <h3 class="user-name">
                {{ user.f_user_name }}
              </h3>
              <p class="user-id">
                {{ user.f_userinfo_code }}
              </p>
            </div>
          </div>
          <div class="user-status">
            <span
              class="status-badge"
              :class="{
                'status-badge--normal': user.f_user_state === '正常',
                'status-badge--overdue': user.f_user_state === '欠费',
                'status-badge--suspended': user.f_user_state === '停用' || user.f_user_state === '销户',
              }"
            >
              {{ user.f_user_state }}
            </span>
          </div>
        </div>

        <div class="user-card__content">
          <InfoDisplay :config="userInfoConfig as ConfigItem[]" :data="user" />

          <div class="user-card__actions font-600">
            <van-button
              plain
              type="default"
              class="detail-button"
              @click="viewUserDetail(user)"
            >
              <i class="i-fa-info-circle" />
              详情
            </van-button>
            <van-button
              type="primary"
              class="business-button"
              @click="openBusinessHandler(user)"
            >
              <i class="i-fa-solid-file-invoice" />
              业务办理
            </van-button>
          </div>
        </div>
      </div>

      <!-- 加载更多区域 - 使用ID属性增加选择器可靠性 -->
      <div id="loadMoreTarget" ref="observerTarget" class="observer-target">
        <!-- 加载中状态 -->
        <div v-if="isLoadingMore" class="loading-more">
          <div class="shimmer-effect" />
          <p class="loading-text">
            加载更多数据中...
          </p>
        </div>

        <!-- 全部加载完毕提示 -->
        <div v-else-if="!hasMoreData && usersList.length > 0" class="no-more-data">
          没有更多数据了
        </div>

        <!-- 加载更多占位 -->
        <div v-else class="load-more-placeholder">
          <span class="load-more-text">上滑加载更多</span>
        </div>
      </div>
    </div>

    <!-- 未找到用户提示 -->
    <div v-if="showNoUserFound" class="not-found">
      <div class="not-found__icon">
        <van-icon name="user-circle-o" />
      </div>
      <h3 class="not-found__title">
        未找到用户
      </h3>
      <p class="not-found__text">
        没有找到符合条件的用户，请检查输入是否正确或尝试其他搜索条件。
      </p>
      <a class="not-found__link" @click="showNoUserFound = false">
        返回搜索
      </a>
    </div>
  </div>
</template>

<style lang="less" scoped>
/* 在样式部分的开头添加动画定义 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer-effect {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.user-search {
  width: 100%;

  &__form {
    padding: 16px;
    background-color: #fff;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    border: 1px solid #eee;
    border-top: none;
  }

  &__card-mode {
    padding: 16px;
    background-color: #fff;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    border: 1px solid #eee;
    border-top: none;
  }

  &__meter-mode {
    padding: 16px;
    background-color: #fff;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    border: 1px solid #eee;
    border-top: none;
  }
}

/* 选项卡样式 */
.tab-header {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
  border-bottom: none;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  position: relative;
  transition: color 0.3s;

  &.active {
    color: var(--van-primary-color);

    &:after {
      content: '';
      position: absolute;
      left: 20%;
      right: 20%;
      bottom: 0;
      height: 2px;
      background-color: var(--van-primary-color);
    }
  }
}

.tab-icon {
  margin-right: 4px;
  font-size: 16px;
}

.tab-text {
  font-weight: 600;
}

.search-field {
  margin-bottom: 16px;

  &__label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }

  &__input {
    border-radius: 8px;
  }
}

.search-button {
  height: 40px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 500;
  margin-top: 8px;
}

.card-reader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;

  &__icon {
    width: 80px;
    height: 80px;
    background-color: #f0f7ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    .van-icon {
      font-size: 40px;
      color: var(--van-primary-color);
    }
  }

  &__text {
    font-size: 14px;
    color: #666;
    text-align: center;
    line-height: 1.5;
    margin-bottom: 24px;
  }
}

.loading-bar {
  width: 100%;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-text {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  text-align: center;
}

.card-button {
  width: 100%;
  height: 40px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 600;

  .van-icon {
    margin-right: 4px;
    font-size: 16px;
  }
}

.loading-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 32px;
  text-align: center;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #eee;
}

.user-list {
  &__title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 16px 4px;
  }

  // 添加最大高度和滚动
  max-height: 75vh;
  overflow-y: auto;
  padding: 0 4px;
}

.user-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 16px;
  border: 1px solid #eee;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
  }

  &__user-info {
    display: flex;
    align-items: center;
  }

  &__content {
    padding: 16px;
  }

  &__actions {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
  }
}

.user-avatar {
  width: 40px;
  height: 40px;
  background-color: #e6f7ff;
  border-radius: 50%;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;

  .van-icon {
    font-size: 20px;
    color: var(--van-primary-color);
  }
}

.user-info {
  .user-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  .user-id {
    font-size: 12px;
    color: #999;
    margin: 0;
  }
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 100px;
  font-size: 12px;
  font-weight: 600;

  &--normal {
    background-color: #e6ffed;
    color: #23863a;
  }

  &--overdue {
    background-color: #fff1f0;
    color: #cf1322;
  }

  &--suspended {
    background-color: #fff7e6;
    color: #d46b08;
  }
}

.meter-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
  margin-bottom: 16px;
}

.detail-button {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  background-color: rgb(243 244 246 / 1);
  border-color: rgb(243 244 246 / 1);
  .van-icon {
    margin-right: 4px;
  }
}

.business-button {
  padding: 8px 24px;
  border-radius: 8px;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(25, 137, 250, 0.2);
  transform: scale(1);
  transition: transform 0.2s;

  &:active {
    transform: scale(1.05);
  }

  .van-icon {
    margin-right: 8px;
  }
}

.not-found {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 32px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #eee;

  &__icon {
    width: 64px;
    height: 64px;
    background-color: #f5f5f5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    .van-icon {
      font-size: 32px;
      color: #ccc;
    }
  }

  &__title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }

  &__text {
    font-size: 14px;
    color: #999;
    margin: 0 0 16px 0;
    line-height: 1.5;
  }

  &__link {
    color: var(--van-primary-color);
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
  }
}

.search-icon {
  font-size: 18px;
  color: #ccc;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: var(--van-primary-color);
  }
}

.scan-icon {
  font-size: 20px;
}

:deep(.van-field__control) {
  font-size: 14px;
}

:deep(.van-cell) {
  padding: 10px 16px;
}

:deep(.van-dropdown-menu__bar) {
  box-shadow: none;
}

.loading-more {
  width: 100%;
  padding: 16px;
  margin: 16px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.no-more-data {
  text-align: center;
  font-size: 14px;
  color: #999;
  padding: 16px;
  margin-bottom: 16px;
}

.observer-target {
  min-height: 50px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.load-more-placeholder {
  height: 50px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.load-more-text {
  color: #999;
  font-size: 14px;
}

.meter-scanner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;

  &__icon {
    width: 80px;
    height: 80px;
    background-color: #f0f7ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    .van-icon {
      font-size: 40px;
      color: var(--van-primary-color);
    }
  }

  &__text {
    font-size: 14px;
    color: #666;
    text-align: center;
    line-height: 1.5;
    margin-bottom: 24px;
  }
}

.scan-button {
  width: 100%;
  height: 40px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 600;

  .van-icon {
    margin-right: 4px;
    font-size: 16px;
  }
}
</style>
