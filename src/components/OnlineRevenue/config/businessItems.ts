// 定义业务类型
export type BusinessCategory = '收费类' | '卡务类' | '表务类' | '其它'
export type BusinessSection = '可用业务' | '其他业务' | '用户端业务'
export type MeterType = '气量卡表' | '金额卡表' | '物联网表' | '机表'

// 导出业务类别数组，用于循环渲染
export const businessCategories: BusinessCategory[] = ['收费类', '卡务类', '表务类', '其它']

export interface BusinessItem {
  name: string
  icon: string
  category: BusinessCategory
  section: BusinessSection
  bgColor: string
  iconColor: string
  forMeterTypes: MeterType[]
}

// 业务项列表
export const allBusinessItems: BusinessItem[] = [
  // 收费类业务
  {
    name: '发卡售气',
    icon: 'i-fa6-solid-credit-card',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-500',
    forMeterTypes: ['气量卡表', '金额卡表'],
  },
  {
    name: '卡表收费',
    icon: 'i-fa6-solid-money-bill-wave',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-600',
    forMeterTypes: ['气量卡表', '金额卡表'],
  },
  {
    name: '购气撤销',
    icon: 'i-fa-solid-undo-alt',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-red-100',
    iconColor: 'text-red-500',
    forMeterTypes: ['气量卡表', '金额卡表'],
  },
  {
    name: '掉气补气',
    icon: 'i-fluent-color-arrow-clockwise-dashes-32',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-500',
    forMeterTypes: ['气量卡表', '金额卡表'],
  },
  {
    name: '线下写卡',
    icon: 'i-fa6-solid-person-chalkboard',
    category: '卡务类',
    section: '可用业务',
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-500',
    forMeterTypes: ['气量卡表', '金额卡表'],
  },
  {
    name: '物联网表收费',
    icon: 'i-fa6-solid-money-bill-wave',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-500',
    forMeterTypes: ['物联网表'],
  },
  {
    name: '机表收费',
    icon: 'i-fa6-solid-money-bill-wave',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-500',
    forMeterTypes: ['机表'],
  },
  {
    name: '气费退款',
    icon: 'i-fa-solid-undo-alt',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600',
    forMeterTypes: ['物联网表', '机表'],
  },
  {
    name: '超用收费',
    icon: 'i-fa-solid-exclamation-circle',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-red-100',
    iconColor: 'text-red-500',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },
  {
    name: '在线抄表',
    icon: 'i-fa6-solid-chart-line',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-cyan-100',
    iconColor: 'text-cyan-600',
    forMeterTypes: ['物联网表'],
  },
  {
    name: '其他收费',
    icon: 'i-fa-solid-file-invoice-dollar',
    category: '收费类',
    section: '可用业务',
    bgColor: 'bg-gray-100',
    iconColor: 'text-gray-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },

  // 卡务类业务
  {
    name: '补卡',
    icon: 'i-fa6-solid-credit-card',
    category: '卡务类',
    section: '可用业务',
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600',
    forMeterTypes: ['气量卡表', '金额卡表'],
  },
  // {
  //   name: '换表补气',
  //   icon: 'i-fa-solid-gas-pump',
  //   category: '卡务类',
  //   bgColor: 'bg-purple-100',
  //   iconColor: 'text-purple-600',
  //   forMeterTypes: ['气量卡表', '金额卡表', '物联网表'],
  // },
  {
    name: '补气购气',
    icon: 'i-fa-solid-gas-pump',
    category: '卡务类',
    section: '可用业务',
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },
  {
    name: '转气',
    icon: 'i-fa-solid-random',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-teal-100',
    iconColor: 'text-teal-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },

  // 表务类业务
  {
    name: '换表',
    icon: 'i-fa-solid-exchange-alt',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-red-100',
    iconColor: 'text-red-500',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },
  {
    name: '气表升级',
    icon: 'i-carbon-upgrade',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-red-100',
    iconColor: 'text-red-500',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表'],
  },
  {
    name: '换控制器',
    icon: 'i-ic-outline-control-camera',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-red-100',
    iconColor: 'text-red-500',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表'],
  },
  {
    name: '换主板',
    icon: 'i-proicons-motherboard',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-red-100',
    iconColor: 'text-red-500',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表'],
  },
  {
    name: '换基表',
    icon: 'i-material-symbols-component-exchange-sharp',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-red-100',
    iconColor: 'text-red-500',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表'],
  },
  {
    name: '清零',
    icon: 'i-fa-solid-eraser',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },
  {
    name: '停用',
    icon: 'i-fa-solid-stop-circle',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-orange-100',
    iconColor: 'text-orange-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },
  {
    name: '启用',
    icon: 'i-fa-solid-play-circle',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-orange-100',
    iconColor: 'text-orange-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },
  {
    name: '表具参数设置',
    icon: 'i-fa:cogs',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-gray-100',
    iconColor: 'bg-white-100',
    forMeterTypes: ['物联网表'],
  },
  {
    name: '表具阀控',
    icon: 'i-gridicons:fire',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-gray-100',
    iconColor: 'bg-white-100',
    forMeterTypes: ['物联网表'],
  },
  {
    name: '周期开阀',
    icon: 'i-mdi:timer-lock-open-outline',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-gray-100',
    iconColor: 'text-white-600',
    forMeterTypes: ['物联网表'],
  },
  {
    name: '平台解绑',
    icon: 'i-meteor-icons:unlink',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-gray-100',
    iconColor: 'text-black-600',
    forMeterTypes: ['物联网表'],
  },
  {
    name: '指令下发',
    icon: 'i-carbon:transform-instructions',
    category: '表务类',
    section: '可用业务',
    bgColor: 'bg-gray-100',
    iconColor: 'text-black-600',
    forMeterTypes: ['物联网表'],
  },

  // 其它业务
  {
    name: '过户',
    icon: 'i-fa-solid-exchange-alt',
    category: '其它',
    section: '可用业务',
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },
  {
    name: '重新开户',
    icon: 'i-icon-park:open-an-account',
    category: '其它',
    section: '可用业务',
    bgColor: 'bg-green-100',
    iconColor: 'bg-white-100',
    forMeterTypes: ['物联网表'],
  },

  // 其他业务
  {
    name: 'NFC绑定',
    icon: 'i-material-symbols-wifi',
    category: '其它',
    section: '其他业务',
    bgColor: 'bg-indigo-100',
    iconColor: 'text-blue-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },
  {
    name: '扫码绑定',
    icon: 'i-material-symbols-qr-code',
    category: '其它',
    section: '其他业务',
    bgColor: 'bg-teal-100',
    iconColor: 'text-teal-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },

  // 用户端业务
  {
    name: '档案修正',
    icon: 'i-carbon:transform-instructions',
    category: '其它',
    section: '用户端业务',
    bgColor: 'bg-amber-100',
    iconColor: 'text-amber-600',
    forMeterTypes: ['气量卡表', '金额卡表', '物联网表', '机表'],
  },
]
