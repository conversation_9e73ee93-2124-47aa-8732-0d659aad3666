<script setup lang="ts">
import type { MeterType } from './config/businessItems'
import type { BusinessUser } from './types'
import { Button } from 'vant'
import { computed, ref } from 'vue'
import useBusinessStore from '@/stores/modules/business'
import InfoDisplayCard from '../common/infoDisplayCard/index.vue'
import InfoHeader from '../common/InfoHeader.vue'
import StairInfo from '../common/StairInfo.vue'

const props = defineProps<{
  user: BusinessUser
}>()

// 面板展开状态
const showDetails = ref(false)

// 获取用户表具类型
const userMeterType = computed((): MeterType => {
  return props.user.f_meter_type as MeterType
})

// 获取当前用户的阶梯信息
const currentUserStairInfo = computed(() => {
  const businessStore = useBusinessStore()
  return businessStore.currentUserStairInfo
})

// 根据用户状态返回样式类
const statusClass = computed(() => {
  // 映射用户状态
  const status = props.user.f_user_state || '正常'

  switch (status) {
    case '正常':
      return 'bg-green-50 text-green-600'
    case '销户':
      return 'bg-red-50 text-red-600'
    case '欠费':
      return 'bg-red-50 text-red-600'
    case '停用':
      return 'bg-yellow-50 text-yellow-600'
    default:
      return ''
  }
})

// 根据表具类型返回样式类
const meterTypeClass = computed(() => {
  switch (userMeterType.value) {
    case '气量卡表':
    case '金额卡表':
      return 'bg-blue-50 text-blue-500'
    case '物联网表':
      return 'bg-purple-50 text-purple-500'
    case '机表':
      return 'bg-orange-50 text-orange-500'
    default:
      return ''
  }
})

// 用户详细信息配置
const userInfoFields = computed(() => {
  const config = [
    { label: '联系电话', field: 'f_user_phone' },
    { label: '用户类型', field: 'f_user_type' },
    { label: '表具编号', field: 'f_meternumber' },
    { label: '最近购气日期', field: 'f_last_gas_date', format: (value: string) => value || '--' },
    { label: '卡号', field: 'f_card_id', condition: () => props.user.f_meter_type?.includes('卡表') || props.user.f_hascard === '是' },
    { label: '购气次数', field: 'f_times' },
    { label: '累购气量(m³)', field: 'f_total_gas', format: value => `${value}m³` },
    { label: '累购金额(元)', field: 'f_total_fee', format: value => `¥ ${value}` },
    { label: '阀门状态', field: 'f_valve_state', condition: () => props.user.f_meter_type?.includes('物联网表') },
    { label: '用户地址', field: 'f_address', full: true },
  ]
  return config
})
</script>

<template>
  <div class="user-profile bg-white shadow shadow-sm">
    <div class="user-profile-header border-gray-100">
      <div class="profile-left">
        <span class="profile-title text-gray-800">用户档案</span>
        <div class="profile-tags">
          <span class="status-tag" :class="statusClass">{{ props.user.f_user_state }}</span>
          <span class="meter-tag" :class="meterTypeClass">{{ userMeterType }}</span>
        </div>
      </div>
      <Button
        class="toggle-btn border-none bg-transparent"
        @click="showDetails = !showDetails"
      >
        <i :class="showDetails ? 'i-fa6-solid-chevron-up' : 'i-fa6-solid-chevron-down'" class="transition-transform duration-300" />
      </Button>
    </div>

    <div class="user-basic-info border-gray-100 bg-gray-50">
      <div class="user-avatar-info">
        <div class="user-avatar bg-blue-100">
          <i class="user-icon text-primary i-fa6-solid-user" />
        </div>
        <div class="user-meta">
          <span class="user-name text-gray-800">{{ props.user.f_user_name }}</span>
          <span class="user-id text-gray-500">{{ props.user.f_userinfo_code }}</span>
        </div>
      </div>
      <div
        v-if="!props.user.f_meter_type?.includes('物联网表')
          || (props.user.f_meter_type?.includes('物联网表') && props.user.f_user_balance > 0)" class="user-counters"
      >
        <div class="counter-item">
          <span class="counter-label text-gray-600">账户余额</span>
          <span class="counter-value text-orange-500">¥ {{ props.user.f_user_balance }}</span>
        </div>
      </div>
      <div v-if="props.user.f_meter_type?.includes('物联网表')" class="user-counters">
        <div class="counter-item">
          <span class="counter-label text-gray-600">表上余额</span>
          <span class="counter-value text-orange-500">¥ {{ props.user.f_balance_amount }}</span>
        </div>
      </div>
    </div>

    <!-- 用户详细信息（可折叠） -->
    <transition
      name="details-transition"
      enter-active-class="transition ease-out duration-300"
      enter-from-class="opacity-0 transform -translate-y-2"
      enter-to-class="opacity-100 transform translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 transform translate-y-0"
      leave-to-class="opacity-0 transform -translate-y-2"
    >
      <div v-show="showDetails" class="user-details-container px-4 py-2 overflow-hidden">
        <!-- 用户基本信息 -->
        <InfoDisplayCard
          title="用户基本信息"
          icon="i-fa6-solid-circle-user"
          theme="info"
          class="mb-10"
          :data="props.user"
          :fields="userInfoFields"
        />

        <InfoHeader
          class="mt-10"
          title="阶梯用气情况"
          icon="i-fa6-solid-chart-line"
          theme="magic"
        >
          <StairInfo
            :stair-info="currentUserStairInfo"
          />
        </InfoHeader>
      </div>
    </transition>
  </div>
</template>

<style lang="less" scoped>
/* 用户档案区域 */
.user-profile {
  position: sticky;
  z-index: 100;
  margin-bottom: 12px;
  transition: all 0.2s;
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}

.user-profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
}

.profile-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.profile-title {
  font-size: 15px;
  font-weight: 600;
}

.profile-tags {
  display: flex;
  gap: 6px;
}

.status-tag,
.meter-tag {
  padding: 2px 8px;
  border-radius: 9999px;
  font-size: 12px;
}

.toggle-btn {
  padding: 4px;
}

.user-basic-info {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  align-items: center;
}

.user-avatar-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  background-color: #dbeafe;
}

.user-icon {
  font-size: 24px;
  color: var(--van-primary-color);
}

.user-meta {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
}

.user-id {
  font-size: 12px;
}

.user-counters {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.counter-item {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.counter-label {
  font-size: 12px;
  margin-bottom: 2px;
}

.counter-value {
  font-size: 14px;
  font-weight: 500;
}

.user-details {
  padding: 12px;
}

.user-details-container {
  padding: 12px;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-blue-700 {
  color: #1d4ed8;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
</style>
