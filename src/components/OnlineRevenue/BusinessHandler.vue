<script setup lang="ts">
import type { BusinessCategory, MeterType } from './config/businessItems'
import type { BusinessUser } from './types'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { Button, Icon, Tab, Tabs } from 'vant'
import { computed, provide, ref } from 'vue'
import IotMeterOpenNewAccount from '@/components/IotMeterOpenNewAccount/index.vue'
import IotMeterParamsSet from '@/components/IotMeterParamsSet/index.vue'
import IotMeterPeriodicValveClosure from '@/components/IotMeterPeriodicValveClosure/index.vue'
import IotMeterPlatformUnbind from '@/components/IotMeterPlatformUnbind/index.vue'
import IotMeterValveControl from '@/components/IotMeterValveControl/index.vue'
import NotJinKaParamSet from '@/components/NotJinKaParamSet/index.vue'
import useBusinessStore from '@/stores/modules/business'
import cardMeterChargeCancel from '@/views/cardMeterChargeCancel/index.vue'
import CardReplacement from '@/views/cardReplacement/index.vue'
import GasCompensation from '@/views/gasCompensation/index.vue'
import GasTransfer from '@/views/gasTransfer/index.vue'
import MeterReplaceMent from '@/views/meterReplaceMent/meterReplaceMent.vue'
import meterReplaceMentHKZQ from '@/views/meterReplaceMent/meterReplaceMentHKZQ.vue'
import meterReplaceMentHZB from '@/views/meterReplaceMent/meterReplaceMentHZB.vue'
import meterReplaceMentJB from '@/views/meterReplaceMent/meterReplaceMentJB.vue'
import meterReplaceMentQBSJ from '@/views/meterReplaceMent/meterReplaceMentQBSJ.vue'
import meterReset from '@/views/meterReset/meterReset.vue'
import offGasAddGas from '@/views/offGasAddGas/index.vue'
import overUsageCharge from '@/views/overUsageCharge/index.vue'
import OwnershipTransfer from '@/views/ownershipTransfer/index.vue'
import ReturnPremium from '@/views/ReturnPremium/index.vue'
import statusChange from '@/views/statusChange/index.vue'
import unWriteCardCenter from '@/views/unWriteCardCenter/index.vue'
import CardPaymentForm from '../CardPayment'
import IotMeter from '../IotMeter'
import MechanicalMeterPayment from '../mechanical-meter'
import OtherChargeForm from '../OtherCharge'
import SellGasForm from '../SellGas'
import { allBusinessItems } from './config/businessItems'
import UserProfile from './UserProfile.vue'

const props = defineProps<{
  user: BusinessUser
  operations: string[]
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'operationComplete', operation: string, status: { status: boolean, readCard: boolean }): void
  (e: 'changeMeterType', type: MeterType): void
}>()

const businessStore = useBusinessStore()

// 当前激活的业务类别
const activeTab = ref<string>('')

// 选中的操作
const selectedOperation = ref<string | null>(null)

// 获取用户表具类型
const userMeterType = computed((): MeterType => {
  return props.user.f_meter_type as MeterType
})

// 获取所有可用的业务项
const availableBusinessItems = computed(() => {
  // 获取用户权限列表
  const userPermissions = useUserStore().getPermissions()
  // 特殊处理"换表&清零"权限
  const handleSpecialPermission = (itemName: string) => {
    // 如果用户有"换表&清零"权限，则也拥有"换表"和"清零"权限
    if (itemName === '换表' || itemName === '清零') {
      return userPermissions.includes(itemName) || userPermissions.includes('换表&清零')
    }
    // 其他权限正常判断
    return userPermissions.includes(itemName)
  }

  // 检查用户是否有"移除气表清零"权限
  // 咸阳特殊要求 / 苍南网表不要清零，暂时没办法通用处理
  // 后台获取按钮时有判断 返回 移除气表清零
  const hasRemoveMeterResetPermission = props.operations.includes('移除气表清零')

  // todo 其他业务 用户端业务 展示全部放开
  const filteredItems = allBusinessItems.filter(
    item =>
      item.section === '其他业务'
      || item.section === '用户端业务'
      || (
        item.forMeterTypes.includes(userMeterType.value)
        && props.operations.includes(item.name)
        && handleSpecialPermission(item.name)
        // 如果是"清零"操作且用户有"移除气表清零"权限，则不显示该操作
        && !(item.name === '清零' && hasRemoveMeterResetPermission)
      ),
  )

  // 如果满足条件且用户有购气撤销权限，则添加购气撤销业务项
  // 如果需要无卡撤销把这个注释掉就行了
  if (!(businessStore.canRefundGas && userPermissions.includes('购气撤销'))) {
    return filteredItems.filter(item => item.name !== '购气撤销')
  }

  return filteredItems
})

// 从可用业务项中动态获取分类
const availableCategories = computed<BusinessCategory[]>(() => {
  // 从已筛选的业务项中提取不重复的分类
  const categories = [...new Set(availableBusinessItems.value.map(item => item.category))]
  return categories as BusinessCategory[]
})

// 当前激活的业务类别（默认选择第一个可用分类）
const activeCategory = computed<BusinessCategory>({
  get() {
    // 如果当前活动分类不在可用分类中，则选择第一个可用分类
    if (activeTab.value && availableCategories.value.includes(activeTab.value as BusinessCategory)) {
      return activeTab.value as BusinessCategory
    }
    return availableCategories.value.length > 0 ? availableCategories.value[0] : '收费类'
  },
  set(value) {
    activeTab.value = value
  },
})

// 按section分组的业务项
const businessItemsBySection = computed(() => {
  const mainItems = availableBusinessItems.value.filter(item => item.section === '可用业务')
  const otherItems = availableBusinessItems.value.filter(item => item.section === '其他业务')
  const userItems = availableBusinessItems.value.filter(item => item.section === '用户端业务')

  return {
    main: mainItems,
    other: otherItems,
    user: userItems,
  }
})

// 可用业务项列表（main section）
const businessItems = computed(() => {
  // 根据表具类型和当前选中的业务类别过滤业务
  return businessItemsBySection.value.main.filter(
    item => item.category === activeCategory.value,
  )
})

// 其他业务项列表
const otherBusinessItems = computed(() => {
  return businessItemsBySection.value.other
})

// 用户端业务项列表
const userBusinessItems = computed(() => {
  return businessItemsBySection.value.user
})

// 选择业务操作
function selectOperation(operation: string) {
  selectedOperation.value = operation
  // 可以在这里添加业务逻辑处理
}

// 关闭当前业务操作
function closeOperation() {
  successOperation({
    status: false,
  })
}

// 关闭当前业务操作
function successOperation(status) {
  emit('operationComplete', selectedOperation.value, status)
  selectedOperation.value = null
}

// 根据选中的操作获取对应的业务项
const currentBusinessItem = computed(() => {
  if (!selectedOperation.value)
    return null

  return allBusinessItems.find(item => item.name === selectedOperation.value) || null
})

// 根据选中的操作返回对应的组件
function getOperationComponent(operation: string) {
  switch (operation) {
    case '补卡':
      return CardReplacement
    // case '换表补气':
    //   return GasCompensation
    case '补气购气':
      return GasCompensation
    case '转气':
      return GasTransfer
    case '过户':
      return OwnershipTransfer
    case '发卡售气':
      return SellGasForm
    case '机表收费':
      return MechanicalMeterPayment
    case '物联网表收费':
      return IotMeter
    case '卡表收费':
      return CardPaymentForm
    case '换表':
      return MeterReplaceMent
    case '换基表':
      return meterReplaceMentJB
    case '换主板':
      return meterReplaceMentHZB
    case '换控制器':
      return meterReplaceMentHKZQ
    case '气表升级':
      return meterReplaceMentQBSJ
    case '清零':
      return meterReset
    case '停用':
      return statusChange
    case '启用':
      return statusChange
    case '其他收费':
      return OtherChargeForm
    case '气费退款':
      return ReturnPremium
    case '超用收费':
      return overUsageCharge
    case '购气撤销':
      return cardMeterChargeCancel
    case '掉气补气':
      return offGasAddGas
    case '线下写卡':
      return unWriteCardCenter
    case '表具参数设置':
      return NotJinKaParamSet
    case '表具阀控':
      return IotMeterValveControl
    case '周期开阀':
      return IotMeterPeriodicValveClosure
    case '平台解绑':
      return IotMeterPlatformUnbind
    case '重新开户':
      return IotMeterOpenNewAccount
    case '指令下发':
      return IotMeterParamsSet
    case 'NFC绑定':
      return null
    // 添加其他业务组件的映射
    default:
      return null
  }
}

// 暴露出 选择业务函数出去
defineExpose({
  selectOperation,
})

// 作为祖父组件为子组件提供方法
provide('success', successOperation)
</script>

<template>
  <div class="business-handler">
    <!-- 用户档案信息卡片 -->
    <UserProfile
      :user="props.user"
    />
    <!-- 业务菜单 -->
    <div v-if="!selectedOperation" class="business-menu">
      <div class="menu-header">
        <div class="section-title">
          <div class="title-indicator" />
          <span>可用业务</span>
        </div>
      </div>

      <!-- 当没有可用业务操作时显示空状态提示 -->
      <div v-if="availableBusinessItems.length === 0" class="empty-business">
        <div class="empty-icon">
          <i class="i-fa6-solid-box text-6xl text-gray-300" />
        </div>
        <p class="empty-title">
          暂无可用业务
        </p>
        <p class="empty-description">
          当前用户类型或表具类型下没有可用的业务操作，或您没有相关权限
        </p>
      </div>

      <!-- 业务分类选项卡 -->
      <template v-else>
        <Tabs
          v-if="availableCategories.length > 1"
          v-model:active="activeTab"
          class="business-tabs"
          line-width="1.25rem"
          animated
          swipeable
        >
          <Tab
            v-for="category in availableCategories"
            :key="category"
            :name="category"
            title-class="category-tab-title"
          >
            <template #title>
              <div class="tab-title-with-icon">
                <i v-if="category === '收费类'" class="i-fa6-solid-money-bill-wave mr-1" />
                <i v-else-if="category === '卡务类'" class="i-fa6-solid-credit-card mr-1" />
                <i v-else-if="category === '表务类'" class="i-fa-exchange mr-1" />
                <i v-else-if="category === '其它'" class="i-fa6-solid-ellipsis mr-1" />
                {{ category }}
              </div>
            </template>
            <div class="business-items-grid">
              <div
                v-for="item in businessItems"
                :key="item.name"
                class="business-item"
                @click="selectOperation(item.name)"
              >
                <div class="business-icon" :class="[item.bgColor]">
                  <i :class="[item.icon, item.iconColor]" />
                </div>
                <span class="business-name">{{ item.name }}</span>
              </div>
            </div>
          </Tab>
        </Tabs>
        <div v-else class="business-items-grid">
          <div
            v-for="item in businessItems"
            :key="item.name"
            class="business-item"
            @click="selectOperation(item.name)"
          >
            <div class="business-icon" :class="[item.bgColor]">
              <i :class="[item.icon, item.iconColor]" />
            </div>
            <span class="business-name">{{ item.name }}</span>
          </div>
        </div>
      </template>
    </div>

    <!-- 其他业务卡片 -->
    <div v-if="!selectedOperation && otherBusinessItems.length > 0" class="business-menu">
      <div class="menu-header">
        <div class="section-title">
          <div class="title-indicator" />
          <span>其他业务</span>
        </div>
      </div>
      <div class="business-items-grid">
        <div
          v-for="item in otherBusinessItems"
          :key="item.name"
          class="business-item"
          @click="selectOperation(item.name)"
        >
          <div class="business-icon" :class="[item.bgColor]">
            <i :class="[item.icon, item.iconColor]" />
          </div>
          <span class="business-name">{{ item.name }}</span>
        </div>
      </div>
    </div>

    <!-- 用户端业务卡片 -->
    <div v-if="!selectedOperation && userBusinessItems.length > 0" class="business-menu">
      <div class="menu-header">
        <div class="section-title">
          <div class="title-indicator" />
          <span>用户端业务</span>
        </div>
      </div>
      <div class="business-items-grid">
        <div
          v-for="item in userBusinessItems"
          :key="item.name"
          class="business-item"
          @click="selectOperation(item.name)"
        >
          <div class="business-icon" :class="[item.bgColor]">
            <i :class="[item.icon, item.iconColor]" />
          </div>
          <span class="business-name">{{ item.name }}</span>
        </div>
      </div>
    </div>

    <!-- 业务操作界面 -->
    <!-- 动态渲染 -->
    <div
      v-if="selectedOperation"
      class="business-operation"
    >
      <div class="business-operation__header">
        <div class="business-operation__title">
          <i :class="`${currentBusinessItem?.icon ?? 'i-fa6-solid-scale-balanced'} text-white`" style="font-size: 20px;" />
          <h3 class="business-operation__title-text">
            {{ selectedOperation }}
          </h3>
        </div>
        <van-icon name="cross" @click="closeOperation" />
      </div>
      <div class="business-operation__content">
        <component
          :is="getOperationComponent(selectedOperation)"
          :user="props.user"
          @close-operation="closeOperation"
          @complete="successOperation"
        />
      </div>
    </div>

    <div v-if="selectedOperation && getOperationComponent(selectedOperation) === null" class="operation-container">
      <div class="operation-header">
        <div class="operation-title">
          <i :class="`${currentBusinessItem?.icon ? `i-${currentBusinessItem.icon}` : 'i-fa6-solid-file-lines'} operation-icon`" />
          <span>{{ selectedOperation }}</span>
        </div>
        <Button class="close-btn" @click="closeOperation">
          <Icon name="cross" />
        </Button>
      </div>
      <div class="operation-content">
        <!-- 这里可以根据selectedOperation动态加载不同的业务组件 -->
        <div class="placeholder-content">
          <Icon name="orders-o" size="48" class="placeholder-icon" />
          <p class="placeholder-text">
            {{ selectedOperation }} 功能将在此实现
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.business-handler {
  min-height: calc(100vh - 2.875rem);
  background-color: #f5f5f5;
  padding: 0rem 0.75rem 3.75rem;
}

/* 业务菜单 */
.business-menu {
  background-color: #ffffff;
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 0.0625rem solid #f0f0f0;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 0.9375rem;
  font-weight: 500;
  color: #333;
}

.title-indicator {
  width: 0.25rem;
  height: 1rem;
  background-color: var(--van-primary-color);
  border-radius: 0.125rem;
  margin-right: 0.5rem;
}

.meter-type-indicator {
  font-size: 0.8125rem;
  color: #666;

  .meter-type {
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
  }
}

.business-tabs {
  background-color: #f9f9f9;
}

:deep(.category-tab-title) {
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;

  &.van-tab--active {
    color: var(--van-primary-color);
  }
}

.tab-title-with-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.mr-1 {
  margin-right: 0.25rem;
}

.business-items-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 0.5rem;
  padding: 1rem;
  background-color: #ffffff;
}

.business-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.625rem 0.375rem;
  border-radius: 0.5rem;
  background-color: #ffffff;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition:
    transform 0.2s,
    box-shadow 0.2s;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.1);
  }
}

.business-icon {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.375rem;

  .van-icon {
    font-size: 1.125rem;
  }
}

.business-name {
  font-size: 0.75rem;
  color: #333;
  text-align: center;
}

/* 业务操作界面 */
.operation-container {
  background-color: #ffffff;
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-top: -1rem; /* 增加负边距使组件更近 */
}

.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 0.0625rem solid #f0f0f0;
}

.operation-title {
  display: flex;
  align-items: center;
  font-size: 0.9375rem;
  font-weight: 500;
  color: #333;

  .operation-icon {
    margin-right: 0.5rem;
    color: var(--van-primary-color);
  }
}

.close-btn {
  padding: 0.25rem;
  background: transparent;
  border: none;
}

.operation-content {
  padding: 1.5rem;
  min-height: 12.5rem;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem 1rem;
}

.placeholder-icon {
  color: #ddd;
  margin-bottom: 1rem;
}

.placeholder-text {
  font-size: 0.875rem;
  color: #999;
  text-align: center;
}

/* 开发工具 */
.dev-tools {
  background-color: #ffffff;
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.05);
  overflow: hidden;
  padding: 0.75rem 1rem;
}

.dev-tools-header {
  font-size: 0.8125rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.meter-switch-btn {
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  background-color: #f5f5f5;
  border: 0.0625rem solid #ddd;
  font-size: 0.8125rem;
  color: #666;
  cursor: pointer;

  &:hover {
    background-color: #e8f4ff;
    border-color: var(--van-primary-color);
    color: var(--van-primary-color);
  }
}

.empty-business {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
  background-color: #ffffff;
}

.empty-icon {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;

  &__image {
    color: #c0c4cc;
  }
}

.empty-title {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
}

.empty-description {
  font-size: 0.875rem;
  color: #909399;
  max-width: 20rem;
  line-height: 1.5;
}

.business-operation {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--van-primary-color);
    color: #fff;
  }

  &__title {
    display: flex;
    align-items: center;
  }

  &__title-text {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 0 8px;
  }

  &__content {
    padding: 16px;
  }
}
</style>
