<script setup lang="ts">
import { computed } from 'vue'

// 定义主题类型
type ThemeType = 'info' | 'warn' | 'error' | 'magic'

const props = defineProps<{
  title: string
  icon?: string
  theme?: ThemeType
}>()

// 设置默认主题
const theme = computed(() => props.theme || 'info')

// 根据主题返回样式
const themeClass = computed(() => {
  switch (theme.value) {
    case 'info':
      return {
        bg: 'bg-blue-50',
        border: 'border-blue-100',
        text: 'text-blue-700',
        icon: 'text-blue-500',
      }
    case 'warn':
      return {
        bg: 'bg-yellow-50',
        border: 'border-yellow-100',
        text: 'text-yellow-700',
        icon: 'text-yellow-500',
      }
    case 'error':
      return {
        bg: 'bg-red-50',
        border: 'border-red-100',
        text: 'text-red-700',
        icon: 'text-red-500',
      }
    case 'magic':
      return {
        bg: 'bg-purple-50',
        border: 'border-purple-100',
        text: 'text-purple-700',
        icon: 'text-purple-500',
      }
    default:
      return {
        bg: 'bg-blue-50',
        border: 'border-blue-100',
        text: 'text-blue-700',
        icon: 'text-blue-500',
      }
  }
})
</script>

<template>
  <div class="mb-3 rounded-xl bg-white shadow-sm overflow-hidden">
    <div class="px-10 py-2 flex items-center" :class="[themeClass.bg, themeClass.border]">
      <i v-if="props.icon" class="mr-4" :class="[props.icon, themeClass.icon]" />
      <h4 class="text-sm font-bold" :class="themeClass.text">
        {{ props.title }}
      </h4>
    </div>
    <slot />
  </div>
</template>

<style lang="less" scoped>
.text-purple-500 {
  color: #8b5cf6;
}

.text-purple-700 {
  color: #7e22ce;
}

.bg-purple-50 {
  background-color: #f5f3ff;
}

.border-purple-100 {
  border-color: #ede9fe;
}

.text-sm {
  font-size: 12px;
  line-height: 35px;
}

.rounded-xl {
  border-radius: 12px;
}
</style>
