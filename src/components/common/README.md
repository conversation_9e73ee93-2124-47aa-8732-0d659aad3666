# 通用组件库

这个文件夹包含了一系列通用组件，可以在不同的业务场景中复用。所有组件都使用 Vue3 组合式 API 和 Vant UI 组件库开发。

## 组件列表

### CardContainer

卡片容器组件，用于包裹内容，提供统一的卡片样式。

**Props:**
- `shadow`：是否显示阴影效果，默认为 `true`
- `className`：额外的自定义 CSS 类名，默认为空字符串

**使用示例:**
```vue
<CardContainer>
  <div>卡片内容</div>
</CardContainer>

<CardContainer :shadow="false" className="custom-card">
  <div>无阴影的自定义卡片</div>
</CardContainer>
```

### CardHeader

卡片标题头部组件，通常与 CardContainer 一起使用，提供标题和图标显示。

**Props:**
- `title`：标题文本，必须
- `icon`：图标类名（FontAwesome），默认为空字符串

**插槽:**
- `icon`：自定义图标内容
- `extra`：右侧额外内容区域

**使用示例:**
```vue
<CardHeader title="标题文本" icon="fa-home" />

<CardHeader title="自定义图标">
  <template #icon>
    <van-icon name="setting-o" />
  </template>
  <template #extra>
    <van-button size="mini">操作</van-button>
  </template>
</CardHeader>
```

### FormField

表单字段组件，提供标签和输入字段的布局。

**Props:**
- `label`：字段标签文本，默认为空字符串
- `hint`：提示信息，默认为空字符串

**使用示例:**
```vue
<FormField label="用户名">
  <van-field v-model="username" placeholder="请输入用户名" />
</FormField>

<FormField label="密码" hint="密码长度需要在8-20位之间">
  <van-field v-model="password" type="password" placeholder="请输入密码" />
</FormField>
```

### InfoCard

信息卡片组件，用于展示提示信息。

**Props:**
- `type`：卡片类型，可选值为 `info`, `success`, `warning`, `error`，默认为 `info`
- `mainText`：主要文本内容，默认为空字符串
- `subText`：次要文本内容，默认为空字符串

**使用示例:**
```vue
<InfoCard
  type="info"
  mainText="这是一条信息提示"
  subText="详细说明文本"
/>

<InfoCard type="warning">
  <p>自定义警告内容</p>
  <p>可以使用HTML标签</p>
</InfoCard>
```

### PaymentMethodSelector

支付方式选择器组件，用于选择支付方式。

**Props:**
- `modelValue`：选中的支付方式值，必须
- `methods`：支付方式列表，默认提供现金、微信、支付宝和银联四种支付方式
- `icon`：组件图标，默认为 `fa-credit-card`

**Events:**
- `update:modelValue`：选中值变更事件

**使用示例:**
```vue
<PaymentMethodSelector v-model="paymentMethod" />

<PaymentMethodSelector
  v-model="paymentMethod"
  :methods="[
    { label: '现金缴费', value: 'cash', icon: 'fas fa-money-bill-wave', colorType: 'green' },
    { label: '微信支付', value: 'wechat', icon: 'fab fa-weixin', colorType: 'green' }
  ]"
/>
```

### FileUploader

文件上传组件，支持图片预览和文件列表展示。

**Props:**
- `title`：组件标题，默认为 `上传附件`
- `icon`：组件图标，默认为 `fa-upload`
- `fileList`：已上传文件列表
- `showFileList`：是否显示文件列表，默认为 `true`
- `multiple`：是否支持多文件上传，默认为 `false`
- `accept`：接受的文件类型，默认为空
- `maxSize`：最大文件大小（字节），默认为 10MB
- `allowedTypes`：允许的文件类型数组，默认为 PNG、JPG 和 PDF

**Events:**
- `update:fileList`: 文件列表更新时触发
- `fileAdded`: 添加文件时触发，参数为 `{file, fileItem}`
- `fileRemoved`: 移除文件时触发，参数为 `fileItem`

**使用示例:**
```vue
<FileUploader
  v-model:file-list="files"
  :multiple="true"
  @fileAdded="handleFileAdded"
  @fileRemoved="handleFileRemoved"
/>

<FileUploader
  v-model:fileList="files"
  title="上传图片"
  :multiple="true"
  accept="image/*"
  :maxSize="5242880"
  :allowedTypes="['image/png', 'image/jpeg']"
  @file-added="handleFileAdded"
  @file-removed="handleFileRemoved"
/>
```

### ReceiptModal

收据弹窗组件，用于展示电子凭证。

**Props:**
- `show`：是否显示弹窗，必须
- `title`：弹窗标题，默认为 `电子凭证`
- `subTitle`：副标题，默认为空字符串
- `showSaveButton`：是否显示保存按钮，默认为 `true`
- `saveButtonText`：保存按钮文本，默认为 `保存截图`
- `closeButtonText`：关闭按钮文本，默认为 `完成`

**Events:**
- `update:show`：显示状态变更事件
- `save`：保存事件
- `close`：关闭事件

**插槽:**
- `default`：弹窗内容
- `header`：自定义标题区域
- `footer`：自定义底部按钮区域

**使用示例:**
```vue
<ReceiptModal
  v-model:show="showReceipt"
  title="订单凭证"
  @save="saveReceipt"
  @close="closeReceipt"
>
  <div>收据内容</div>
</ReceiptModal>
```

## 全局注册

这些组件也可以通过全局注册方式使用：

```js
// main.ts
import { createApp } from 'vue'
import App from './App.vue'
import CommonComponents from './components/common'

const app = createApp(App)
app.use(CommonComponents)
app.mount('#app')
```
