<script setup lang="ts">
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { computed, ref } from 'vue'
import useBusinessStore from '@/stores/modules/business'

interface PaymentMethod {
  label: string
  value: string
  icon: string
  color: string
}

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: '支付方式',
  },
  allowedMethods: {
    type: Array as () => string[],
    default: null,
  },
  icon: {
    type: String,
    default: 'fa-credit-card',
  },
})

const emit = defineEmits(['update:modelValue'])
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
const defaultMethods: PaymentMethod[] = [
  {
    value: '现金缴费',
    label: '现金缴费',
    icon: 'i-fa6-solid-money-bill-wave',
    color: 'red',
  },
  {
    value: '扫码支付',
    label: '扫码支付',
    icon: 'i-fa6-solid-qrcode',
    color: 'blue',
  },
  {
    value: '微信支付',
    label: '微信支付',
    icon: 'i-fa-brands-weixin',
    color: 'green',
  },
  {
    value: '支付宝支付',
    label: '支付宝支付',
    icon: 'i-fa-brands-alipay',
    color: 'blue',
  },
]

const displayMethods = computed(() => {
  if (userInfo.value.f_orgid === '1767') {
    return defaultMethods.filter(method =>
      method.value === '微信支付',
    )
  }
  else {
    if (!props.allowedMethods) {
      return defaultMethods
    }
    return defaultMethods.filter(method =>
      props.allowedMethods.includes(method.value),
    )
  }
})

function handleSelect(value: string) {
  console.log(value)
  emit('update:modelValue', value)
}
</script>

<template>
  <div class="payment-method-selector">
    <div class="payment-method-selector__header">
      <CardHeader :title="title" />
    </div>
    <div class="payment-method-selector__body">
      <div class="payment-method-selector__grid">
        <div
          v-for="method in displayMethods"
          :key="method.value"
          class="payment-method-selector__item"
          :class="{ 'payment-method-selector__item--active': modelValue === method.value }"
          @click="handleSelect(method.value)"
        >
          <div class="payment-method-selector__icon-wrap" :class="`payment-method-selector__icon-wrap--${method.color}`">
            <div class="payment-method-selector__icon" :class="[method.icon]" />
          </div>
          <span class="payment-method-selector__label" :class="{ 'payment-method-selector__label--active': modelValue === method.value }">
            {{ method.label }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.payment-method-selector {
  &__header {
    margin-bottom: 12px;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;

    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__item {
    display: flex;
    align-items: center;
    padding: 8px;
    cursor: pointer;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #fff;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f9fafb;
    }

    &--active {
      background-color: #ebf5ff;
      border-color: #3b82f6;
      box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
    }
  }

  &__icon-wrap {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    flex-shrink: 0;

    &--green {
      background-color: #dcfce7;

      .payment-method-selector__icon {
        color: #16a34a;
      }
    }

    &--blue {
      background-color: #dbeafe;

      .payment-method-selector__icon {
        color: #2563eb;
      }
    }

    &--red {
      background-color: #fff0f0;

      .payment-method-selector__icon {
        color: #d32f2f;
      }
    }
  }

  &__icon {
    width: 16px;
    height: 16px;
    display: inline-block;
  }

  &__label {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;

    &--active {
      color: #2563eb;
    }
  }
}
</style>
