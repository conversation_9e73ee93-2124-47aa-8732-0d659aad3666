<script setup lang="ts">
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: string
  remarks: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'update:remarks', value: string): void
}>()

const printOptions = [
  { text: '电子发票', value: '电子发票' },
  { text: '普通收据', value: '普通收据' },
  { text: '两者都需要', value: 'both' },
]

const selectedOption = ref(getOptionText(props.modelValue))
const remarks = ref(props.remarks)
const showPicker = ref(false)

function getOptionText(value: string): string {
  const option = printOptions.find(opt => opt.value === value)
  return option ? option.text : ''
}

function onConfirm({ selectedValues }: { selectedValues: string[] }) {
  selectedOption.value = selectedValues[0]
  emit('update:modelValue', selectedValues[0])
  showPicker.value = false
}

watch(() => remarks.value, (newValue) => {
  emit('update:remarks', newValue)
})

// 当从外部更新值时更新内部状态
watch(() => props.modelValue, (newValue) => {
  selectedOption.value = getOptionText(newValue)
})

watch(() => props.remarks, (newValue) => {
  remarks.value = newValue
})
</script>

<template>
  <CardContainer class="charge-print-and-remarks">
    <CardHeader title="打印及备注" />
    <div class="charge-print-options">
      <div class="charge-print-options__section">
        <label class="charge-print-options__label">收据打印选项</label>
        <div class="charge-print-options__selector">
          <van-field
            v-model="selectedOption"
            is-link
            readonly
            placeholder="请选择打印方式"
            @click="showPicker = true"
          />
        </div>
      </div>

      <div class="charge-print-options__section">
        <label class="charge-print-options__label">备注</label>
        <div class="charge-print-options__selector">
          <van-field
            v-model="remarks"
            type="textarea"
            placeholder="可选填写备注信息"
            rows="2"
            autosize
            maxlength="200"
            show-word-limit
          />
        </div>
      </div>

      <van-popup v-model:show="showPicker" position="bottom" round teleport="body">
        <van-picker
          :columns="printOptions"
          show-toolbar
          title="选择打印方式"
          @confirm="onConfirm"
          @cancel="showPicker = false"
        />
      </van-popup>
    </div>
  </CardContainer>
</template>

<style scoped lang="less">
.charge-print-and-remarks {
  margin-bottom: 16px;
}

.charge-print-options {
  &__section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 4px;
  }

  &__selector {
    position: relative;

    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  :deep(.van-field__control) {
    background-color: #f9fafb;
    border-radius: 6px;
  }
}
</style>
