<script setup lang="ts">
import InfoHeader from '../InfoHeader.vue'

// 定义字段配置接口
interface FieldConfig {
  label: string
  field: string
  full?: boolean
  format?: (value: any) => string
  condition?: (data: any) => boolean
}

const props = defineProps<{
  title: string
  icon?: string
  theme?: 'info' | 'warn' | 'error' | 'magic'
  data: Record<string, any>
  fields: FieldConfig[]
}>()

// 获取字段值并应用格式化
function getFieldValue(field: FieldConfig) {
  const value = props.data[field.field]
  return field.format ? field.format(value) : (value || '--')
}
</script>

<template>
  <InfoHeader
    :title="props.title"
    :icon="props.icon"
    :theme="props.theme"
  >
    <div class="p-7 gap-8 grid grid-cols-2">
      <div
        v-for="(field, index) in props.fields.filter(item => item.condition ? item.condition(props.data) : true)"
        :key="index"
        :class="{ 'col-span-2': field.full }"
        class="px-8 py-4 rounded-md bg-gray-50"
      >
        <p class="text-xs text-gray-500 mb-0.5">
          {{ field.label }}
        </p>
        <p class="text-sm text-gray-800 font-bold">
          {{ getFieldValue(field) }}
        </p>
      </div>
    </div>
  </InfoHeader>
</template>

<style lang="less" scoped>
.text-sm {
  font-size: 12px;
  line-height: 22px;
}

.text-xs {
  font-size: 12px;
  line-height: 22px;
}

.rounded-xl {
  border-radius: 12px;
}
</style>
