/**
 * 阶梯用气数据接口
 */
export interface StairUserData {
  f_stair1ceiling?: string | number
  f_stair1amount?: string | number
  f_stair1surplus?: string | number
  f_stair1price?: string | number
  f_stair2ceiling?: string | number
  f_stair2amount?: string | number
  f_stair2surplus?: string | number
  f_stair2price?: string | number
  f_stair3amount?: string | number
  f_stair3price?: string | number
  chargePrice?: any[]
  stairBeginDate?: string
  stairEndDate?: string
  f_stair3ceiling?: string | number
  f_stair3surplus?: string | number
  f_stair4ceiling?: string | number
  f_stair4amount?: string | number
  f_stair4surplus?: string | number
  f_stair4price?: string | number
  sumAmount?: string | number
}
