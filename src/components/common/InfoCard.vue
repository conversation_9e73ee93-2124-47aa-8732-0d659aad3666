<script setup lang="ts">
import { computed } from 'vue'

type InfoCardType = 'info' | 'success' | 'warning' | 'error'

const props = defineProps({
  type: {
    type: String as PropType<InfoCardType>,
    default: 'info',
  },
  mainText: {
    type: String,
    default: '',
  },
  subText: {
    type: String,
    default: '',
  },
})

const iconClass = computed(() => {
  const icons = {
    info: 'i-fa-info-circle',
    success: 'i-fa-check-circle',
    warning: 'i-fa-exclamation-triangle',
    error: 'i-fa-times-circle',
  }
  return icons[props.type] || icons.info
})
</script>

<template>
  <div class="info-card" :class="`info-card--${type}`">
    <div class="info-card__icon">
      <i class="fas" :class="iconClass" />
    </div>
    <div class="info-card__content">
      <slot>
        <p v-if="mainText" class="info-card__main">
          {{ mainText }}
        </p>
        <p v-if="subText" class="info-card__sub">
          {{ subText }}
        </p>
      </slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
.info-card {
  display: flex;
  align-items: flex-start;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;

  &__icon {
    font-size: 18px;
    margin-right: 12px;
    margin-top: 2px;
  }

  &__content {
    flex: 1;
  }

  &__main {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 4px 0;
  }

  &__sub {
    font-size: 13px;
    font-weight: 600;
    margin: 4px 0 0 0;
  }

  // 类型样式
  &--info {
    background-color: #e6f7ff;
    border-left: 4px solid var(--van-primary-color);

    .info-card__icon {
      color: var(--van-primary-color);
    }

    .info-card__main {
      color: #0050b3;
    }

    .info-card__sub {
      color: var(--van-primary-color);
    }
  }

  &--success {
    background-color: #f6ffed;
    border-left: 4px solid var(--van-success-color);

    .info-card__icon {
      color: var(--van-success-color);
    }

    .info-card__main {
      color: #135200;
    }

    .info-card__sub {
      color: var(--van-success-color);
    }
  }

  &--warning {
    background-color: #fffbe6;
    border-left: 4px solid var(--van-warning-color);

    .info-card__icon {
      color: var(--van-warning-color);
    }

    .info-card__main {
      color: #613400;
    }

    .info-card__sub {
      color: var(--van-warning-color);
    }
  }

  &--error {
    background-color: #fff2f0;
    border-left: 4px solid var(--van-danger-color);

    .info-card__icon {
      color: var(--van-danger-color);
    }

    .info-card__main {
      color: #5c0011;
    }

    .info-card__sub {
      color: var(--van-danger-color);
    }
  }
}
</style>
