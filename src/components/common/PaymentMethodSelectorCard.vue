<script setup lang="ts">
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import PaymentMethodSelector from './PaymentMethodSelector.vue'

defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: '支付方式',
  },
  allowedMethods: {
    type: Array as () => string[],
    default: null,
  },
  className: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

function handleUpdate(value: string) {
  emit('update:modelValue', value)
}
</script>

<template>
  <CardContainer :class="className">
    <PaymentMethodSelector
      :model-value="modelValue"
      :title="title"
      :allowed-methods="allowedMethods"
      @update:model-value="handleUpdate"
    />
  </CardContainer>
</template>
