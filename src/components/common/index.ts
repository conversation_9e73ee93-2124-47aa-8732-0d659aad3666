import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import ChargePrintSelectorAndRemarks from './ChargePrintSelectorAndRemarks.vue'
import CodePayment from './CodePayment.vue'
import FileUploader from './FileUploader.vue'
import FormField from './FormField.vue'
import GridFileUploader from './GridFileUploader.vue'
import InfoCard from './InfoCard.vue'
import PaymentMethodSelector from './PaymentMethodSelector.vue'
import PaymentMethodSelectorCard from './PaymentMethodSelectorCard.vue'
import ReceiptModal from './ReceiptModal.vue'

export {
  CardContainer,
  CardHeader,
  ChargePrintSelectorAndRemarks,
  CodePayment,
  FileUploader,
  FormField,
  GridFileUploader,
  InfoCard,
  PaymentMethodSelector,
  PaymentMethodSelectorCard,
  ReceiptModal,
}

export default {
  install(app: any) {
    app.component('CardContainer', CardContainer)
    app.component('CardHeader', CardHeader)
    app.component('FormField', FormField)
    app.component('InfoCard', InfoCard)
    app.component('PaymentMethodSelector', PaymentMethodSelector)
    app.component('PaymentMethodSelectorCard', PaymentMethodSelectorCard)
    app.component('ChargePrintSelectorAndRemarks', ChargePrintSelectorAndRemarks)
    app.component('FileUploader', FileUploader)
    app.component('GridFileUploader', GridFileUploader)
    app.component('ReceiptModal', ReceiptModal)
  },
}
