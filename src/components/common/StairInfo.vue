<script setup lang="ts">
import type { StairUserData } from './types'
import { computed } from 'vue'

interface TierData {
  tierName: string
  price: string
  used: number
  total: number
  remaining: string | number
  color: string
  ringClass: string
}

const props = defineProps<{
  stairInfo: StairUserData
}>()

// 当前用户阶梯等级
const currentTier = computed(() => {
  const chargePriceLength = props.stairInfo.chargePrice?.length || 0
  if (chargePriceLength >= 4)
    return 4
  if (chargePriceLength >= 3)
    return 3
  if (chargePriceLength >= 2)
    return 2
  return 1
})

// 阶梯数据
const tiersData = computed<TierData[]>(() => {
  const data = props.stairInfo
  const result: TierData[] = []
  const totalTiers = data.chargePrice?.length || 0

  // 第一阶梯
  const tier1Total = Number(data.f_stair1ceiling || 0)
  const tier1Remaining = Number(data.f_stair1surplus || 0)
  const tier1Used = tier1Total - tier1Remaining // 使用总量减去剩余量计算用量
  const tier1Price = data.f_stair1price ? `¥${Number(data.f_stair1price).toFixed(2)}/m³` : '¥0.00/m³'

  result.push({
    tierName: '第一阶梯',
    price: tier1Price,
    used: tier1Used,
    total: tier1Total,
    remaining: tier1Remaining,
    color: 'green',
    ringClass: 'ring-green-500',
  })

  // 检查是否有第二阶梯的价格
  if (data.f_stair2price) {
    // 第二阶梯
    const tier2Total = Number(data.f_stair2ceiling || 0) - tier1Total
    const tier2Remaining = Number(data.f_stair2surplus || 0)
    const tier2Used = tier2Total - tier2Remaining // 使用总量减去剩余量计算用量
    const tier2Price = data.f_stair2price ? `¥${Number(data.f_stair2price).toFixed(2)}/m³` : '¥0.00/m³'

    result.push({
      tierName: '第二阶梯',
      price: tier2Price,
      used: tier2Used,
      total: tier2Total,
      remaining: tier2Remaining,
      color: 'yellow',
      ringClass: 'ring-yellow-500',
    })
  }

  // 检查是否有第三阶梯的价格
  if (data.f_stair3price) {
    // 第三阶梯
    let tier3Total
    let tier3Remaining

    if (totalTiers <= 3) {
      // 如果只有3个阶梯，第三阶梯是无上限的
      tier3Total = 99999
      tier3Remaining = '无上限'
    }
    else {
      // 如果有4个阶梯，第三阶梯有上限
      tier3Total = Number(data.f_stair3ceiling || 0) - Number(data.f_stair2ceiling || 0)
      tier3Remaining = Number(data.f_stair3surplus || 0)
    }

    // 计算用量
    const previousTiersUsage = result.reduce((sum, tier) => sum + tier.used, 0)
    const totalUsed = Number(data.sumAmount || 0)

    let tier3Used
    if (totalTiers <= 3) {
      // 如果是无上限阶梯，用量为总用量减去前面阶梯的用量
      tier3Used = Math.max(0, totalUsed - previousTiersUsage)
    }
    else {
      // 如果有上限，通过总量和剩余量计算
      tier3Used = tier3Total - Number(tier3Remaining)
    }

    const tier3Price = data.f_stair3price ? `¥${Number(data.f_stair3price).toFixed(2)}/m³` : '¥0.00/m³'

    result.push({
      tierName: '第三阶梯',
      price: tier3Price,
      used: tier3Used,
      total: tier3Total,
      remaining: tier3Remaining,
      color: 'red',
      ringClass: 'ring-red-500',
    })
  }

  // 检查是否有第四阶梯的价格
  if (data.f_stair4price) {
    // 第四阶梯 (无上限)
    const previousTiersUsage = result.reduce((sum, tier) => sum + tier.used, 0)
    const totalUsed = Number(data.sumAmount || 0)
    const tier4Used = Math.max(0, totalUsed - previousTiersUsage)
    const tier4Total = 99999 // 进度条使用99999作为分母
    const tier4Remaining = '无上限' // 显示为"无上限"
    const tier4Price = data.f_stair4price ? `¥${Number(data.f_stair4price).toFixed(2)}/m³` : '¥0.00/m³'

    result.push({
      tierName: '第四阶梯',
      price: tier4Price,
      used: tier4Used,
      total: tier4Total,
      remaining: tier4Remaining,
      color: 'purple',
      ringClass: 'ring-purple-500',
    })
  }
  else if (totalTiers === 2) {
    // 只有两个阶梯，第二阶梯是最后阶梯（无上限）
    const totalUsed = Number(data.sumAmount || 0)
    const tier2Used = Math.max(0, totalUsed - tier1Used)

    // 更新第二阶梯数据为无上限
    result[1].used = tier2Used
    result[1].total = 99999
    result[1].remaining = '无上限'
  }

  return result
})

// 计算百分比
function getPercentage(used: number, total: number, remaining: string | number) {
  // 处理无上限阶梯
  if (remaining === '无上限') {
    // 使用实际用量与最大值(99999)的比例，但最大不超过100%
    return Math.min((used / 99999) * 100, 100)
  }

  if (total <= 0)
    return 0
  return Math.min((used / total) * 100, 100)
}

// 用于判断是否是最后阶梯（无上限阶梯）
function isLastTier(tier: TierData, index: number) {
  // 简化逻辑，直接根据remaining是否为'无上限'判断
  return tier.remaining === '无上限'
}

// 获取固定价格
const getFixedPrice = computed(() => {
  const priceObj = props.stairInfo.chargePrice?.[0]
  if (!priceObj)
    return '0.00'

  // 尝试从不同的可能字段中获取价格
  const price = priceObj.f_price || priceObj.f_gas_price || props.stairInfo.f_stair1price || '0'
  return Number(price).toFixed(2)
})
</script>

<template>
  <div v-if="props.stairInfo.chargePrice?.length === 1" class="fixed-price-container">
    <div class="fixed-price-info">
      <span class="price-label">固定价格:</span>
      <span class="price-value">¥{{ getFixedPrice }}/m³</span>
    </div>
  </div>
  <div v-else class="tier-gas-container">
    <div class="cycle-info">
      年度周期: {{ props.stairInfo.stairBeginDate?.substring(0, 10) }} - {{ props.stairInfo.stairEndDate?.substring(0, 10) }}
    </div>
    <div class="tiers-container">
      <div
        v-for="(tier, index) in tiersData"
        :key="index"
        class="tier-item"
        :class="{ [tier.ringClass]: currentTier === index + 1, 'ring-2': currentTier === index + 1 }"
      >
        <div class="tier-header">
          <div class="tier-name-price">
            <div class="tier-name">
              {{ tier.tierName }}
            </div>
            <div class="tier-price" :class="`text-${tier.color}-600`">
              {{ tier.price }}
            </div>
          </div>
          <div v-if="!isLastTier(tier, index)" class="tier-usage">
            {{ tier.used }}/{{ tier.total }} m³
          </div>
          <div v-else class="tier-usage">
            已用 {{ tier.used }}
          </div>
        </div>
        <div class="progress-container">
          <div class="progress-bar-bg">
            <div
              class="progress-bar"
              :class="[
                `bg-gradient-${tier.color}`,
                isLastTier(tier, index) ? 'unlimited-tier-bar' : '',
              ]"
              :style="{ width: `${getPercentage(tier.used, tier.total, tier.remaining)}%` }"
            />
          </div>
          <div class="remaining-amount" :class="`text-${tier.color}-600`">
            {{ isLastTier(tier, index) ? '无上限' : `剩余 ${tier.remaining} m³` }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.ring-green-500 {
  --tw-ring-color: rgba(34, 197, 94, 1);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-yellow-500 {
  --tw-ring-color: rgba(234, 179, 8, 1);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-red-500 {
  --tw-ring-color: rgba(239, 68, 68, 1);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-purple-500 {
  --tw-ring-color: rgba(168, 85, 247, 1);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

// 颜色相关样式
.text-green-600 {
  color: #16a34a;
}

.text-yellow-600 {
  color: #ca8a04;
}

.text-red-600 {
  color: #dc2626;
}

.text-purple-600 {
  color: #9333ea;
}

.bg-gradient-green {
  background: linear-gradient(to right, #4ade80, #22c55e);
}

.bg-gradient-yellow {
  background: linear-gradient(to right, #facc15, #eab308);
}

.bg-gradient-red {
  background: linear-gradient(to right, #f87171, #ef4444);
}

.bg-gradient-purple {
  background: linear-gradient(to right, #c084fc, #a855f7);
}

// 容器和布局样式
.tier-gas-container {
  padding: 0.5rem;
}

.cycle-info {
  margin-bottom: 1rem;
  text-align: center;
  font-size: 13px;
  color: #6b7280;
}

.tiers-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tier-item {
  background-color: #f9fafb;
  border-radius: 0.375rem;
  padding: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.tier-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tier-name-price {
  display: flex;
  align-items: center;
}

.tier-name {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.tier-price {
  margin-left: 0.5rem;
  font-size: 13px;
  font-weight: 500;
}

.tier-usage {
  font-size: 13px;
  color: #6b7280;
}

.progress-container {
  display: flex;
  align-items: center;
}

.progress-bar-bg {
  flex: 1;
  height: 1rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 9999px;
  transition: all 0.3s ease;
}

.remaining-amount {
  margin-left: 0.5rem;
  font-size: 13px;
  font-weight: 500;
}

.unlimited-tier-bar {
  background: repeating-linear-gradient(
    45deg,
    #f87171,
    #f87171 10px,
    #ef4444 10px,
    #ef4444 20px
  );
  animation: flow 2s linear infinite;
}

@keyframes flow {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 40px 0;
  }
}

// 固定价格样式
.fixed-price-container {
  padding: 0.75rem 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fixed-price-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 15px;
  background-color: #f9fafb;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.price-label {
  color: #374151;
  font-weight: 500;
}

.price-value {
  color: #1d4ed8;
  font-weight: 600;
}
</style>
