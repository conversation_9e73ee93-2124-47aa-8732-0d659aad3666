<script setup lang="ts">
defineProps({
  label: {
    type: String,
    default: '',
  },
  hint: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <div class="form-field">
    <label v-if="label" class="form-field__label">{{ label }}</label>
    <div class="form-field__content">
      <slot />
    </div>
    <div v-if="hint" class="form-field__hint">
      {{ hint }}
    </div>
  </div>
</template>

<style lang="less" scoped>
.form-field {
  margin-bottom: 16px;

  &__label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #606060;
    margin-bottom: 4px;
  }

  &__content {
    position: relative;
  }

  &__hint {
    margin-top: 4px;
    font-size: 12px;
    color: #999;
    font-style: italic;
  }
}
</style>
