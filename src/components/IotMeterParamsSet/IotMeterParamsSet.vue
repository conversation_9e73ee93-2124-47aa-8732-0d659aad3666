<script setup>
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import { post } from '@af-mobile-client-vue3/services/restTools'
import useUserStore from 'af-mobile-client-vue3/src/stores/modules/user.js'
import { showDialog, showToast } from 'vant'
import { onMounted, reactive, ref, watch } from 'vue'
import useBusinessStore from '@/stores/modules/business.js'

const emit = defineEmits(['closeOperation', 'complete'])
const meterInfo = ref([{}])
const formData = reactive({})
const paramTypes = ref([])
const paramType = ref('')
const activeCollapseNames = ref([])
const showParamTypePicker = ref(false)
const showOptionPickerPopup = ref(false)
const showDatePickerPopup = ref(false)
const activeTab = ref(0)
const currentDateParam = ref('')
const dateTimePickerValue = ref(undefined)
const optionColumns = ref([])
const currentOptionParam = ref(null)
const showConfirmDialog = ref(false)
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })

// 监听参数类型变化
watch(
  () => paramType.value,
  async (val) => {
    if (!val)
      return
    Object.keys(formData).forEach(key => delete formData[key])
    const index = meterInfo.value.findIndex(item => item.name === val)
    if (index !== -1) {
      activeCollapseNames.value = [index]
    }
    // 多用户时不自动填充参数
    if (userInfo.value && userInfo.value.f_userfiles_id) {
      try {
        const response = await runLogic(
          'mobile_getIotParams',
          { f_userfiles_id: userInfo.value.f_userfiles_id },
          'af-revenue',
        )
        if (response && response.length > 0) {
          response.forEach((item) => {
            if (val === item.f_param_group_name) {
              formData[item.f_param_lname] = item.f_param_value
            }
          })
        }
      }
      catch (error) {
        showToast('获取参数失败')
      }
    }
  },
)

function close() {
  Object.keys(formData).forEach(key => delete formData[key])
  emit('closeOperation')
}

function resetForm() {
  Object.keys(formData).forEach(key => delete formData[key])
  showToast('已重置所有参数')
}

async function refreshParam(val) {
  paramTypes.value = []
  try {
    const res = await post('/webmeter/rs/logic/getParamsByGasBrand', {
      data: { gasBrandId: val },
    })
    if (res) {
      meterInfo.value = res
      paramTypes.value = res.map(item => ({ text: item.name, value: item.name }))
    }
  }
  catch (error) {
    showToast({ message: '该气表品牌不支持参数设置', type: 'warning' })
  }
}

function onParamTypeConfirm(value) {
  paramType.value = value.selectedValues[0]
  showParamTypePicker.value = false
  const index = meterInfo.value.findIndex(item => item.name === paramType.value)
  if (index !== -1) {
    activeCollapseNames.value = [index]
  }
}

function showDatePicker(paramTitle) {
  currentDateParam.value = paramTitle
  showDatePickerPopup.value = true
}

function onDateTimePickerConfirm() {
  const dateStr = dateTimePickerValue.value.date.join('-')
  const timeStr = dateTimePickerValue.value.time.join(':')
  formData[currentDateParam.value] = `${dateStr} ${timeStr}`
  activeTab.value = 0
  showDatePickerPopup.value = false
}

function onPickerCancel() {
  showDatePickerPopup.value = false
}

function showOptionPicker(param) {
  currentOptionParam.value = param
  optionColumns.value = param.params.map(item => ({ text: item.label, value: item.value }))
  showOptionPickerPopup.value = true
}

function confirmOptionPicker(option) {
  formData[currentOptionParam.value.title] = option.value
  showOptionPickerPopup.value = false
}

function getOptionLabel(param, value) {
  const option = param.params.find(opt => opt.value === value)
  return option ? option.label : value
}

function getDisplayValue(param) {
  const value = formData[param.title]
  if (!value)
    return ''
  if (param.type === 'option') {
    return getOptionLabel(param, value)
  }
  return value
}

async function setParam() {
  const user = [userInfo.value.f_userfiles_id]
  const filesid = userInfo.value.f_userfiles_id

  const params = []
  for (let i = 0; i < meterInfo.value.length; i++) {
    if (paramType.value === meterInfo.value[i].name) {
      for (let j = 0; j < meterInfo.value[i].params.length; j++) {
        for (const key in formData) {
          if (key === meterInfo.value[i].params[j].title) {
            const data = {}
            data.param_group = meterInfo.value[i].name
            data.param_name = meterInfo.value[i].params[j].name
            data.tag = meterInfo.value[i].tag
            if (formData[key]) {
              if (Array.isArray(formData[key])) {
                const valueArr = []
                const contentArr = []
                formData[key].forEach((item) => {
                  valueArr.push(item.code)
                  contentArr.push(item.content)
                })
                data.param_value = valueArr
                data.param_content = contentArr
              }
              else if (Object.prototype.toString.call(formData[key]) === '[object Object]') {
                data.param_value = formData[key].code
                data.param_content = formData[key].content
              }
              else {
                data.param_value = formData[key]
                data.param_content = formData[key]
              }
            }
            else {
              data.param_value = null
              data.param_content = null
            }
            data.param_lname = meterInfo.value[i].params[j].title
            if (formData[key]) {
              params.push(data)
            }
          }
        }
      }
    }
  }
  const param = {
    condition: `g.f_gasbrand_id = ${userInfo.value.f_gasbrand_id} and f.f_userfiles_id in (${filesid})`,
    switchCheckAll: false,
    users: user,
    params,
    f_alias: userInfo.value.f_alias,
    columnName: 'g.f_userfiles_id',
    f_operator: currUser.resources.name,
    f_operatorid: currUser.resources.id,
    f_orgid: currUser.resources.orgid,
    f_orgname: currUser.resources.orgs,
    f_depid: currUser.resources.depids,
    f_depname: currUser.resources.deps,
    f_gasbrand_id: userInfo.value.f_gasbrand_id,
  }
  try {
    await runLogic('alteration_batch', param, 'af-revenue')
    showToast('保存成功')
    Object.keys(formData).forEach(key => delete formData[key])
    emit('complete', { status: true })
    showConfirmDialog.value = false
  }
  catch (error) {
    showToast('保存失败')
  }
}

function showTooltip(message) {
  showDialog({ title: '参数说明', message })
}

onMounted(() => {
  refreshParam(userInfo.value.f_gasbrand_id)
})
</script>

<template>
  <div class="param-setting-container">
    <!-- 参数类型选择 -->
    <van-cell-group inset class="mb-4">
      <van-field
        v-model="paramType"
        is-link
        readonly
        label="参数类型"
        placeholder="请选择参数类型"
        input-align="right"
        :rules="[{ required: true, message: '请选择参数类型' }]"
        @click="showParamTypePicker = true"
      />
      <van-popup
        v-model:show="showParamTypePicker"
        round
        position="bottom"
        teleport="body"
        overlay-class="date-picker-overlay"
      >
        <van-picker
          title="参数类型"
          :columns="paramTypes"
          :columns-field-names="{ text: 'text', value: 'value' }"
          confirm-button-text="确认"
          cancel-button-text="取消"
          @cancel="showParamTypePicker = false"
          @confirm="onParamTypeConfirm"
        />
      </van-popup>
    </van-cell-group>
    <!-- 参数设置区域 -->
    <template v-for="(config, configIndex) in meterInfo" :key="configIndex">
      <template v-if="config.name === paramType">
        <van-collapse v-model="activeCollapseNames">
          <van-collapse-item :name="configIndex" :title="config.name">
            <van-cell-group inset>
              <template v-for="(param, paramIndex) in config.params" :key="paramIndex">
                <van-field
                  v-if="param.type !== 'option' && param.type !== 'date'"
                  v-model="formData[param.title]"
                  :type="param.type"
                  :placeholder="param.placeholder || ''"
                  :min="param.min"
                  :max="param.max"
                  input-align="right"
                  :label="param.name"
                  :label-width="120"
                >
                  <template #label>
                    <div class="param-label">
                      <span>{{ param.name }}：</span>
                      <van-icon
                        v-if="param.remark"
                        name="question-o"
                        class="param-help-icon"
                        @click.stop="showTooltip(param.remark)"
                      />
                    </div>
                  </template>
                </van-field>
                <van-field
                  v-if="param.type === 'date'"
                  v-model="formData[param.title]"
                  readonly
                  is-link
                  :label="param.name"
                  :placeholder="`请选择${param.name}`"
                  :label-width="120"
                  input-align="right"
                  @click="showDatePicker(param.title)"
                >
                  <template #label>
                    <div class="param-label">
                      <span>{{ param.name }}：</span>
                      <van-icon
                        v-if="param.remark"
                        name="question-o"
                        class="param-help-icon"
                        @click.stop="showTooltip(param.remark)"
                      />
                    </div>
                  </template>
                </van-field>
                <van-field
                  v-if="param.type === 'option'"
                  v-model="formData[param.title]"
                  :label="param.name"
                  :placeholder="`请选择${param.name}`"
                  readonly
                  is-link
                  input-align="right"
                  :rules="[{ required: param.required === 'true', message: '请选择' }]"
                  @click="showOptionPicker(param)"
                >
                  <template #label>
                    <div class="param-label">
                      <span>{{ param.name }}：</span>
                      <van-icon
                        v-if="param.remark"
                        name="question-o"
                        class="param-help-icon"
                        @click.stop="showTooltip(param.remark)"
                      />
                    </div>
                  </template>
                </van-field>
              </template>
            </van-cell-group>
          </van-collapse-item>
        </van-collapse>
      </template>
    </template>
    <!-- 底部按钮区域 -->
    <div class="button-container">
      <van-button plain type="primary" @click="resetForm">
        重置
      </van-button>
      <van-button plain @click="close">
        取消
      </van-button>
      <van-button type="primary" :disabled="!paramType" @click="showConfirmDialog = true">
        提交
      </van-button>
    </div>
    <!-- 日期选择弹窗 -->
    <van-popup
      v-model:show="showDatePickerPopup"
      position="bottom"
      round
      teleport="body"
      overlay-class="date-picker-overlay"
    >
      <van-picker-group
        v-model:active-tab="activeTab"
        title="选择日期时间"
        :tabs="['选择日期', '选择时间']"
        next-step-text="下一步"
        confirm-button-text="确认"
        cancel-button-text="取消"
        @confirm="onDateTimePickerConfirm"
        @cancel="onPickerCancel"
      >
        <van-date-picker v-model="dateTimePickerValue.date" :columns-type="['year', 'month', 'day']" />
        <van-time-picker v-model="dateTimePickerValue.time" :columns-type="['hour', 'minute', 'second']" min-time="00:00:00" max-time="23:59:59" />
      </van-picker-group>
    </van-popup>
    <!-- 选项选择弹窗 -->
    <van-popup
      v-model:show="showOptionPickerPopup"
      round
      position="bottom"
      teleport="body"
      overlay-class="date-picker-overlay"
    >
      <van-picker
        :title="currentOptionParam?.name || '请选择'"
        :columns="optionColumns"
        :columns-field-names="{ text: 'text', value: 'value' }"
        confirm-button-text="确认"
        cancel-button-text="取消"
        @cancel="showOptionPickerPopup = false"
        @confirm="confirmOptionPicker"
      />
    </van-popup>
    <!-- 确认对话框（多用户时） -->
    <van-dialog
      v-model:show="showConfirmDialog"
      title="确认参数设置"
      show-cancel-button
      @confirm="setParam"
    >
      <div class="confirm-content">
        <div class="param-summary">
          <template v-for="(config, index) in meterInfo" :key="index">
            <template v-if="config.name === paramType">
              <div class="param-group">
                <h5 class="param-group-title">
                  {{ config.name }}
                </h5>
                <div class="param-list">
                  <template v-for="param in config.params" :key="param.title">
                    <div v-if="formData[param.title]" class="param-item">
                      <span class="param-name">{{ param.name }}:</span>
                      <span class="param-value">{{ getDisplayValue(param) }}</span>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </template>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<style scoped>
.param-setting-container {
  padding: 16px;
  background-color: #f7f8fa;
}
.param-label {
  display: flex;
  align-items: center;
}
.button-container {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
}
.param-help-icon {
  margin-left: 4px;
  color: #1989fa;
}
.param-history {
  margin-top: 32px;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
}
.history-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}
.history-table th,
.history-table td {
  border: 1px solid #eee;
  padding: 6px 8px;
  text-align: center;
}
.history-table th {
  background: #f2f7ff;
}
</style>
