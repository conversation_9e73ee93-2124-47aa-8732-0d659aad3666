<script setup>
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import { post } from '@af-mobile-client-vue3/services/restTools'
import useUserStore from 'af-mobile-client-vue3/src/stores/modules/user.js'
import { showDialog, showToast } from 'vant'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import useBusinessStore from '@/stores/modules/business.js'

const emit = defineEmits(['closeOperation', 'complete'])

// 响应式状态
const meterInfo = ref([{}])
const formData = reactive({})
const paramTypes = ref([])
const paramType = ref('')
const activeCollapseNames = ref([]) // 改为空数组初始值
const showParamTypePicker = ref(false)
const showOptionPickerPopup = ref(false)
const showDatePickerPopup = ref(false)
const activeTab = ref(0)
const currentDateParam = ref('')
const dateTimePickerValue = ref(undefined)
const optionColumns = ref([])
const currentOptionParam = ref(null)
const showConfirmDialog = ref(false)
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })

// 计算属性：每个 option 字段的展示label
const optionDisplay = computed(() => {
  const map = {}
  meterInfo.value.forEach((config) => {
    console.log(config)
    if (Array.isArray(config.params) && config.name === paramType.value) {
      config.params.forEach((param) => {
        if (param.type === 'option') {
          const value = formData[param.title]
          if (typeof value === 'object' && value !== null && value.label) {
            map[param.title] = value.label
          }
          else {
            const option = param.params.find(opt => opt.value === value)
            map[param.title] = option ? option.label : value || ''
          }
        }
      })
    }
  })
  return map
})

// 监听参数类型变化
watch(
  () => paramType.value,
  async (val) => {
    if (!val)
      return

    // 重置表单数据
    Object.keys(formData).forEach(key => delete formData[key])

    // 找到对应参数类型的索引，自动展开对应的折叠面板
    const index = meterInfo.value.findIndex(item => item.name === val)
    if (index !== -1) {
      activeCollapseNames.value = [index] // 设置当前选择的参数类型对应的折叠面板为展开状态
    }

    if (userInfo.value && userInfo.value.f_userfiles_id) {
      try {
        const response = await runLogic(
          'mobile_getIotParams',
          {
            f_userfiles_id: userInfo.value.f_userfiles_id,
          },
          'af-revenue',
        )
        if (response && response.length > 0) {
          response.forEach((item) => {
            if (val === item.f_param_group_name) {
              formData[item.f_param_lname] = item.f_param_value
            }
          })
        }
      }
      catch (error) {
        showToast('获取参数失败')
      }
    }
  },
)

// 方法
function close() {
  Object.keys(formData).forEach(key => delete formData[key])
  emit('closeOperation')
}

function resetForm() {
  // 重置表单数据为默认值
  Object.keys(formData).forEach(key => delete formData[key])
  showToast('已重置所有参数')
}

async function refreshParam(val) {
  paramTypes.value = []
  try {
    // 获取参数配置
    const res = await post('/webmeter/rs/logic/getParamsByGasBrand', {
      data: {
        gasBrandId: userInfo.value.f_gasbrand_id,
      },
    })

    if (res) {
      meterInfo.value = res
      paramTypes.value = res.map(item => ({
        text: item.name,
        value: item.name,
      }))
    }
  }
  catch (error) {
    showToast({
      message: '该气表品牌不支持参数设置',
      type: 'warning',
    })
  }
}

function onParamTypeConfirm(value) {
  paramType.value = value.selectedValues[0]
  showParamTypePicker.value = false

  // 在选择参数类型后自动展开对应的折叠面板
  const index = meterInfo.value.findIndex(
    item => item.name === paramType.value,
  )
  if (index !== -1) {
    activeCollapseNames.value = [index]
  }
}

function showDatePicker(paramTitle) {
  currentDateParam.value = paramTitle
  if (formData[paramTitle]) {
    // 拆分日期和时间
    const [dateStr, timeStr] = formData[paramTitle]
    // 拆分日期部分
    const date = dateStr.split('-')
    // 拆分时间部分
    const time = timeStr.split(':')
    // 赋值给 dateTimePickerValue
    dateTimePickerValue.value = {
      date,
      time,
    }
  }
  else {
    dateTimePickerValue.value = {
      date: ['2015', '01', '01'],
      time: ['00', '00', '00'],
    }
  }
  console.log(dateTimePickerValue)
  showDatePickerPopup.value = true
}

function onDateTimePickerConfirm() {
  const dateStr = dateTimePickerValue.value.date.join('-')
  const timeStr = dateTimePickerValue.value.time.join(':')

  formData[currentDateParam.value] = `${dateStr} ${timeStr}`
  console.log(
    'formData[currentDateParam.value]',
    formData[currentDateParam.value],
  )
  activeTab.value = 0
  showDatePickerPopup.value = false
}

function onPickerCancel() {
  showDatePickerPopup.value = false
}

function showOptionPicker(param) {
  currentOptionParam.value = param
  optionColumns.value = param.params.map(item => ({
    text: item.label,
    value: item.value,
  }))
  showOptionPickerPopup.value = true
}

function confirmOptionPicker(option) {
  console.log(option, currentOptionParam)
  formData[currentOptionParam.value.title] = option.selectedValues[0]
  showOptionPickerPopup.value = false
}

function getOptionLabel(param, value) {
  const option = param.params.find(opt => opt.value === value)
  return option ? option.label : value
}

function getDisplayValue(param) {
  const value = formData[param.title]
  if (!value)
    return ''

  if (param.type === 'option') {
    return getOptionLabel(param, value)
  }
  return value
}

async function setParam() {
  try {
    let filesid = ''
    filesid += `${userInfo.value.f_userfiles_id},`
    filesid = filesid.substr(0, filesid.length - 1)

    const user = [userInfo.value.f_userfiles_id]

    const params = []
    for (let i = 0; i < meterInfo.value.length; i++) {
      if (paramType.value === meterInfo.value[i].name) {
        for (let j = 0; j < meterInfo.value[i].params.length; j++) {
          const param = meterInfo.value[i].params[j]
          if (formData[param.title] !== undefined) {
            params.push({
              param_group: meterInfo.value[i].name,
              param_name: param.name,
              param_value: formData[param.title],
              param_lname: param.title,
            })
          }
        }
      }
    }

    const requestData = {
      condition: `g.f_gasbrand_id = ${userInfo.value.f_gasbrand_id} and f.f_userfiles_id in (${filesid})`,
      users: user,
      params,
      f_alias: userInfo.value.f_alias,
      columnName: 'g.f_userfiles_id',
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
      f_gasbrand_id: userInfo.value.f_gasbrand_id,
    }

    console.log('========>', requestData)
    await runLogic('alteration_batch', requestData, 'af-revenue').then(
      (res) => {
        showToast('保存成功')
        Object.keys(formData).forEach(key => delete formData[key])
        emit('complete', { status: true })
        showConfirmDialog.value = false
      },
    )
  }
  catch (error) {
    console.error(error)
    showToast('保存失败')
  }
}

function showTooltip(message) {
  showDialog({
    title: '参数说明',
    message,
  })
}

// 初始化
onMounted(() => {
  refreshParam(userInfo.value.f_gasbrand_id)
})
</script>

<template>
  <div class="param-setting-container">
    <!-- 参数类型选择 -->
    <van-cell-group inset class="mb-4">
      <van-field
        v-model="paramType"
        is-link
        readonly
        label="参数类型"
        placeholder="请选择参数类型"
        input-align="right"
        :rules="[{ required: true, message: '请选择参数类型' }]"
        @click="showParamTypePicker = true"
      />

      <van-popup
        v-model:show="showParamTypePicker"
        round
        position="bottom"
        teleport="body"
        overlay-class="date-picker-overlay"
      >
        <van-picker
          title="参数类型"
          :columns="paramTypes"
          :columns-field-names="{ text: 'text', value: 'value' }"
          confirm-button-text="确认"
          cancel-button-text="取消"
          @cancel="showParamTypePicker = false"
          @confirm="onParamTypeConfirm"
        />
      </van-popup>
    </van-cell-group>

    <!-- 参数设置区域 -->
    <template v-for="(config, configIndex) in meterInfo" :key="configIndex">
      <template v-if="config.name === paramType">
        <van-collapse v-model="activeCollapseNames">
          <van-collapse-item :name="configIndex" :title="config.name">
            <van-cell-group inset>
              <template
                v-for="(param, paramIndex) in config.params"
                :key="paramIndex"
              >
                <!-- 文本/数字输入框 -->
                <van-field
                  v-if="param.type !== 'option' && param.type !== 'date'"
                  v-model="formData[param.title]"
                  :type="param.type"
                  :placeholder="param.placeholder || ''"
                  :min="param.min"
                  :max="param.max"
                  input-align="right"
                  :label="param.name"
                  :label-width="120"
                >
                  <template #label>
                    <div class="param-label">
                      <span>{{ param.name }}：</span>
                      <van-icon
                        v-if="param.remark"
                        name="question-o"
                        class="param-help-icon"
                        @click.stop="showTooltip(param.remark)"
                      />
                    </div>
                  </template>
                </van-field>

                <!-- 日期选择器 -->
                <van-field
                  v-if="param.type === 'date'"
                  v-model="formData[param.title]"
                  readonly
                  is-link
                  :label="param.name"
                  :placeholder="`请选择${param.name}`"
                  :label-width="120"
                  input-align="right"
                  @click="showDatePicker(param.title)"
                >
                  <template #label>
                    <div class="param-label">
                      <span>{{ param.name }}：</span>
                      <van-icon
                        v-if="param.remark"
                        name="question-o"
                        class="param-help-icon"
                        @click.stop="showTooltip(param.remark)"
                      />
                    </div>
                  </template>
                </van-field>

                <!-- 选项类型 -->
                <van-field
                  v-if="param.type === 'option'"
                  :model-value="optionDisplay[param.title]"
                  :label="param.name"
                  :placeholder="`请选择${param.name}`"
                  readonly
                  is-link
                  input-align="right"
                  :rules="[
                    { required: param.required === 'true', message: '请选择' },
                  ]"
                  @click="showOptionPicker(param)"
                >
                  <template #label>
                    <div class="param-label">
                      <span>{{ param.name }}：</span>
                      <van-icon
                        v-if="param.remark"
                        name="question-o"
                        class="param-help-icon"
                        @click="showTooltip(param.remark)"
                      />
                    </div>
                  </template>
                </van-field>
              </template>
            </van-cell-group>
          </van-collapse-item>
        </van-collapse>
      </template>
    </template>

    <!-- 底部按钮区域 -->
    <div class="button-container">
      <van-button plain type="primary" @click="resetForm">
        重置
      </van-button>
      <van-button plain @click="close">
        取消
      </van-button>
      <van-button
        type="primary"
        :disabled="!paramType"
        @click="showConfirmDialog = true"
      >
        提交
      </van-button>
    </div>

    <!-- 日期选择弹窗 -->
    <van-popup
      v-model:show="showDatePickerPopup"
      position="bottom"
      round
      teleport="body"
      overlay-class="date-picker-overlay"
    >
      <van-picker-group
        v-model:active-tab="activeTab"
        title="选择日期时间"
        :tabs="['选择日期', '选择时间']"
        next-step-text="下一步"
        confirm-button-text="确认"
        cancel-button-text="取消"
        @confirm="onDateTimePickerConfirm"
        @cancel="onPickerCancel"
      >
        <van-date-picker
          v-model="dateTimePickerValue.date"
          :columns-type="['year', 'month', 'day']"
        />
        <van-time-picker
          v-model="dateTimePickerValue.time"
          :columns-type="['hour', 'minute', 'second']"
          min-time="00:00:00"
          max-time="23:59:59"
        />
      </van-picker-group>
    </van-popup>

    <!-- 选项选择弹窗 -->
    <van-popup
      v-model:show="showOptionPickerPopup"
      round
      position="bottom"
      teleport="body"
      overlay-class="date-picker-overlay"
    >
      <van-picker
        :title="currentOptionParam?.name || '请选择'"
        :columns="optionColumns"
        :columns-field-names="{ text: 'text', value: 'value' }"
        confirm-button-text="确认"
        cancel-button-text="取消"
        @cancel="showOptionPickerPopup = false"
        @confirm="confirmOptionPicker"
      />
    </van-popup>

    <!-- 确认对话框 -->
    <van-dialog
      v-model:show="showConfirmDialog"
      title="确认参数设置"
      show-cancel-button
      @confirm="setParam"
    >
      <div class="confirm-content">
        <div class="param-summary">
          <template v-for="(config, index) in meterInfo" :key="index">
            <template v-if="config.name === paramType">
              <div class="param-group">
                <h5 class="param-group-title">
                  {{ config.name }}
                </h5>
                <div class="param-list">
                  <template v-for="param in config.params" :key="param.title">
                    <div v-if="formData[param.title]" class="param-item">
                      <span class="param-name">{{ param.name }}:</span>
                      <span class="param-value">{{
                        getDisplayValue(param)
                      }}</span>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </template>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<style scoped>
.param-setting-container {
  padding: 16px;
  background-color: #f7f8fa;
}

.param-label {
  display: flex;
  align-items: center;
}

.param-help-icon {
  margin-left: 4px;
  color: #1989fa;
}

.button-container {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
}

.param-summary {
  background-color: #f2f7ff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.param-group {
  margin-bottom: 12px;
}

.param-group-title {
  font-size: 14px;
  font-weight: 500;
  color: #1989fa;
  margin-bottom: 8px;
}

.param-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.param-item {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
}

.param-name {
  color: #646566;
}

.param-value {
  font-weight: 500;
  color: #323233;
}
</style>
