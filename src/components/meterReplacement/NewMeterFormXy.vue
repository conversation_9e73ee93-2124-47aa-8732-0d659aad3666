<script setup lang="ts">
import type { MeterReplacementFormData } from './types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { mobileUtil } from '@af-mobile-client-vue3/utils/mobileUtil'
import { Field, Picker, Popup, showConfirmDialog, showDialog, showFailToast, showToast } from 'vant'
import { computed, inject, ref } from 'vue'
import { FormField } from '@/components/common'
import useBusinessStore from '@/stores/modules/business'

const props = defineProps({
  type: {
    type: String,
    default: '换新表',
  },
})

const emit = defineEmits(['meterReadingComplete', 'uploadFile'])

// 通过inject接收父组件提供的formData
const currUser = useUserStore().getLogin().f
const formData = inject('formData') as MeterReplacementFormData
const userInfo = useBusinessStore().currentUser

// 是否可以修改通信协议
const canModifyProtocol = computed(() => {
  return useUserStore().getPermissions().includes('通讯协议修改')
})

// 是否展示imei 表号验证结束之后展示
const imeiShow = ref(false)
const meterNumberValidate = ref(false)

// 气表品牌选择 总共三级 表类型/品牌/型号
let meterTypesOptions = reactive([])
// 是否展示表品牌选择框
const showMeterBrandPicker = ref(false)
// 仅展示用的选择结果
const displayBrandResult = ref('')
const displayBrandResultStr = ref('')
const collectionType = ref(null)

// 开户方式选项
const accountOptions = [
  { text: '指令开户', value: '指令开户' },
  { text: '发卡开户', value: '发卡开户' },
]

// 是否显示开户方式选择器
const showAccountTypePicker = ref(false)

// 通讯协议选项
const protocolOptions = [
  { text: 'cat.1', value: '3' },
  { text: 'NB', value: '1' },
]

// 是否显示通讯协议选择器
const showProtocolPicker = ref(false)
// 选中的通讯协议显示文本
const selectedProtocolText = computed(() => {
  const selected = protocolOptions.find(item => item.value === formData.f_terminal_id)
  return selected ? selected.text : ''
})

const customFieldName = {
  text: 'label',
  value: 'value',
  children: 'children',
}

// 计算属性：是否显示开户方式
const showAccountType = computed(() => {
  if (formData.gasbrand?.length > 0) {
    return formData.gasbrand[0].f_meter_type === '物联网表' && formData.gasbrand[0].f_share_open === '是'
  }
  return false
})

// 特殊表品牌Id
const specialMeterBrandId = [419, **********, *********, **********, **********]

// 计算属性：表示阶梯用量字段是否应该显示
// const showStairUseField = computed(() => {
//   if (formData.gasbrand?.length > 0) {
//     return imeiShow.value && props.type !== '气表升级' && props.type !== '换控制器'
//   }
//   return false
// })

// 修改表单值后更新表单
function updateProtocol({ selectedOptions }) {
  formData.f_terminal_id = selectedOptions[0].value
  showProtocolPicker.value = false
}

// 开户方式变化
function accountTypeChange({ selectedOptions }) {
  formData.f_open_type = selectedOptions[0].value
  showAccountTypePicker.value = false
}

// 暴露给外部的验证方法
function validate() {
  // 防止 imei 没有展示出来用户就点击了提交
  if (meterNumberValidate.value) {
    showFailToast('表号校验中，请稍后再进行提交')
    return false
  }

  // 验证各必填字段
  if (!formData.f_using_base_old) {
    showFailToast('请输入旧表最新底数')
    return false
  }

  if (!(formData.gasbrand && formData.gasbrand.length > 0)) {
    showFailToast('请选择新表品牌')
    return false
  }

  if (!formData.f_meter_base) {
    showFailToast('请输入新表底数')
    return false
  }

  if (!formData.f_meternumber) {
    showFailToast('请输入新表号')
    return false
  }

  // 表识别码/DTU编号必填验证
  if (imeiShow.value) {
    // 判断是否需要必填表识别码
    const isImeiRequired
      // 苍南系列判断
      = (formData.gasbrand[0].id !== 419
        && ['CangNanTCP', 'CangNanMoney', 'CangNanGas'].includes(formData.gasbrand[0].f_alias))
      // 特殊品牌判断
      || specialMeterBrandId.includes(formData.gasbrand[0].id)

    if (isImeiRequired && !formData.f_imei) {
      const fieldName = formData.gasbrand[0].f_meter_brand.includes('苍南') ? 'DTU编号' : '表识别码'
      showFailToast(`请输入${fieldName}`)
      return false
    }
  }

  // 通讯协议验证（仅当需要显示时）
  if (formData.gasbrand && formData.gasbrand.length > 0 && formData.gasbrand[0].id === ********** && !formData.f_terminal_id) {
    showFailToast('请选择通讯协议')
    return false
  }

  // 验证开户方式
  if (showAccountType.value && !formData.f_open_type) {
    showFailToast('请选择开户方式')
    return false
  }

  return true
}
function validateArrears(): void {
  // 判断机表是否欠费
  runLogic<{
    arrears: Array<any>
    data: {
      f_remanent_gas: number
      f_remanent_price: number
      f_remanent_money: number
    }
    msg: string
  }>('mobile_getMachineArrears', {
    f_userfiles_id: userInfo.f_userfiles_id,
    f_meter_base: formData.f_using_base_old,
  }, 'af-revenue').then((res) => {
    if (userInfo.f_balance < 0 || res.arrears.length > 0) {
      showDialog({
        message: '该表存在欠费，请先完成欠费缴费',
      })
      formData.f_using_base_old = undefined
      formData.f_remanent_gas = undefined
      formData.f_remanent_price = undefined
      formData.f_remanent_money = undefined
    }
    else {
      Object.assign(formData, res.data)
    }
  })
}

// 旧表底数变化
function meterBaseChange() {
  if (!(formData.f_using_base_old || formData.f_using_base_old === 0)) {
    return
  }
  // 调用logic使用后台校验逻辑
  // 如果输入的旧表底数 > 用户表底数
  if (['机表', '物联网表'].includes(userInfo.f_meter_type) && formData.f_using_base_old > userInfo.f_meter_base) {
    const msg = (userInfo.f_meter_type === '机表' ? '该用户表底数大于上次抄表底数，请确定是否继续生成欠费记录' : '该用户表底数大于上次抄表底数，请确定是否进行结算')
    showConfirmDialog({
      title: '标题',
      message: msg,
    })
      .then(() => {
        runLogic('third_change_meter_hand', {
          f_userfiles_id: userInfo.f_userfiles_id,
          f_meter_base: formData.f_using_base_old,
          f_operator: currUser.resources.name,
          f_operatorid: currUser.resources.id,
          f_orgid: currUser.resources.orgid,
          f_orgname: currUser.resources.orgs,
          f_depid: currUser.resources.depids,
          f_depname: currUser.resources.deps,
        }).then(() => {
          showToast('抄表结算成功，重新加载用户信息')
          emit('meterReadingComplete', { status: true })
        })
      })
      .catch(() => {
        // 咸阳要求除机表外结算弹窗点击取消不能继续往下走 必须要点确定
        if (userInfo.f_meter_type !== '机表') {
          formData.f_using_base_old = undefined
          formData.f_remanent_gas = undefined
          formData.f_remanent_price = undefined
          formData.f_remanent_money = undefined
        }
        else {
          validateArrears()
        }
      })
  }
  else {
    meterBaseCheck()
  }
}

async function meterBaseCheck() {
  runLogic<{
    code: string
    data: {
      f_remanent_gas: number
      f_remanent_price: number
      f_remanent_money: number
    }
    msg: string
  }>('third_meter_base_check', {
    f_meter_base: formData.f_using_base_old,
    f_userfiles_id: userInfo.f_userfiles_id,
    f_meter_type: userInfo.f_meter_type,
    f_remanent_money: formData.f_remanent_money,
    f_remanent_price: formData.f_remanent_price,
    f_remanent_gas: formData.f_remanent_gas,
  }).then(async (res) => {
    if (res.code === '0000') {
      // 赋值 补气金额/单价/气量
      Object.assign(formData, res.data)
      showToast('表底数验证成功')
      // await showDialog({
      //   message: '表底数验证成功，请拍下旧表照片之后再进行下一步操作',
      // })
      // emit('uploadFile', '旧表底数')
    }
    else {
      showDialog({
        message: res.msg.replace(/，/g, '\n'),
      })
      formData.f_using_base_old = undefined
      formData.f_remanent_gas = undefined
      formData.f_remanent_price = undefined
      formData.f_remanent_money = undefined
    }
  })
}

// 新表底数变化
async function newMeterBaseChange() {
  // await showDialog({
  //   message: '新表底数填写成功，请拍下旧表照片之后再进行下一步操作',
  // })
  // emit('uploadFile', '新表底数')
}

// 气表品牌变化
function meterBrandChange({ selectedOptions }) {
  formData.gasbrand = [selectedOptions[1]]
  formData.gasmodel = [selectedOptions[2]]
  collectionType.value = selectedOptions[1].f_collection_type
  displayBrandResultStr.value = `${selectedOptions[1].label}/${selectedOptions[2].label} ${selectedOptions[2].f_type}`
  // 关闭抽屉
  showMeterBrandPicker.value = false
  // 协议处理
  if (formData.gasbrand[0].id === **********) {
    if (formData.gasmodel[0].label.includes('cat.1')) {
      formData.f_terminal_id = '3'
    }
    else if (formData.gasmodel[0].label.includes('NB')) {
      formData.f_terminal_id = '1'
    }
  }

  // 客户需求 苍南金额远传 表 默认成指令开户
  if (formData.gasbrand[0].f_gasbrand_open_way) {
    formData.f_open_type = formData.gasbrand[0].f_gasbrand_open_way
  }
  else {
    formData.f_open_type = null
  }
  // 天顺nb金额表换基表是 imie 默认为 '无'
  if (formData.gasbrand[0].id === 419 && props.type === '换基表') {
    formData.f_imei = userInfo.f_imei || undefined
  }
}

// 表号变化进行校验
function meternumberValidate() {
  meterNumberValidate.value = true
  // 先进行本地格式验证
  if (formData.gasbrand && formData.gasbrand.length > 0) {
    // 天顺NB金额表验证
    if (formData.gasbrand[0].id === 419) {
      // 如果是天顺NB金额表 开始验证表号格式
      const meternum = /^\d{8}S$/
      const meternum1 = /^\d{9}$/
      const meternum2 = /^\d{11}S$/
      const imeinum = /^\d{15}$/
      if (formData.f_meternumber && !(meternum.test(formData.f_meternumber) || meternum2.test(formData.f_meternumber) || meternum1.test(formData.f_meternumber))) {
        showDialog({
          message: '请输入正确的表号！！( 8 或 11 位数字 + S 或者 9位数字)',
        })
        formData.f_meternumber = undefined
        return
      }
      if (formData.f_imei && !imeinum.test(formData.f_imei) && formData.f_imei !== '无') {
        showDialog({
          message: '请输入正确的表识别码！！(15位数字)',
        })
        formData.f_imei = undefined
        return
      }
    }

    // 金卡民用NB表验证
    if (formData.gasbrand[0].id === **********) {
      const imeinum = /^\d{15}$/
      const meternum3 = /^\d{12}$/
      if (formData.f_meternumber && !meternum3.test(formData.f_meternumber)) {
        showDialog({
          message: '请输入正确的表号！！(12位数字)',
        })
        formData.f_meternumber = undefined
        return
      }
      const lastDigit = formData.f_meternumber.slice(-1)
      if (lastDigit !== '6' && lastDigit !== '7') {
        showDialog({
          message: '金卡民用NB表，表号最后一位数字是6或7！！',
        })
        formData.f_meternumber = undefined
        return
      }
      if (formData.f_imei && !imeinum.test(formData.f_imei)) {
        showDialog({
          message: '请输入正确的表识别码！！(15位数字)',
        })
        formData.f_imei = undefined
        return
      }
    }

    // 其他品牌表验证 - ID: *********
    if (formData.gasbrand[0].id === *********) {
      const imeinum = /^\d{15}$/
      const meternum3 = /^(?:\d{8}S|\d{9}|\d{12})$/
      if (formData.f_meternumber && !meternum3.test(formData.f_meternumber)) {
        showDialog({
          message: '请输入正确的表号！！( 8 位数字 + S 或者 9 位数字、12 位数字)',
        })
        formData.f_meternumber = undefined
        return
      }
      if (formData.f_imei && !imeinum.test(formData.f_imei)) {
        showDialog({
          message: '请输入正确的表识别码！！(15位数字)',
        })
        formData.f_imei = undefined
        return
      }
    }

    // 其他品牌表验证 - ID: **********
    if (formData.gasbrand[0].id === **********) {
      const imeinum = /^\d{15}$/
      const meternum3 = /^\d{14}$/
      if (formData.f_meternumber && !meternum3.test(formData.f_meternumber)) {
        showDialog({
          message: '请输入正确的表号！！(14 位数字)',
        })
        formData.f_meternumber = undefined
        return
      }
      if (formData.f_imei && !imeinum.test(formData.f_imei)) {
        showDialog({
          message: '请输入正确的表识别码！！(15位数字)',
        })
        formData.f_imei = undefined
        return
      }
    }

    // 先锋民用NB表验证 - ID: **********
    if (formData.gasbrand[0].id === **********) {
      const imeinum = /^\d{15}$/
      const meternum3 = /^\d{12}$/
      if (formData.f_meternumber && !meternum3.test(formData.f_meternumber)) {
        showDialog({
          message: '请输入正确的表号！！(12 位数字)',
        })
        formData.f_meternumber = undefined
        return
      }
      // 表号最后一位数字是8或9，则是先锋民用NB表
      const lastDigit = formData.f_meternumber.slice(-1)
      if (lastDigit !== '8' && lastDigit !== '9') {
        showDialog({
          message: '先锋民用NB表，表号最后一位数字是8或9！！',
        })
        formData.f_meternumber = undefined
        return
      }
      if (formData.f_imei && !imeinum.test(formData.f_imei)) {
        showDialog({
          message: '请输入正确的表识别码！！(15位数字)',
        })
        formData.f_imei = undefined
        return
      }

      if (props.type === '换基表') {
        formData.f_imei = userInfo.f_imei
      }
    }
  }

  // 通过本地验证后，进行服务器验证
  runLogic<{ code: string, msg: string }>('third_meter_number_check', {
    f_meternumber: formData.f_meternumber,
    f_imei: formData.f_imei,
    f_userfiles_id: userInfo.f_userfiles_id,
    f_gasbrand_id: formData.gasbrand[0].id,
  }).then((res) => {
    if (res.code !== '0000') {
      showDialog({
        message: res.msg.replace(/，/g, '\n'),
      })
      formData.f_meternumber = undefined
    }
    else {
      imeiShow.value = formData.gasbrand[0].f_meter_type === '物联网表'
      showToast('表号验证成功')
      meterNumberValidate.value = false
    }
  })
}

// 获取气表品牌信息
async function getMeterBrands() {
  const allBrands = await runLogic<any[]>('mobile_getMeterBrandAndModel', {
    orgId: userInfo.f_orgid,
    queryModel: true,
    changeType: props.type,
  })

  // 根据不同操作类型筛选不同的表品牌
  const filteredBrands = []

  if (props.type === '气表升级' && Array.isArray(allBrands)) {
    // 气表升级只显示指定ID的物联网表
    allBrands.forEach((item) => {
      if (Array.isArray(item.children)) {
        item.children.forEach((brand) => {
          if (brand.f_meter_type === '物联网表' && brand.id === 1177943077) {
            filteredBrands.push(item)
            // 自动选择物联网表和2G流量计
            if (!formData.gasbrand || formData.gasbrand.length === 0) {
              formData.gasbrand = [brand]
              try {
                const flowMeterModel = brand.gasmodel.find(model => model.label === '2G流量计')
                if (flowMeterModel) {
                  formData.gasmodel = [flowMeterModel]
                  displayBrandResultStr.value = `${brand.label} / 2G流量计`
                }
              }
              catch (_) {}
            }
          }
        })
      }
    })
  }
  else if (props.type === '换控制器' && Array.isArray(allBrands)) {
    // 换控制器只显示物联网表  客户让去掉？？  不理解但是先尊重
    // allBrands.forEach((item) => {
    //   if (Array.isArray(item.children)) {
    //     const hasIotMeter = item.children.some(brand => brand.f_meter_type === '物联网表')
    //     if (hasIotMeter) {
    //       filteredBrands.push(item)
    //     }
    //   }
    // })
  }

  return filteredBrands.length > 0 ? filteredBrands : (Array.isArray(allBrands) ? allBrands : [])
}

onMounted(async () => {
  // 获取气表品牌信息
  const brands = await getMeterBrands()
  meterTypesOptions = reactive(brands)
})

// 表号扫描操作
function scanMeter(type: string) {
  mobileUtil.execute({
    funcName: 'scanBarcode',
    param: {},
    callbackFunc: (result: any) => {
      if (result.status === 'success' && result.data) {
        if (type === '新表号') {
          formData.f_meternumber = result.data.displayValue
        }
        if (type === '表识别码') {
          formData.f_imei = result.data.displayValue
        }
        // 扫描完成后自动触发验证
        meternumberValidate()
      }
    },
  })
}

// 暴露方法给父组件
defineExpose({
  validate,
})
</script>

<template>
  <CardContainer title="换表基本信息">
    <div class="new-meter-form">
      <div class="form-group">
        <div class="form-item">
          <FormField label="旧表底数">
            <Field
              v-model="formData.f_using_base_old"
              type="number"
              number="9.2"
              placeholder="请输入旧表底数"
              input-align="right"
              :rules="[{ required: true, message: '请输入旧表底数' }]"
              @blur="meterBaseChange"
            />
          </FormField>
        </div>

        <div class="form-item">
          <FormField label="新表底数">
            <Field
              v-model="formData.f_meter_base"
              type="number"
              number="9.2"
              placeholder="请输入新表底数"
              input-align="right"
              :rules="[{ required: true, message: '请输入新表底数' }]"
              @blur="newMeterBaseChange"
            />
          </FormField>
        </div>

        <div class="form-item">
          <FormField label="新表品牌/型号" @click="showMeterBrandPicker = true">
            <Field
              v-model="displayBrandResultStr"
              is-link
              readonly
              placeholder="请选择新表品牌"
            />
            <Popup
              v-model:show="showMeterBrandPicker"
              position="bottom"
              teleport="body"
            >
              <van-cascader
                v-model="displayBrandResult"
                title="选择表具品牌"
                :options="meterTypesOptions"
                :field-names="customFieldName"
                @close="showMeterBrandPicker = false"
                @finish="meterBrandChange"
              />
            </Popup>
          </FormField>
        </div>

        <!-- 恢复新表计费类型字段 -->
        <div v-if="collectionType" class="form-item">
          <FormField label="新表计费类型">
            <Field
              v-model="collectionType"
              readonly
              input-align="left"
            />
          </FormField>
        </div>

        <!-- 新增：新表号输入框，只有当三个字段都选择后才显示 -->
        <div v-if="formData.gasbrand?.length > 0" class="form-item">
          <FormField label="新表号">
            <Field
              v-model="formData.f_meternumber"
              placeholder="请输入新表号"
              input-align="right"
              :rules="[{ required: true, message: '请输入新表号' }]"
              @blur="meternumberValidate"
            >
              <template #right-icon>
                <van-icon
                  name="scan"
                  class="scan-icon"
                  @click="scanMeter('新表号')"
                />
              </template>
            </Field>
          </FormField>
        </div>

        <div v-if="imeiShow && formData.gasbrand?.length > 0" class="form-item">
          <FormField :label="(formData.gasbrand[0].f_meter_brand.includes('苍南')) ? 'DTU编号' : '表识别码'">
            <Field
              v-model="formData.f_imei"
              :placeholder="props.type === '换基表' ? '无' : (formData.gasbrand[0].f_meter_brand.includes('苍南')) ? '请输入DTU编号' : '请输入表识别码'"
              input-align="right"
              :readonly="props.type === '换基表'"
              @blur="meternumberValidate"
            >
              <template v-if="!(formData.gasbrand[0].f_meter_brand.includes('苍南'))" #right-icon>
                <van-icon
                  name="scan"
                  class="scan-icon"
                  @click="scanMeter('表识别码')"
                />
              </template>
            </Field>
          </FormField>
        </div>

        <!-- 开户方式选择器 -->
        <div v-if="showAccountType" class="form-item">
          <FormField label="开户方式">
            <Field
              v-model="formData.f_open_type"
              is-link
              readonly
              placeholder="请选择开户方式"
              @click="showAccountTypePicker = true"
            />
            <Popup
              v-model:show="showAccountTypePicker"
              position="bottom"
              teleport="body"
            >
              <Picker
                title="选择开户方式"
                :columns="accountOptions"
                @confirm="accountTypeChange"
                @cancel="showAccountTypePicker = false"
              />
            </Popup>
          </FormField>
        </div>

        <!-- 修改：阶梯用量输入框，仅当表类型为物联网表且累计旧表阶梯用量为true时显示 -->
        <!-- <div v-if="showStairUseField" class="form-item">
          <FormField label="旧表余气">
            <Field
              v-model="formData.f_oldodd_gas"
              type="digit"
              placeholder="请输入旧表余气"
              input-align="right"
            />
          </FormField>
        </div> -->

        <!-- 通讯协议选择器，仅在特定气表品牌ID时显示 -->
        <div v-if="formData.gasbrand?.length > 0 && formData.gasbrand[0].id === **********" class="form-item">
          <FormField label="通讯协议">
            <Field
              v-model="selectedProtocolText"
              is-link
              :readonly="canModifyProtocol"
              placeholder="请选择通讯协议"
              @click="showProtocolPicker = true"
            />
            <Popup
              v-model:show="showProtocolPicker"
              position="bottom"
              teleport="body"
            >
              <Picker
                title="选择通讯协议"
                :columns="protocolOptions"
                @confirm="updateProtocol"
                @cancel="showProtocolPicker = false"
              />
            </Popup>
          </FormField>
        </div>
      </div>
    </div>
  </CardContainer>
</template>

<style lang="less" scoped>
.new-meter-form {
  padding: 8px;

  .form-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .form-item {
    width: calc(33.33% - 6px);
  }

  /* 移动端样式：一行一个 */
  @media screen and (max-width: 767px) {
    .form-item {
      width: 100%;
    }
  }

  /* 平板样式：一行两个 */
  @media screen and (min-width: 768px) and (max-width: 1024px) {
    .form-group {
      gap: 10px;
    }

    .form-item {
      width: calc(50% - 5px);
      flex: 0 0 calc(50% - 5px);
    }
  }
}

.field-placeholder {
  color: #999;
}

/* 添加van-field样式 */
:deep(.van-field) {
  background-color: #f9fafb;
  border-radius: 6px;
}

:deep(.van-field__control) {
  color: #333;
  font-size: 14px;

  &::placeholder {
    color: #999;
  }
}

:deep(.van-field--disabled .van-field__control) {
  color: #999;
}

/* 添加扫描图标样式 */
.scan-icon {
  font-size: 20px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: var(--van-primary-color);
  }
}
</style>
