<script setup lang="ts">
import type { MeterReplacementFormData } from './types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { mobileUtil } from '@af-mobile-client-vue3/utils/mobileUtil'
import { Field, Picker, Popup, showConfirmDialog, showDialog, showFailToast, showToast } from 'vant'
import { computed, inject, ref } from 'vue'
import { FormField } from '@/components/common'
import useBusinessStore from '@/stores/modules/business'

const emit = defineEmits(['meterReadingComplete'])

// 通过inject接收父组件提供的formData
const currUser = useUserStore().getLogin().f
const formData = inject('formData') as MeterReplacementFormData
const userInfo = useBusinessStore().currentUser

// 气表品牌选择 总共三级 表类型/品牌/型号
let meterTypesOptions = reactive([])
// 是否展示表品牌选择框
const showMeterBrandPicker = ref(false)
// 仅展示用的选择结果
const displayBrandResult = ref('')
const displayBrandResultStr = ref('')
const collectionType = ref(null)

// 开户方式选项
const accountOptions = [
  { text: '指令开户', value: '指令开户' },
  { text: '发卡开户', value: '发卡开户' },
]

// 是否显示开户方式选择器
const showAccountTypePicker = ref(false)

// 计算属性：表示阶梯用量字段是否应该显示
const showStairUseField = computed(() => {
  if (formData.gasbrand?.length > 0) {
    return userInfo.f_meter_type.includes('卡表') && formData.gasbrand[0].f_meter_type === '物联网表' && formData.f_ladder_sync
  }
  return false
})

// 计算属性：是否显示开户方式
const showAccountType = computed(() => {
  if (formData.gasbrand?.length > 0) {
    return formData.gasbrand[0].f_meter_type === '物联网表' && formData.gasbrand[0].f_hascard === '是'
  }
  return false
})

const customFieldName = {
  text: 'label',
  value: 'value',
  children: 'children',
}

// 旧表底数变化
function meterBaseChange() {
  if (!(formData.f_using_base_old || formData.f_using_base_old === 0)) {
    return
  }
  // 调用logic使用后台校验逻辑
  // 如果输入的旧表底数 > 用户表底数
  if (['机表', '物联网表'].includes(userInfo.f_meter_type) && formData.f_using_base_old > userInfo.f_meter_base) {
    const msg = (userInfo.f_meter_type === '机表' ? '该用户表底数大于上次抄表底数，请确定是否继续生成欠费记录' : '该用户表底数大于上次抄表底数，请确定是否进行结算')
    showConfirmDialog({
      title: '标题',
      message: msg,
    })
      .then(() => {
        runLogic('third_change_meter_hand', {
          f_userfiles_id: userInfo.f_userfiles_id,
          f_meter_base: formData.f_using_base_old,
          f_operator: currUser.resources.name,
          f_operatorid: currUser.resources.id,
          f_orgid: currUser.resources.orgid,
          f_orgname: currUser.resources.orgs,
          f_depid: currUser.resources.depids,
          f_depname: currUser.resources.deps,
        }).then(() => {
          showToast('抄表结算成功，重新加载用户信息')
          emit('meterReadingComplete', { status: true })
        })
      })
  }
  else {
    runLogic<{
      code: string
      data: {
        f_remanent_gas: number
        f_remanent_price: number
        f_remanent_money: number
      }
      msg: string
    }>('third_meter_base_check', {
      f_meter_base: formData.f_using_base_old,
      f_remanent_money: formData.f_remanent_money,
      f_remanent_price: formData.f_remanent_price,
      f_remanent_gas: formData.f_remanent_gas,
      f_userfiles_id: userInfo.f_userfiles_id,
    }).then((res) => {
      if (res.code === '0000') {
        // 赋值 补气金额/单价/气量
        Object.assign(formData, res.data)
      }
      else {
        showDialog({
          message: res.msg.replace(/，/g, '\n'),
        })
        formData.f_using_base_old = undefined
        formData.f_remanent_gas = undefined
        formData.f_remanent_price = undefined
        formData.f_remanent_money = undefined
      }
    })
  }
}

// 气表品牌变化
function meterBrandChange({ selectedOptions }) {
  formData.gasbrand = [selectedOptions[1]]
  formData.gasmodel = [selectedOptions[2]]
  collectionType.value = selectedOptions[1].f_collection_type
  displayBrandResultStr.value = selectedOptions.map(option => option.label).join(' / ')
  // 关闭抽屉
  showMeterBrandPicker.value = false
}

// 开户方式变化
function accountTypeChange({ selectedOptions }) {
  formData.f_open_type = selectedOptions[0].value
  showAccountTypePicker.value = false
}
// 表号变化进行校验
function meternumberValidate() {
  runLogic<{ code: string, msg: string }>('third_meter_number_check', {
    f_meternumber: formData.f_meternumber,
    f_userfiles_id: userInfo.f_userfiles_id,
    f_gasbrand_id: formData.gasbrand[0].id,
  }).then((res) => {
    if (res.code !== '0000') {
      showDialog({
        message: res.msg.replace(/，/g, '\n'),
      })
      formData.f_meternumber = undefined
    }
  })
}

// 表号扫描操作
function scanMeter() {
  mobileUtil.execute({
    funcName: 'scanBarcode',
    param: {},
    callbackFunc: (result: any) => {
      if (result.status === 'success' && result.data) {
        formData.f_meternumber = result.data.displayValue
        // 扫描完成后自动触发验证
        meternumberValidate()
      }
    },
  })
}

// 暴露给外部的验证方法
function validate() {
  // 验证各必填字段
  if (!formData.f_using_base_old) {
    showFailToast('请输入旧表最新底数')
    return false
  }

  if (!(formData.gasbrand && formData.gasbrand.length > 0)) {
    showFailToast('请选择新表品牌')
    return false
  }

  if (!formData.f_meter_base) {
    showFailToast('请输入新表底数')
    return false
  }

  if (!formData.f_meternumber) {
    showFailToast('请输入新表号')
    return false
  }

  // 验证开户方式
  if (showAccountType.value && !formData.f_open_type) {
    showFailToast('请选择开户方式')
    return false
  }

  return true
}

onMounted(async () => {
  // 获取气表品牌信息
  meterTypesOptions = await runLogic('mobile_getMeterBrandAndModel', { orgId: userInfo.f_orgid, queryModel: true })
})

// 暴露方法给父组件
defineExpose({
  validate,
})
</script>

<template>
  <CardContainer>
    <CardHeader title="换表基本信息" />

    <div class="new-meter-form">
      <div class="form-group">
        <div class="form-item">
          <FormField label="旧表底数">
            <Field
              v-model="formData.f_using_base_old"
              type="number"
              number="9.2"
              placeholder="请输入旧表底数"
              input-align="right"
              :rules="[{ required: true, message: '请输入旧表底数' }]"
              @blur="meterBaseChange"
            />
          </FormField>
        </div>

        <div class="form-item">
          <FormField label="新表底数">
            <Field
              v-model="formData.f_meter_base"
              type="number"
              number="9.2"
              placeholder="请输入新表底数"
              input-align="right"
              :rules="[{ required: true, message: '请输入新表底数' }]"
            />
          </FormField>
        </div>

        <div class="form-item">
          <FormField label="新表品牌/型号" @click="showMeterBrandPicker = true">
            <Field
              v-model="displayBrandResultStr"
              is-link
              readonly
              placeholder="请选择新表品牌"
            />
            <Popup
              v-model:show="showMeterBrandPicker"
              position="bottom"
              teleport="body"
            >
              <van-cascader
                v-model="displayBrandResult"
                title="选择表具品牌"
                :options="meterTypesOptions"
                :field-names="customFieldName"
                @close="showMeterBrandPicker = false"
                @finish="meterBrandChange"
              />
            </Popup>
          </FormField>
        </div>

        <!-- 恢复新表计费类型字段 -->
        <div v-if="collectionType" class="form-item">
          <FormField label="新表计费类型">
            <Field
              v-model="collectionType"
              readonly
              input-align="left"
            />
          </FormField>
        </div>

        <!-- 新增：新表号输入框，只有当三个字段都选择后才显示 -->
        <div v-if="formData.gasbrand?.length > 0" class="form-item">
          <FormField label="新表号">
            <Field
              v-model="formData.f_meternumber"
              placeholder="请输入新表号"
              input-align="right"
              :rules="[{ required: true, message: '请输入新表号' }]"
              @blur="meternumberValidate"
            >
              <template #right-icon>
                <van-icon
                  name="scan"
                  class="scan-icon"
                  @click="scanMeter"
                />
              </template>
            </Field>
          </FormField>
        </div>

        <!-- 开户方式选择器 -->
        <div v-if="showAccountType" class="form-item">
          <FormField label="开户方式">
            <Field
              v-model="formData.f_open_type"
              is-link
              readonly
              placeholder="请选择开户方式"
              @click="showAccountTypePicker = true"
            />
            <Popup
              v-model:show="showAccountTypePicker"
              position="bottom"
              teleport="body"
            >
              <Picker
                title="选择开户方式"
                :columns="accountOptions"
                @confirm="accountTypeChange"
                @cancel="showAccountTypePicker = false"
              />
            </Popup>
          </FormField>
        </div>

        <!-- 修改：阶梯用量输入框，仅当表类型为物联网表且累计旧表阶梯用量为true时显示 -->
        <div v-if="showStairUseField" class="form-item">
          <FormField label="阶梯用量">
            <Field
              v-model="formData.f_stair_use"
              type="digit"
              placeholder="请输入阶梯用量"
              input-align="right"
            />
          </FormField>
        </div>
      </div>
    </div>
  </CardContainer>
</template>

<style lang="less" scoped>
.new-meter-form {
  padding: 8px;

  .form-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .form-item {
    width: calc(33.33% - 6px);
  }

  /* 移动端样式：一行一个 */
  @media screen and (max-width: 767px) {
    .form-item {
      width: 100%;
    }
  }

  /* 平板样式：一行两个 */
  @media screen and (min-width: 768px) and (max-width: 1024px) {
    .form-group {
      gap: 10px;
    }

    .form-item {
      width: calc(50% - 5px);
      flex: 0 0 calc(50% - 5px);
    }
  }
}

.field-placeholder {
  color: #999;
}

/* 添加van-field样式 */
:deep(.van-field) {
  background-color: #f9fafb;
  border-radius: 6px;
}

:deep(.van-field__control) {
  color: #333;
  font-size: 14px;

  &::placeholder {
    color: #999;
  }
}

:deep(.van-field--disabled .van-field__control) {
  color: #999;
}

/* 添加扫描图标样式 */
.scan-icon {
  font-size: 20px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: var(--van-primary-color);
  }
}
</style>
