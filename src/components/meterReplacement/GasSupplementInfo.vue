<script setup lang="ts">
import type { MeterReplacementFormData } from './types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { Field, Radio, RadioGroup } from 'vant'
import { FormField } from '@/components/common'

defineProps({
  showSetGasSync: {
    type: Boolean,
    default: true,
  },
  tenantAlias: {
    type: String,
    default: 'standard',
  },
})

const formData = inject('formData') as MeterReplacementFormData

const showGas = computed(() => {
  if (formData.gasbrand?.length > 0) {
    return formData.gasbrand[0].f_collection_type === '按气量'
  }
  return true
})
</script>

<template>
  <CardContainer>
    <CardHeader title="补气信息" />

    <div class="gas-supplement-info">
      <div class="form-group">
        <div v-if="showSetGasSync" class="form-item usage-toggle">
          <FormField label="累计旧表阶梯用量">
            <RadioGroup v-model="formData.f_ladder_sync" direction="horizontal">
              <Radio :name="true">
                是
              </Radio>
              <Radio :name="false">
                否
              </Radio>
            </RadioGroup>
          </FormField>
        </div>

        <div v-show="tenantAlias === 'Xianyang' ? true : showGas" class="form-item">
          <FormField label="需补气量">
            <Field
              v-model="formData.f_remanent_gas"
              type="number"
              placeholder="请输入补气量"
              input-align="right"
              :readonly="tenantAlias === 'Xianyang'"
              :formatter="val => Number(val).toFixed(2)"
              :rules="[{ required: true, message: '请输入补气量' }]"
            />
          </FormField>
        </div>

        <div class="form-item">
          <FormField label="补气单价">
            <Field
              v-model="formData.f_remanent_price"
              type="number"
              placeholder="请输入补气单价"
              input-align="right"
              :readonly="tenantAlias === 'Xianyang'"
              :formatter="val => Number(val).toFixed(2)"
              :rules="[{ required: true, message: '请输入补气单价' }]"
            />
          </FormField>
        </div>

        <div class="form-item">
          <FormField label="补气金额">
            <Field
              v-model="formData.f_remanent_money"
              type="number"
              input-align="right"
              :formatter="val => Number(val).toFixed(2)"
            >
              <template #prefix>
                <span class="gas-supplement-info__currency">¥</span>
              </template>
            </Field>
          </FormField>
        </div>
      </div>
    </div>
  </CardContainer>
</template>

<style lang="less" scoped>
.gas-supplement-info {
  padding: 8px;

  .form-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .form-item {
    width: calc(33.33% - 6px);

    &.usage-toggle {
      width: 100%;
      margin-bottom: 8px;
    }

    /* 移动端样式：一行一个 */
    @media screen and (max-width: 767px) {
      width: 100%;
    }

    /* 平板样式：一行两个 */
    @media screen and (min-width: 768px) and (max-width: 1024px) {
      width: calc(50% - 5px);
      flex: 0 0 calc(50% - 5px);

      &.usage-toggle {
        width: 100%;
        flex: 0 0 100%;
      }
    }
  }

  &__currency {
    color: #666;
    margin-right: 4px;
  }
}

:deep(.van-radio-group) {
  display: flex;
  gap: 24px;
}

:deep(.van-field) {
  background-color: #f9fafb;
  border-radius: 6px;
}

:deep(.van-field__control) {
  color: #333;
  font-size: 14px;

  &::placeholder {
    color: #999;
  }
}

:deep(.van-field--disabled .van-field__control) {
  color: #999;
}

:deep(.van-switch) {
  margin-left: 8px;
}

.field-placeholder {
  :deep(.van-field__control) {
    color: #999;
  }
}
</style>
