<script setup lang="ts">
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from 'af-mobile-client-vue3/src/stores/modules/user'
import { showToast } from 'vant'
import { computed, ref } from 'vue'
import useBusinessStore from '@/stores/modules/business'
import 'vant/es/toast/style'

const emit = defineEmits(['closeOperation', 'complete'])
const businessStore = useBusinessStore()
const userInfo = computed(() => businessStore.currentUser)
const currUser = useUserStore().getLogin().f

// 参数设置
const qr = ref('0') // 是否开启失联关阀
const zq = ref('0') // 是否开启过流保护
const outcant = ref('30') // 失联关阀天数
const outweekcant = ref('180') // 周期关阀天数
const clickConfirm = ref(false)

// 保存参数设置
async function saveParameters() {
  // 验证参数
  if (Number(outcant.value) > 30) {
    showToast('失联关阀天数不可以大于30天')
    return
  }
  clickConfirm.value = true

  const data = {
    userId: `${userInfo.value.f_userfiles_id}`,
    title: '设置表具参数',
    content: {
      setType: '设置运行参数',
      overflowingAlarm: `${zq.value}`,
      lostCloseValveSign: `${qr.value}`,
      closingValveCycle: `${outweekcant.value}`,
      lostCloseValveDays: `${outcant.value}`,
    },
    alias: `${userInfo.value.f_alias}`,
    inputtor: `${currUser.resources.name}`,
    moduleName: 'WeiSiDaSyncNB',
    f_userfiles_id: userInfo.value.f_userfiles_id,
    f_meternumber: userInfo.value.f_meternumber,
    f_userinfo_id: userInfo.value.f_userinfo_id,
    f_user_id: userInfo.value.f_user_id,
    f_orgid: currUser.resources.orgid,
    f_orgname: currUser.resources.orgs,
    f_depid: currUser.resources.depids,
    f_depname: currUser.resources.deps,
    f_operatorid: currUser.resources.id,
    f_operator: currUser.resources.name,
  }
  runLogic('out_conacnt', data, 'af-revenue').then((result) => {
    showToast('设置参数成功')
    clickConfirm.value = false
    emit('complete', { status: true })
  }).catch((err) => {
    showToast(`设置参数失败${err.message || err.msg || '未知错误'}`)
    clickConfirm.value = false
  })
}

function cancel() {
  emit('closeOperation')
}
</script>

<template>
  <div>
    <CardContainer class="parameter-setting">
      <CardHeader title="参数设置" />

      <div class="p-4">
        <!-- 失联关阀设置 -->
        <van-cell-group inset class="mb-4">
          <van-cell title="失联关阀设置" class="font-bold" />
          <van-cell>
            <template #title>
              <div class="flex items-center">
                <span class="text-5xl mr-2">是否开启失联关阀</span>
              </div>
            </template>
            <template #right-icon>
              <van-radio-group v-model="qr" direction="horizontal">
                <van-radio name="1" class="mr-4">
                  是
                </van-radio>
                <van-radio name="0">
                  否
                </van-radio>
              </van-radio-group>
            </template>
          </van-cell>
          <van-field
            v-model="outcant"
            label="失联关阀天数"
            type="digit"
            input-align="right"
            placeholder="请输入天数"
            :disabled="qr === '0'"
          >
            <template #button>
              <span class="text-gray-500 ml-2">天</span>
            </template>
          </van-field>
        </van-cell-group>

        <!-- 过流保护设置 -->
        <van-cell-group inset class="mb-6">
          <van-cell title="过流保护设置" class="font-bold" />
          <van-cell>
            <template #title>
              <div class="flex items-center">
                <span class="text-5xl mr-2">是否开启过流保护</span>
              </div>
            </template>
            <template #right-icon>
              <van-radio-group v-model="zq" direction="horizontal">
                <van-radio name="1" class="mr-4">
                  是
                </van-radio>
                <van-radio name="0">
                  否
                </van-radio>
              </van-radio-group>
            </template>
          </van-cell>
          <van-field
            v-model="outweekcant"
            label="周期关阀天数"
            type="digit"
            input-align="right"
            placeholder="请输入天数"
            :disabled="zq === '0'"
          >
            <template #button>
              <span class="text-gray-500 ml-2">天</span>
            </template>
          </van-field>
        </van-cell-group>

        <!-- 操作按钮 -->
        <div class="action mt-6">
          <van-button
            plain type="default"
            :loading="clickConfirm"
            @click="cancel"
          >
            取消
          </van-button>
          <van-button
            type="primary"
            native-type="submit"
            :loading="clickConfirm"
            @click="saveParameters"
          >
            保存更改
          </van-button>
        </div>
      </div>
    </CardContainer>
  </div>
</template>

<style scoped lang="less">
.parameter-setting {
  margin-bottom: 16px;
}

.action {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}
// 组件内部的字体样式定义
.text-4xl {
  font-size: 11px;
  letter-spacing: 0.5px;
}

.text-5xl {
  font-size: 12px;
  letter-spacing: 1px;
  line-height: 1.6;
}

.text-6xl {
  font-size: 13px;
  letter-spacing: 1px;
  line-height: 1.6;
}

// 为了防止字体过小，提高 van-radio 的字体大小
:deep(.van-radio__label) {
  font-size: 12px;
}

// 设置输入框的样式
:deep(.van-field__label) {
  font-size: 12px;
  letter-spacing: 0.5px;
}

:deep(.van-field__control) {
  font-size: 12px;
}
</style>
