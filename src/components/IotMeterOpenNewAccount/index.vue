<script setup lang="ts">
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from 'af-mobile-client-vue3/src/stores/modules/user'
import { showDialog, showToast } from 'vant'
import { onMounted, ref } from 'vue'
import useBusinessStore from '@/stores/modules/business'
import { formatTime } from '@/utils/date'

const emit = defineEmits(['closeOperation', 'complete'])
// 初始化状态
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
const clickConfirm = ref(false)
const operateReason = ref('')

function saveRecord() {
  const data = {
    f_operat_type: '重新开户',
    f_describe: `${currUser.resources.name}对用户${userInfo.value.f_user_name}进行重新开户操作`,
    f_state: '有效',
    f_comments: operateReason.value,
    f_operate_date: formatTime(new Date()),
    f_userfiles_id: userInfo.value.f_userfiles_id,
    f_userinfo_id: userInfo.value.f_userinfo_id,
    f_user_id: userInfo.value.f_user_id,
    f_orgid: currUser.resources.orgid,
    f_orgname: currUser.resources.orgs,
    f_depid: currUser.resources.depids,
    f_depname: currUser.resources.deps,
    f_operator: currUser.resources.name,
    f_operatorid: currUser.resources.id,
  }
  runLogic('saveOperateRecord', data, 'af-revenue').then(() => {
    emit('complete', { status: true })
    showToast(`重新开户成功`)
  })
}

function openAccount() {
  if (!operateReason.value) {
    showToast(`请填写操作原因`)
    return
  }
  showDialog({
    title: '提示',
    message: `确认要对用户${userInfo.value.f_user_name}进行重新开户操作吗？`,
    showCancelButton: true,
  }).then(async () => {
    const data = {
      f_userfiles_id: userInfo.value.f_userfiles_id,
    }
    runLogic('startup', data, 'af-revenue').then((res) => {
      console.log(res)
      saveRecord()
    }).catch((error) => {
      console.error(error)
      showToast(`重新开户失败${error.message || error.msg || '未知错误'}`)
    })
  }).catch(() => {
  })
}
function cancel() {
  emit('closeOperation')
}

onMounted(() => {

})
</script>

<template>
  <div class="iot-meter-open-new-account">
    <form @submit.prevent="openAccount">
      <CardContainer class="iot-meter-open-new-account">
        <CardHeader title="操作原因" />
        <div class="iot-meter-open-new-account__section">
          <div class="iot-meter-open-new-account__selector">
            <van-field
              v-model="operateReason"
              type="textarea"
              placeholder="填写操作原因"
              rows="2"
              autosize
              maxlength="200"
              show-word-limit
            />
          </div>
        </div>
      </CardContainer>
      <!-- 按钮区域 -->
      <div class="iot-meter-open-new-account__actions">
        <van-button
          plain type="default"
          :loading="clickConfirm"
          @click="cancel"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          native-type="submit"
          :loading="clickConfirm"
        >
          确认
        </van-button>
      </div>
    </form>
  </div>
</template>

<style scoped lang="less">
.iot-meter-open-new-account {
  &__content {
    margin-bottom: 16px;
  }
  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
  }
  &__section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 4px;
  }

  &__selector {
    position: relative;

    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  :deep(.van-field__control) {
    background-color: #f9fafb;
    border-radius: 6px;
  }
}
</style>
