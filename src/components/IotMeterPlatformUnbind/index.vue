<script setup lang="ts">
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import { post } from '@af-mobile-client-vue3/services/restTools'
import useUserStore from 'af-mobile-client-vue3/src/stores/modules/user'
import { showDialog, showToast } from 'vant'
import { ref } from 'vue'
import useBusinessStore from '@/stores/modules/business'
import { formatTime } from '@/utils/date'

const emit = defineEmits(['closeOperation', 'complete'])
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
const permissions = useUserStore().getPermissions()
const balanceAmount = ref(userInfo.value.f_balance_amount)
const reportDate = ref(userInfo.value.f_meteread_date !== undefined && userInfo.value.f_meteread_date ? userInfo.value.f_meteread_date : '')
const clickConfirm = ref(false)
const showDatePicker = ref(false)
const operateReason = ref('')

// 日期时间选择数据
const dateTimePickerValue = ref(undefined)
function showDataTimePicker() {
  if (userInfo.value.f_meteread_date !== undefined
    && userInfo.value.f_meteread_date !== ''
    && userInfo.value.f_meteread_date !== null
    && typeof userInfo.value.f_meteread_date === 'string') {
    const [dateStr, timeStr] = userInfo.value.f_meteread_date.split(' ')
    const date = dateStr.split('-')
    const time = timeStr.split(':')
    dateTimePickerValue.value = {
      date,
      time,
    }
  }
  else {
    dateTimePickerValue.value = {
      date: ['2015', '01', '01'],
      time: ['00', '00', '00'],
    }
  }
  showDatePicker.value = true
}

function onDateTimePickerConfirm() {
  showDatePicker.value = false
  const dateStr = dateTimePickerValue.value.date.join('-')
  const timeStr = dateTimePickerValue.value.time.join(':')
  reportDate.value = `${dateStr} ${timeStr}`
}

function onPickerCancel() {
  showDatePicker.value = false
}

function saveChangeMeterTemplate(data) {
  try {
    post('webmeter/rs/logic/syncSaveChangeMeterTemplate', data).then(() => {
      emit('complete', { status: true })
      showToast(`平台接绑成功`)
    })
  }
  catch (error) {
    showToast(`平台接绑失败`)
    const param = {
      f_balance_amount: balanceAmount.value,
      f_table_state: '正常',
      f_meteread_date: reportDate.value,
      f_userfiles_id: userInfo.value.f_userfiles_id,
    }
    runLogic('saveUserfiles', param, 'af-revenue')
  }
}

function confirmUnbind() {
  showDialog({
    title: '提示',
    message: `确认要对用户${userInfo.value.f_user_name}进行平台解绑操作吗？`,
    showCancelButton: true,
  }).then(async () => {
    const data = {
      object: {
        f_userfiles_id_old: userInfo.value.f_userfiles_id,
      },
      f_meteread_date: reportDate.value,
      f_balance_amount: balanceAmount.value,
      f_operat_type: '解除绑定',
      f_describe: `${currUser.resources.name}对用户${userInfo.value.f_user_name}进行解除绑定操作`,
      f_state: '有效',
      f_comments: operateReason.value,
      f_operate_date: formatTime(new Date()),
      f_userfiles_id: userInfo.value.f_userfiles_id,
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_user_id: userInfo.value.f_user_id,
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
    }
    runLogic('updateUnbindInfo', data, 'af-revenue').then((res) => {
      console.log(res)
      saveChangeMeterTemplate(res)
    }).catch((error) => {
      console.error(error)
      showToast(`平台解绑失败${error.message || error.msg || '未知错误'}`)
    })
  }).catch(() => {
  })
}
function cancel() {
  emit('closeOperation')
}
</script>

<template>
  <div class="iot-meter-platform-unbind">
    <form @submit.prevent="confirmUnbind">
      <CardContainer class="iot-meter-platform-unbind__content">
        <CardHeader title="表具信息" />
        <div
          class="text-list-item"
        >
          {{ `表号：${userInfo.f_meternumber ? userInfo.f_meternumber : ''}` }}
        </div>
        <div
          class="text-list-item"
        >
          {{ `表识别码：${userInfo.f_imei ? userInfo.f_imei : ''}` }}
        </div>
      </CardContainer>
      <CardContainer class="iot-meter-platform-unbind__content">
        <CardHeader title="调整信息" />
        <div class="iot-meter-platform-unbind__section">
          <label class="iot-meter-platform-unbind__label">最后上报时间</label>
          <div class="iot-meter-platform-unbind__selector">
            <!-- 日期选择-非查询 -->
            <VanField
              v-model="(reportDate as string | number)"
              name="datePicker"
              input-align="left"
              readonly
              :is-link="true"
              placeholder="最后上报时间"
              @click="permissions.includes('结算金额修改') ? showDataTimePicker() : null"
            />
            <VanPopup v-model:show="showDatePicker" position="bottom" teleport="body" overlay-class="date-picker-overlay">
              <VanPickerGroup
                title="最后上报时间"
                :tabs="['选择日期', '选择时间']"
                next-step-text="下一步"
                confirm-button-text="确认"
                cancel-button-text="取消"
                @confirm="onDateTimePickerConfirm"
                @cancel="onPickerCancel"
              >
                <VanDatePicker
                  v-model="dateTimePickerValue.date"
                  :columns-type="['year', 'month', 'day']"
                />
                <VanTimePicker
                  v-model="dateTimePickerValue.time"
                  :columns-type="['hour', 'minute', 'second']"
                  min-time="00:00:00"
                  max-time="23:59:59"
                />
              </VanPickerGroup>
            </VanPopup>
          </div>
          <label class="iot-meter-platform-unbind__label">结算金额</label>
          <div class="iot-meter-platform-unbind__selector">
            <van-field
              v-model="balanceAmount"
              type="text"
              :readonly="!permissions.includes('结算金额修改')"
              placeholder="结算金额"
            />
          </div>
          <label class="iot-meter-platform-unbind__label">备注</label>
          <div class="iot-meter-platform-unbind__selector">
            <van-field
              v-model="operateReason"
              type="textarea"
              placeholder="填写备注"
              rows="2"
              autosize
              maxlength="200"
              show-word-limit
            />
          </div>
        </div>
      </CardContainer>
      <!-- 按钮区域 -->
      <div class="iot-meter-platform-unbind__actions">
        <van-button
          plain type="default"
          :loading="clickConfirm"
          @click="cancel"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          native-type="submit"
          :loading="clickConfirm"
        >
          确认
        </van-button>
      </div>
    </form>
  </div>
</template>

<style scoped lang="less">
.iot-meter-platform-unbind {
  &__content {
    margin-bottom: 16px;
  }
  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
  }
  &__section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 4px;
  }

  &__selector {
    position: relative;

    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  :deep(.van-field__control) {
    background-color: #f9fafb;
    border-radius: 6px;
  }
}
.text-list-item {
  padding: 8px 0;
  text-align: left;
  font-size: 13px;
  color: #6b7280;
}
</style>
