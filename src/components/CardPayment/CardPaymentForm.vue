<script setup lang="ts">
import type { BusinessUser } from '../OnlineRevenue/types'
import type { FileItem } from '@/components/common/FileUploader.vue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { showConfirmDialog, showDialog, showFailToast, showToast } from 'vant'
import { computed, onMounted, reactive, ref } from 'vue'
import { ChargePrintSelectorAndRemarks, CodePayment, FileUploader, GridFileUploader, InfoCard, PaymentMethodSelectorCard, ReceiptModal } from '@/components/common'
import { businessGetLimitGas, commonCal } from '@/services/api/business'
import { sellGas } from '@/services/cardService'
import useBusinessStore from '@/stores/modules/business'
import CardGasCalculator from './CardGasCalculator.vue'
import CardPaymentInfo from './CardPaymentInfo.vue'

interface FormData {
  payment: string
  gas: string | null
  money: string
  print: string
  balance: string
  totalCost: string
  collection: string
  comments: string
  serial_number: string
  serial_id: string
  voucher_number: string
  privilege_id: string
  privilege_money: number
  after_discount: number
  purchase: number
  writeCardMoney: string
  curBalance: string
  chargePrice: any[]
  getDeduct: number
  fileList?: any[]
  receiptType?: string
  remarks?: string
  [key: string]: any
}

interface ReceiptData {
  transactionId: string
  datetime: string
  [key: string]: any
}

// 定义配置选项接口
interface ConfigOptions {
  calculatePreByCollection: boolean
  hasPrint: boolean
  billType: string
  printType: string
  payment: string
  floor: boolean
  allowedMethods: string[] | null
  [key: string]: any
  printLogic: string
  fileTypes?: {
    userType: string
    picMinNum: number
    description?: string
  }[]
}

const emit = defineEmits(['closeOperation', 'complete', 'cancel'])

// 定义内部config配置
const config = reactive<ConfigOptions>({
  calculatePreByCollection: false,
  hasPrint: true,
  billType: '燃气费',
  printType: '普通收据',
  payment: '',
  allowedMethods: null,
  floor: false,
  printLogic: '',
  fileTypes: [],
})

const formData = reactive<FormData>({
  payment: '现金缴费',
  gas: '',
  money: '',
  print: '',
  balance: '',
  totalCost: '',
  collection: null,
  comments: '',
  serial_number: '',
  serial_id: '',
  voucher_number: '',
  privilege_id: '0',
  privilege_money: 0,
  after_discount: 0,
  purchase: 0,
  writeCardMoney: '',
  curBalance: '',
  chargePrice: [],
  getDeduct: 0,
})

// 初始化状态
const loading = ref(true)

// 限购相关状态
const hasLimit = ref(false)
const maxGas = ref(99999999)
const maxMoney = ref(99999999)
const allLimit = ref(null)
const limitReason = ref('')
const limitGas = ref(false)
const limitMoney = ref(false)
// 收款验证状态
const paymentValid = ref(false)
// 计算公式显示
const calculateDetail = ref('')
const gasPrice = ref('')
const deductDetail = ref('')
// 子组件的响应组件引用
const printRef = ref()
// 其他状态
const clickConfirm = ref(false)
// 当前用户
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
const receiptData = ref<ReceiptData>({
  transactionId: '',
  datetime: '',
})
// 文件上传相关数据
const fileList = ref<FileItem[]>([])
const gridFileUploaderRef = ref()
const showQrCodePayment = ref(false)

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

function calText(val: any[], getDeduct = 0): void {
  let str = ''
  let num = 0
  val.forEach((item) => {
    if (item.f_gas > 0) {
      num = num + 1
      if (userInfo.value.f_isdecimal === '是') {
        str = `${str}${item.f_gas} x ${item.f_price}+`
      }
      else {
        str = `${str}${Math.floor(item.f_gas)} x ${item.f_price}+`
      }
    }
  })

  if (getDeduct > 0) {
    if (Number(formData.gas) * (userInfo.value.f_gas_type || 0) > (userInfo.value.f_project_money || 0)) {
      deductDetail.value = `当前未结金额${userInfo.value.f_project_money}元，不足抵扣，以未结金额为准`
    }
    else {
      deductDetail.value = `未结金额计算公式:${userInfo.value.f_gas_type} x ${formData.gas}=${getDeduct}`
    }
  }

  str = str.slice(0, str.length - 1)
  calculateDetail.value = str

  if (num > 1) {
    showToast('本次购气已跨阶梯')
  }
}

function getDeduct(row: BusinessUser, gas: string): string {
  let result = ''
  if (row.f_gas_type) {
    if (row.f_gas_type * Number(gas) >= (row.f_project_money || 0)) {
      result = (row.f_project_money || 0).toFixed(2)
    }
    else {
      result = (row.f_gas_type * Number(gas)).toFixed(2)
    }
  }
  return result
}

function cleanGasAndMoney() {
  formData.gas = ''
  formData.money = ''
  formData.totalCost = ''
  formData.collection = ''
}

async function calculateByGas(value: string): Promise<void> {
  if (!value || Number(value) <= 0) {
    formData.gas = null
    paymentValid.value = false
    return
  }

  // 验证最少购买1方气
  if (Number(value) < 1) {
    showFailToast('最少购买1方气')
    paymentValid.value = false
    return
  }

  formData.gas = value
  try {
    // 验证气量限制
    if (Number(formData.gas) > Number(maxGas.value)) {
      showFailToast(`购气量超过限制: ${maxGas.value}m³，请重新输入`)
      paymentValid.value = false
      cleanGasAndMoney()
      return
    }
    // 气量上限检查
    if ((userInfo.value.f_topup_ceil || 0) > 0 && Number(formData.gas) > (userInfo.value.f_topup_ceil || 0)) {
      showToast(`您输入的气量不能大于该表的充值上限: ${userInfo.value.f_topup_ceil}`)
      formData.gas = null
      paymentValid.value = false
      return
    }
    if (userInfo.value.f_isdecimal === '是') {
      formData.gas = Number(formData.gas).toFixed(userInfo.value.f_gas_decimal || 4)
    }
    else {
      formData.gas = Number(formData.gas).toFixed(0)
    }
    // 计算金额
    const result = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      gas: formData.gas,
    })
    formData.getDeduct = 0
    // 计算抵扣包间费用抵扣金额
    if ((userInfo.value.f_project_money || 0) > 0) {
      formData.getDeduct = Number(getDeduct(userInfo.value, formData.gas || '0'))
    }
    formData.money = Number(result.money).toFixed(userInfo.value.f_fee_decimal || 4)

    const balanceNumber = Number(userInfo.value.f_user_balance)
    const moneyNumber = Number(result.money)
    const deductNumber = formData.getDeduct

    // 验证金额限制
    if (Number(formData.money) > Number(maxMoney.value)) {
      showFailToast(`购气金额超过限制: ${maxMoney.value}元，请重新输入`)
      cleanGasAndMoney()
      paymentValid.value = false
      return
    }
    if (balanceNumber > (moneyNumber + deductNumber)) {
      formData.totalCost = '0'
    }
    else {
      formData.totalCost = ((moneyNumber + deductNumber) - balanceNumber).toFixed(userInfo.value.f_fee_decimal || 4)
    }
    formData.collection = formData.totalCost
    if (config.floor) {
      formData.collection = String(Math.ceil(Number(formData.collection)))
    }
    paymentValid.value = true
    calculateCurrentBalance()
    calText(result.chargePrice, formData.getDeduct)
  }
  catch (error: any) {
    showToast(`计算失败:${error.message || error.msg || '未知错误'}`)
  }
}

// 输入金额，换算气量
async function calculateByAmount(value: string): Promise<void> {
  formData.money = value
  try {
    // 验证金额限制
    if (Number(formData.money) > Number(maxMoney.value)) {
      showFailToast(`购气金额超过限制: ${maxMoney.value}元，请重新输入`)
      cleanGasAndMoney()
      paymentValid.value = false
      return
    }

    const calFee = Number(formData.money).toFixed(userInfo.value.f_fee_decimal || 4)

    // 计算金额
    const getGas = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      money: calFee,
    })

    if (getGas.gas) {
      if (userInfo.value.f_isdecimal === '是') {
        if (userInfo.value.f_alias === 'QiaoSong') {
          formData.gas = Number(getGas.gas).toFixed(1)
        }
        else {
          formData.gas = Number(getGas.gas).toFixed(userInfo.value.f_gas_decimal || 4)
        }
      }
      else {
        // 如果不支持小数,将划价出的多余非整数气量进行划价为金额
        formData.gas = Math.floor(Number(getGas.gas)).toString()
      }

      // 验证最少购买1方气
      if (Number(formData.gas) < 1) {
        showFailToast('最少购买1方气，请增加购气金额')
        formData.money = null
        paymentValid.value = false
        return
      }
      if (Number(formData.gas) > Number(maxGas.value)) {
        showFailToast(`购气气量超过限制: ${maxGas.value}m³，请重新输入`)
        cleanGasAndMoney()
        paymentValid.value = false
        return
      }
    }
    formData.getDeduct = 0
    // 计算抵扣包间费用抵扣金额
    if ((userInfo.value.f_project_money || 0) > 0) {
      formData.getDeduct = Number(getDeduct(userInfo.value, formData.gas || '0'))
    }

    const balanceNumber = Number(userInfo.value.f_user_balance)
    const moneyNumber = Number(formData.money)
    const deductNumber = formData.getDeduct

    if (balanceNumber >= (moneyNumber + deductNumber)) {
      formData.totalCost = '0'
    }
    else {
      formData.totalCost = ((moneyNumber + deductNumber) - balanceNumber).toFixed(userInfo.value.f_fee_decimal || 4)
    }

    formData.chargePrice = getGas.chargeprice
    formData.collection = formData.totalCost
    calculateCurrentBalance()
    calText(getGas.chargePrice, formData.getDeduct)
    paymentValid.value = true
  }
  catch (error: any) {
    showToast(`计算失败:${error.message || error.msg || '未知错误'}`)
  }
}

async function calculatePreByCollection(collection: string): Promise<void> {
  formData.collection = collection
  try {
    const calFee = (Number(formData.collection) + Number(userInfo.value.f_user_balance)).toFixed(4)

    // 通过收款进行划价
    const getGas = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      money: calFee,
    })
    formData.gas = getGas.gas

    // 验证最少购买1方气
    if (Number(formData.gas) < 1) {
      showFailToast('最少购买1方气，请增加收款金额')
      formData.collection = null
      paymentValid.value = false
      return
    }

    const redundant = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      gas: formData.gas,
    })

    formData.totalCost = redundant.money
    formData.money = redundant.money
    formData.getDeduct = 0

    if ((userInfo.value.f_project_money || 0) > 0) {
      formData.getDeduct = Number(getDeduct(userInfo.value, formData.gas || '0'))
      formData.totalCost = String(Number(formData.totalCost) + formData.getDeduct)
      formData.collection = String(Number(formData.collection) + formData.getDeduct)
    }
    calculateCurrentBalance()
    calText(redundant.chargePrice, formData.getDeduct)
    paymentValid.value = true
  }
  catch (error: any) {
    showToast(`计算失败:${error.message || error.msg || '未知错误'}`)
  }
}

function receivedAmountChange(value: string): void {
  formData.collection = value
  // 验证金额限制
  if (Number(formData.collection) > Number(maxMoney.value)) {
    showFailToast(`购气金额超过限制: ${maxMoney.value}元，请重新输入`)
    cleanGasAndMoney()
    paymentValid.value = false
    return
  }
  // 如果配置了通过收款金额反向计算，则执行反向计算
  if (config.calculatePreByCollection) {
    calculatePreByCollection(value)
  }
  calculateCurrentBalance()
}

function calculateCurrentBalance(): void {
  if (config.calculatePreByCollection) {
    formData.curBalance = (Number(formData.collection) + Number(formData.balance) - Number(formData.money)).toFixed(4)
    return
  }

  if (formData.money) {
    if (Number(formData.balance) > Number(formData.money)) {
      formData.curBalance = (Number(formData.balance) - Number(formData.money) + Number(formData.collection)).toFixed(4)
    }
    else {
      formData.curBalance = (Number(formData.collection) - Number(formData.totalCost)).toFixed(4)
    }
  }
  else {
    formData.curBalance = formData.balance
  }
}

// 提交
async function onSubmit(): Promise<void> {
  // 验证必填字段
  if (!formData.payment) {
    showToast('请选择付款方式')
    return
  }
  if (!formData.print) {
    showToast('请选择打印格式')
    return
  }
  // 验证收款金额必须大于1
  if (!formData.collection || Number(formData.collection) < 1) {
    showToast('收款金额必须大于1元')
    return
  }
  // 验证最少购买1方气
  if (!formData.gas || Number(formData.gas) < 1) {
    showToast('最少购买1方气')
    return
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return
    }
  }
  clickConfirm.value = true
  await showConfirmDialog({
    title: '确认',
    message: `确定对客户${userInfo.value.f_user_name}进行卡表收费吗？`,
  }).then(async () => {
    try {
      // 微信/支付宝支付走单独的流程，通过事件回调处理支付结果
      if (formData.payment.includes('支付宝') || formData.payment.includes('微信')) {
        showQrCodePayment.value = true
      }
      // 其他支付方式直接调用收费接口进行处理
      else {
        await processPayment()
      }
    }
    catch (error) {
      console.error(error)
    }
  }).catch(() => {
    clickConfirm.value = false
  })
}

// 抽取支付处理逻辑为单独函数
async function processPayment(tradeNo = ''): Promise<any> {
  try {
    formData.writeCardMoney = formData.money
    // 提取文件结果数组
    const fileResults = fileList.value
      .filter(file => file.result) // 过滤出有result属性的文件
      .map(file => file.result.id) // 提取result属性

    formData.fileList = fileResults.length > 0 ? fileResults : []
    // 组织操作人信息
    const operator = {
      orgId: currUser.resources.orgid,
      orgName: currUser.resources.orgs,
      depId: currUser.resources.depids,
      depName: currUser.resources.deps,
      operator: currUser.resources.name,
      operatorId: currUser.resources.id,
    }
    // 如果有凭证号，则设置
    if (tradeNo) {
      formData.serial_id = tradeNo
    }
    const result = await sellGas(formData, userInfo.value, operator)
    receiptData.value = {
      transactionId: result.id,
      datetime: new Date().toLocaleString(),
    }
    await printRef.value.printParams(config.printLogic, receiptData.value.transactionId)
  }
  catch (error) {
    console.error(error)
    showToast(`卡表收费失败${error.message || error.msg || '未知错误'}`)
    clickConfirm.value = false
    throw error
  }
}

async function handleQrCodePaymentSuccess(paymentResult) {
  try {
    // 使用公共处理函数处理支付
    await processPayment(paymentResult.tradeNo)
    showQrCodePayment.value = false
    clickConfirm.value = false
  }
  catch (error) {
    console.error(error)
    clickConfirm.value = false
  }
}

function handleQrCodePaymentCancel() {
  showQrCodePayment.value = false
  clickConfirm.value = false
}

// 取消操作
function handleCancel(): void {
  showDialog({
    title: '确认取消',
    message: '是否确认取消当前收费操作？',
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '返回',
    confirmButtonColor: '#2563eb',
  }).then((result) => {
    if (result === 'confirm') {
      emit('closeOperation')
    }
  })
}

async function getLimitGas(): Promise<void> {
  try {
    const response = await businessGetLimitGas(
      userInfo.value.f_userinfo_id,
      userInfo.value.f_user_id || '',
      userInfo.value.f_stairprice_id || '',
    )
    console.log('获取限购值结果：', response)
    // 处理限购逻辑
    hasLimit.value = response.hasLimit
    if (hasLimit.value) {
      if (response.f_limit_value || response.f_limit_amount) {
        if (response.f_limit_value < 0 || response.f_limit_amount < 0) {
          maxMoney.value = 0
          allLimit.value = response.all_limit ? Number(response.all_limit) : null
          limitReason.value = response?.f_limit_comments
          limitMoney.value = true
          showToast(response.msg || '您已达到限购上限')
        }
        else {
          if (response.f_limit_value) {
            allLimit.value = response.all_limit ? Number(response.all_limit) : null
            limitReason.value = response?.f_limit_comments
            maxGas.value = Number(response.f_limit_value)
            limitGas.value = true
          }
          if (response.f_limit_amount) {
            allLimit.value = response.all_limit ? Number(response.all_limit) : null
            limitReason.value = response?.f_limit_comments
            maxMoney.value = Number(response.f_limit_amount)
            limitMoney.value = true
          }
        }
      }
    }
  }
  catch (error: any) {
    console.error('获取限购值失败', error)
    showFailToast('获取限购值失败')
  }
}

// 初始化配置信息
async function initConfig(): Promise<void> {
  try {
    const res = await getConfigByNameAsync('mobile_CardMeterCenter')

    // 使用响应式方式更新配置
    Object.assign(config, res)

    // 初始化表单数据
    formData.print = config.printType
    formData.payment = config.payment
    formData.balance = userInfo.value.f_user_balance.toFixed(4)
  }
  catch (error) {
    console.error('初始化配置信息失败', error)
  }
}

async function getPrice(): Promise<void> {
  try {
    const result = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      gas: 0,
    })
    console.log('获取气价信息：', result)
    gasPrice.value = result.gasPrice
  }
  catch (error: any) {
    console.error('获取气价失败', error)
    showFailToast('获取气价失败')
  }
}

// 文件上传相关
function onFileAdded(): void {
}

function onFileRemoved(): void {
}

// 初始化
onMounted(async () => {
  try {
    await initConfig()
    await getPrice()
    await getLimitGas()
  }
  catch (error) {
    console.error('卡表收费初始化失败', error)
  }
  finally {
    loading.value = false
  }
})
</script>

<template>
  <div v-if="loading" class="loading-overlay">
    <van-loading size="24px">
      加载中...
    </van-loading>
  </div>

  <div v-else class="card-payment-form">
    <InfoCard
      type="info"
      main-text="请填写购气信息，系统将自动为当前用户进行充值操作。"
      :sub-text="`当前气价：${gasPrice} 元/m³${calculateDetail ? `(计算公式：${calculateDetail})` : ''}`"
    />
    <form @submit.prevent="onSubmit">
      <div class="card-payment-form__content">
        <CardContainer>
          <CardHeader title="购气数量" />
          <!-- 购气信息 -->
          <CardGasCalculator
            :gas="formData.gas"
            :amount="formData.money"
            :max-amount="maxMoney"
            :max-gas-amount="maxGas"
            :limit-gas="limitGas"
            :all-limit="allLimit"
            :limit-reason="limitReason"
            :limit-money="limitMoney"
            @calculate-by-gas="calculateByGas"
            @calculate-by-amount="calculateByAmount"
          />
        </CardContainer>
        <div class="card-payment-form__row">
          <!-- 收款信息 -->
          <CardContainer>
            <CardHeader title="收款信息" />
            <CardPaymentInfo
              :amount="formData.money"
              :collection="formData.collection"
              :previous-balance="parseFloat(formData.balance)"
              :cur-balance="formData.curBalance"
              @received-amount-change="receivedAmountChange"
            />
          </CardContainer>

          <!-- 打印及备注 -->
          <ChargePrintSelectorAndRemarks
            v-model="formData.print"
            v-model:remarks="formData.comments"
            class="other-charge-form__section-margin"
          />
        </div>

        <!-- 支付方式 -->
        <PaymentMethodSelectorCard
          v-model="formData.payment"
          title="支付方式"
          :allowed-methods="config.allowedMethods"
          class="card-payment-form__section-margin"
        />

        <!-- 宫格上传组件（多种文件类型时使用） -->
        <GridFileUploader
          v-if="useGridUploader"
          ref="gridFileUploaderRef"
          v-model:file-list="fileList"
          :file-types="config.fileTypes"
          title="上传附件"
          class="card-payment-form__section-margin"
        />
        <!-- 普通上传组件（单一文件类型或无配置时使用） -->
        <CardContainer
          v-else
          class="card-payment-form__section-margin"
        >
          <FileUploader
            v-model:file-list="fileList"
            title="上传附件"
            :multiple="true"
            use-type="卡表收费"
            :max-size="10 * 1024 * 1024"
            :allowed-types="['image/png', 'image/jpeg', 'application/pdf']"
            @file-added="onFileAdded"
            @file-removed="onFileRemoved"
          />
        </CardContainer>
      </div>
      <!-- 实收金额和购气信息区域 -->
      <div class="card-payment-form__summary">
        <div class="card-payment-form__summary-content">
          <p class="card-payment-form__summary-main">
            实收金额：<span class="card-payment-form__summary-amount">¥ {{ formData.collection }}</span>
          </p>
          <p class="card-payment-form__summary-sub">
            购气：¥{{ formData.money }} / {{ formData.gas }}m³
          </p>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="card-payment-form__actions">
        <van-button
          type="default"
          size="normal"
          :loading="clickConfirm"
          @click="handleCancel"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          native-type="submit"
          size="normal"
          :loading="clickConfirm"
          :disabled="!paymentValid"
        >
          确认收费
        </van-button>
      </div>
    </form>
    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" />
    <!-- 二维码支付弹窗 -->
    <CodePayment
      v-if="showQrCodePayment"
      v-model:show="showQrCodePayment"
      :payment="formData.payment"
      :collection="formData.collection"
      type="机表收费"
      @payment-success="handleQrCodePaymentSuccess"
      @payment-cancel="handleQrCodePaymentCancel"
    />
  </div>
</template>

<style lang="less" scoped>
.card-payment-form {
  &__content {
    margin-bottom: 16px;
  }

  &__row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 16px;
    margin-bottom: 16px;

    @media screen and (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  &__hint {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
  }

  &__price {
    font-size: 14px;
    color: #666;

    span {
      font-weight: 600;
      color: var(--van-primary-color);
    }
  }

  &__section-margin {
    margin-bottom: 16px;
  }

  &__summary {
    background: linear-gradient(to right, #e6f7ff, #f0f7ff);
    border: 1px solid #d6e8ff;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
  }

  &__summary-content {
    width: 100%;
  }

  &__summary-main {
    font-size: 16px;
    font-weight: 500;
    color: #4b5563;
    margin: 0 0 4px 0;
  }

  &__summary-amount {
    font-size: 20px;
    font-weight: 600;
    color: #2563eb;
  }

  &__summary-sub {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
  }
}
.loading-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style>
