<script setup lang="ts">
import type { MeterReplacementFormData } from './types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { Field, Picker, Popup, showConfirmDialog, showDialog, showFailToast, showToast } from 'vant'
import { computed, inject, ref } from 'vue'
import { FormField } from '@/components/common'
import useBusinessStore from '@/stores/modules/business'

const props = defineProps<{
  tenantAlias: string
}>()

const emit = defineEmits(['meterReadingComplete', 'uploadFile'])

// 通过inject接收父组件提供的formData
const currUser = useUserStore().getLogin().f
const formData = inject('formData') as MeterReplacementFormData
const userInfo = useBusinessStore().currentUser

// 开户方式选项
const accountOptions = [
  { text: '指令开户', value: '指令开户' },
  { text: '发卡开户', value: '发卡开户' },
]

// 是否显示开户方式选择器
const showAccountTypePicker = ref(false)

// 计算属性：是否显示开户方式
const showAccountType = computed(() => {
  return userInfo.f_meter_type === '物联网表' && userInfo.f_hascard === '是'
})

// 计算属性：表示阶梯用量字段是否应该显示
const showStairUseField = computed(() => {
  if (formData.gasbrand?.length > 0) {
    return userInfo.f_meter_type.includes('卡表') && formData.gasbrand[0].f_meter_type === '物联网表' && formData.f_ladder_sync
  }
  return false
})

// 开户方式变化
function accountTypeChange({ selectedOptions }) {
  formData.f_open_type = selectedOptions[0].value
  showAccountTypePicker.value = false
}

// 旧表底数变化
function meterBaseChange() {
  if (!(formData.f_using_base_old || formData.f_using_base_old === 0)) {
    return
  }
  // 调用logic使用后台校验逻辑
  // 如果输入的旧表底数 > 用户表底数
  if (['机表', '物联网表'].includes(userInfo.f_meter_type) && formData.f_using_base_old > userInfo.f_meter_base) {
    const msg = (userInfo.f_meter_type === '机表' ? '该用户表底数大于上次抄表底数，请确定是否继续生成欠费记录' : '该用户表底数大于上次抄表底数，请确定是否进行结算')
    showConfirmDialog({
      title: '标题',
      message: msg,
    })
      .then(() => {
        runLogic('third_change_meter_hand', {
          f_userfiles_id: userInfo.f_userfiles_id,
          f_meter_base: formData.f_using_base_old,
          f_operator: currUser.resources.name,
          f_operatorid: currUser.resources.id,
          f_orgid: currUser.resources.orgid,
          f_orgname: currUser.resources.orgs,
          f_depid: currUser.resources.depids,
          f_depname: currUser.resources.deps,
        }).then(() => {
          showToast('抄表结算成功，重新加载用户信息')
          emit('meterReadingComplete', { status: true })
        })
      })
      .catch(() => {
        // 咸阳取消之后还能进行操作 正常计算补气
        if (props.tenantAlias === 'Xianyang') {
          meterBaseCheck()
        }
      })
  }
  else {
    meterBaseCheck()
  }
}

function meterBaseCheck() {
  runLogic<{
    code: string
    data: {
      f_remanent_gas: number
      f_remanent_price: number
      f_remanent_money: number
    }
    msg: string
  }>('third_meter_base_check', {
    f_meter_base: formData.f_using_base_old,
    f_userfiles_id: userInfo.f_userfiles_id,
  }).then(async (res) => {
    if (res.code === '0000') {
      // 赋值 补气金额/单价/气量
      Object.assign(formData, res.data)
      showToast('表底数验证成功')
      // await showDialog({
      //   message: '表底数验证成功，请拍下表底照片之后再进行下一步操作',
      // })
      // emit('uploadFile', '旧表底数')
    }
    else {
      showDialog({
        message: res.msg.replace(/，/g, '\n'),
      })
      formData.f_using_base_old = undefined
      formData.f_remanent_gas = undefined
      formData.f_remanent_price = undefined
      formData.f_remanent_money = undefined
    }
  })
}

// 表单验证
function validate() {
  // 验证各必填字段
  if (!formData.f_using_base_old) {
    showFailToast('请输入旧表最新底数')
    return false
  }

  // 验证开户方式
  if (showAccountType.value && !formData.f_open_type) {
    showFailToast('请选择开户方式')
    return false
  }

  return true
}

// 暴露方法给父组件
defineExpose({
  validate,
})
</script>

<template>
  <CardContainer>
    <CardHeader title="清零基本信息" />

    <div class="new-meter-form">
      <div class="form-group">
        <div class="form-item">
          <FormField label="旧表底数">
            <Field
              v-model="formData.f_using_base_old"
              type="number"
              number="9.2"
              placeholder="请输入旧表底数"
              input-align="right"
              :rules="[{ required: true, message: '请输入旧表底数' }]"
              @blur="meterBaseChange"
            />
          </FormField>
        </div>

        <!-- 开户方式选择器 -->
        <div v-if="showAccountType" class="form-item">
          <FormField label="开户方式">
            <Field
              v-model="formData.f_open_type"
              is-link
              readonly
              placeholder="请选择开户方式"
              @click="showAccountTypePicker = true"
            />
            <Popup
              v-model:show="showAccountTypePicker"
              position="bottom"
              teleport="body"
            >
              <Picker
                title="选择开户方式"
                :columns="accountOptions"
                @confirm="accountTypeChange"
                @cancel="showAccountTypePicker = false"
              />
            </Popup>
          </FormField>
        </div>

        <div v-if="showStairUseField" class="form-item">
          <FormField label="阶梯用量">
            <Field
              v-model="formData.f_stair_use"
              type="digit"
              placeholder="请输入阶梯用量"
              input-align="right"
            />
          </FormField>
        </div>
      </div>
    </div>
  </CardContainer>
</template>

<style lang="less" scoped>
.new-meter-form {
  padding: 8px;

  .form-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .form-item {
    width: calc(33.33% - 6px);
  }

  /* 移动端样式：一行一个 */
  @media screen and (max-width: 767px) {
    .form-item {
      width: 100%;
    }
  }

  /* 平板样式：一行两个 */
  @media screen and (min-width: 768px) and (max-width: 1024px) {
    .form-group {
      gap: 10px;
    }

    .form-item {
      width: calc(50% - 5px);
      flex: 0 0 calc(50% - 5px);
    }
  }
}

.field-placeholder {
  color: #999;
}

/* 添加van-field样式 */
:deep(.van-field) {
  background-color: #f9fafb;
  border-radius: 6px;
}

:deep(.van-field__control) {
  color: #333;
  font-size: 14px;

  &::placeholder {
    color: #999;
  }
}

:deep(.van-field--disabled .van-field__control) {
  color: #999;
}
</style>
