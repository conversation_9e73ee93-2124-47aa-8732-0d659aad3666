export
// 定义表单数据类型
interface MeterReplacementFormData {
  // 新表信息
  f_using_base_old: number
  f_meter_type: string
  f_meter_brand: string
  f_meter_model: string
  f_meter_base: number
  f_meternumber: string
  brandid: string
  modelid: string
  f_alias: string
  f_type: string
  f_stair_use?: number
  // 补气信息
  f_ladder_sync: boolean
  f_remanent_gas?: number
  f_remanent_price?: number
  f_remanent_money?: number
  // 开户方式
  f_open_type?: string
  // 扩展信息
  f_change_operator: string
  f_approve_operator: string
  f_changemeter_fee: number
  f_metertitles: string
  f_meter_book_num: string
  f_serial_number: string
  f_price_id: number
  receiptType: string
  paymentMethod: string
  gasbrand?: any[]
  gasmodel?: any[]
  // 其他信息
  remarks: string
  files: {
    uid: string
    name: string
    size: number
    type: string
    url?: string
    status: 'success' | 'error' | 'uploading'
  }[]
}
