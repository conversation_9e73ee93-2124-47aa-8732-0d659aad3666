<script setup lang="ts">
import type { PickerOption } from 'vant'
import type { MeterReplacementFormData } from './types'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import { Field, Popup, showToast } from 'vant'
import { inject, onMounted, ref } from 'vue'
import { FormField } from '@/components/common'
import useBusinessStore from '@/stores/modules/business'
import 'vant/es/picker/style'

const props = defineProps({
  showMeterBook: {
    type: Boolean,
    default: true,
  },
  showGasPrice: {
    type: Boolean,
    default: true,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
})
const formData = inject('formData') as MeterReplacementFormData
const userInfo = useBusinessStore().currentUser

const customFieldName = {
  text: 'label',
  value: 'value',
  children: 'children',
}
// 控制各个 Picker 的显示状态
const priceOptions = ref([])
const showGasPriceNamePicker = ref(false)
// 气价名称展示
const displayPriceName = ref('')
// 气价 Cascader 数据存储
const priceCascader = ref('')

// 抄表册列表 options
const meterReadingBookOptions = ref([])
const showMeterReadingBookPicker = ref(false)
// 抄表册展示
const displayMeterReadingBook = ref('')

// 换表费用选择器
const showChangeMeterFeePicker = ref(false)
const changeMeterFeeColumns = ref<PickerOption[]>([
  { text: '免费', value: 0 },
  { text: '398元', value: 398 },
])

function priceNameChange({ selectedOptions }) {
  console.log(selectedOptions)
  displayPriceName.value = selectedOptions.map(option => option.label).join(' / ')
  showGasPriceNamePicker.value = false
  formData.f_price_id = selectedOptions[selectedOptions.length - 1].value
}

function meterReadingBookChange({ selectedOptions }) {
  displayMeterReadingBook.value = selectedOptions.map(option => option.label).join(' / ')
  showMeterReadingBookPicker.value = false
  formData.f_meter_book_num = selectedOptions[selectedOptions.length - 1].value
}

// 选择换表费用确认
function onChangeMeterFeeConfirm(event: any) {
  if (event && event.selectedOptions && event.selectedOptions.length > 0) {
    formData.f_changemeter_fee = event.selectedOptions[0].value
    showChangeMeterFeePicker.value = false
    showToast(`已选择：${event.selectedOptions[0].text}`)
  }
}

onMounted(async () => {
  priceOptions.value = await runLogic('getPriceDicLogic', {
    orgId: userInfo.f_orgid,
  })
  meterReadingBookOptions.value = await runLogic('getMeterBookName', {
    orgId: userInfo.f_orgid,
  })
  console.warn('抄表册列表:', JSON.stringify(meterReadingBookOptions.value))

  // 根据配置初始化换表费用选项
  if (props.config.changeMeterFeeList && Array.isArray(props.config.changeMeterFeeList)) {
    changeMeterFeeColumns.value = props.config.changeMeterFeeList.map(fee => ({
      text: fee === 0 ? '免费' : `${fee}元`,
      value: fee,
    }))
  }
})
</script>

<template>
  <div class="extended-info">
    <div class="form-group">
      <div class="form-item">
        <FormField label="审批人">
          <Field
            v-model="formData.f_approve_operator"
            placeholder="请输入审批人姓名"
            input-align="right"
          />
        </FormField>
      </div>

      <div class="form-item">
        <FormField label="换表金额(元)">
          <Field
            :model-value="Number(formData.f_changemeter_fee) === 0 ? '免费' : formData.f_changemeter_fee ? `${formData.f_changemeter_fee}元` : ''"
            is-link
            readonly
            placeholder="请选择换表金额"
            @click="showChangeMeterFeePicker = true"
          />
        </FormField>
      </div>

      <div v-if="showMeterBook" class="form-item">
        <FormField label="抄表册">
          <Field
            v-model="displayMeterReadingBook"
            is-link
            readonly
            input-align="left"
            style="--van-field-text-align: left;"
            placeholder="请选择抄表册"
            @click="showMeterReadingBookPicker = true"
          />
          <Popup v-model:show="showMeterReadingBookPicker" position="bottom" teleport="body">
            <van-picker
              title="选择抄表册"
              :columns="meterReadingBookOptions"
              :columns-field-names="customFieldName"
              :show-toolbar="true"
              confirm-button-text="确定"
              cancel-button-text="取消"
              @cancel="showMeterReadingBookPicker = false"
              @confirm="meterReadingBookChange"
            />
          </Popup>
        </FormField>
      </div>

      <div class="form-item">
        <FormField label="业务单号">
          <Field
            v-model="formData.f_serial_number"
            placeholder="请输入业务单号"
            input-align="right"
          />
        </FormField>
      </div>
      <div v-if="showGasPrice" class="form-item">
        <FormField label="气价名称">
          <Field
            v-model="displayPriceName"
            is-link
            readonly
            input-align="left"
            style="--van-field-text-align: left;"
            placeholder="请选择气价名称"
            @click="showGasPriceNamePicker = true"
          />
          <Popup v-model:show="showGasPriceNamePicker" position="bottom" teleport="body">
            <van-cascader
              v-model="priceCascader"
              title="选择气价名称"
              :options="priceOptions"
              :field-names="customFieldName"
              @close="showGasPriceNamePicker = false"
              @finish="priceNameChange"
            />
          </Popup>
        </FormField>
      </div>
    </div>

    <!-- 换表费用选择弹窗 -->
    <Popup
      v-model:show="showChangeMeterFeePicker"
      position="bottom"
      round
      teleport="body"
      :overlay="true"
      :close-on-click-overlay="false"
    >
      <van-picker
        title="选择换表费用"
        :columns="changeMeterFeeColumns"
        show-toolbar
        @confirm="onChangeMeterFeeConfirm"
        @cancel="showChangeMeterFeePicker = false"
      />
    </Popup>
  </div>
</template>

<style lang="less" scoped>
.extended-info {
  padding: 12px;

  .form-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  .form-item {
    width: calc(33.33% - 8px);

    /* 移动端样式：一行一个 */
    @media screen and (max-width: 767px) {
      width: 100%;
    }

    /* 平板样式：一行两个 */
    @media screen and (min-width: 768px) and (max-width: 1024px) {
      width: calc(50% - 6px);
      flex: 0 0 calc(50% - 6px);
    }
  }
}

:deep(.van-field) {
  padding: 10px 12px;
  background-color: #f9fafb;
  border-radius: 4px;
}

:deep(.van-field__control) {
  color: #333;
  font-size: 14px;

  &::placeholder {
    color: #999;
  }
}

:deep(.van-field--disabled .van-field__control) {
  color: #999;
}

.field-placeholder {
  :deep(.van-field__control) {
    color: #999;
  }
}
</style>
