<script setup lang="ts">
import type { BusinessUser } from '../OnlineRevenue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'

defineProps<{
  info: BusinessUser
  title?: string // 添加标题属性
}>()
</script>

<template>
  <CardContainer title="旧表信息">
    <div class="old-meter-info">
      <div class="old-meter-info__grid columns-3">
        <div class="old-meter-info__item">
          <dt class="old-meter-info__label">
            表具类型
          </dt>
          <dd class="old-meter-info__value">
            {{ info.f_meter_type }}
          </dd>
        </div>
        <div class="old-meter-info__item">
          <dt class="old-meter-info__label">
            表具品牌
          </dt>
          <dd class="old-meter-info__value">
            {{ info.f_meter_brand }}
          </dd>
        </div>
        <div class="old-meter-info__item">
          <dt class="old-meter-info__label">
            计费类型
          </dt>
          <dd class="old-meter-info__value">
            {{ info.f_collection_type || '按金额' }}
          </dd>
        </div>
        <div class="old-meter-info__item">
          <dt class="old-meter-info__label">
            当前表底数
          </dt>
          <dd class="old-meter-info__value">
            {{ info.f_meter_base }} m³
          </dd>
        </div>
        <div class="old-meter-info__item">
          <dt class="old-meter-info__label">
            累计购气量
          </dt>
          <dd class="old-meter-info__value">
            {{ info.f_total_gas.toFixed(2) }} m³
          </dd>
        </div>
        <div class="old-meter-info__item">
          <dt class="old-meter-info__label">
            累计购气金额
          </dt>
          <dd class="old-meter-info__value">
            ¥ {{ info.f_total_fee.toFixed(2) }}
          </dd>
        </div>
        <div class="old-meter-info__item">
          <dt class="old-meter-info__label">
            旧表号
          </dt>
          <dd class="old-meter-info__value">
            {{ info.f_meternumber }}
          </dd>
        </div>
        <!-- 添加两个空项来保持布局平衡 -->
        <div class="old-meter-info__item empty" />
        <div class="old-meter-info__item empty" />
      </div>
    </div>
  </CardContainer>
</template>

<style lang="less" scoped>
.old-meter-info {
  &__grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;

    /* 响应式布局 */
    @media screen and (max-width: 767px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
    }
  }

  &__item {
    padding: 10px;
    background-color: #f9fafb;
    border-radius: 6px;

    &.empty {
      background-color: transparent;
    }

    dt {
      color: #666;
      font-size: 14px;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    dd {
      color: #333;
      font-size: 14px;
      margin: 0;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* 手机端样式调整 */
    @media screen and (max-width: 767px) {
      padding: 8px;

      dt {
        font-size: 12px;
        margin-bottom: 2px;
      }

      dd {
        font-size: 12px;
        white-space: normal;
        word-break: break-word;
      }
    }
  }
}

:deep(.van-field) {
  background-color: #f9fafb;
  border-radius: 6px;
}

:deep(.van-field__control) {
  color: #333;
  font-size: 14px;

  &::placeholder {
    color: #999;
  }
}

:deep(.van-field--disabled .van-field__control) {
  color: #999;
}

.field-placeholder {
  :deep(.van-field__control) {
    color: #999;
  }
}
</style>
