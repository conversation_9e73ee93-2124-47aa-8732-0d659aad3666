<script setup lang="ts">
import { computed, ref } from 'vue'

export interface MeterReadingFilterOptions {
  status: string
  bookNumber: string
  sortBy: string
  sortOrder: string
}

export interface MeterReadingBookOption {
  value: string
  label: string
}

const props = defineProps<{
  filters: MeterReadingFilterOptions
  isOpen: boolean
  bookOptions: MeterReadingBookOption[] // 从后台获取的表册选项
  loading: boolean // 表册数据加载状态
}>()

const emit = defineEmits(['update:filters', 'update:isOpen'])

// 下拉选择相关状态
const showMeterBookPicker = ref(false)
const showSortByPicker = ref(false)
const showSortOrderPicker = ref(false)
const meterBookName = ref('')
const statusValue = ref('')
const sortByValue = ref('')
const sortOrderValue = ref('')

// 初始化下拉框值
meterBookName.value = props.filters.bookNumber
statusValue.value = props.filters.status
sortByValue.value = props.filters.sortBy
sortOrderValue.value = props.filters.sortOrder

// 转换表册选项为下拉框选项格式
const dropdownBookOptions = computed(() => {
  // 添加"全部"选项
  return [
    { text: '全部', value: '' },
    ...props.bookOptions.map(option => ({
      text: option.label,
      value: option.value,
    })),
  ]
})

// 排序方式选项
const sortByOptions = [
  { text: '按户号', value: 'f_userinfo_code' },
  { text: '按姓名', value: 'f_user_name' },
]

// 排序顺序选项
const sortOrderOptions = [
  { text: '正序', value: 'asc' },
  { text: '倒序', value: 'desc' },
]

// 表册号显示文本
const meterBookNameText = computed(() => {
  if (!meterBookName.value)
    return '全部'

  const option = props.bookOptions.find(option => option.value === meterBookName.value)
  return option ? option.label : '全部'
})

// 排序方式显示文本
const sortByText = computed(() => {
  const option = sortByOptions.find(option => option.value === sortByValue.value)
  return option ? option.text : '按户号'
})

// 排序顺序显示文本
const sortOrderText = computed(() => {
  const option = sortOrderOptions.find(option => option.value === sortOrderValue.value)
  return option ? option.text : '倒序'
})

// 选择表册号
function selectBookNumber(value: any) {
  if (value && value.selectedValues && value.selectedValues.length > 0) {
    meterBookName.value = value.selectedValues[0]
  }
  else if (value && typeof value.value === 'string') {
    // 直接处理列选择的情况
    meterBookName.value = value.value
  }
  updateFilter('bookNumber', meterBookName.value)
}

// 选择排序方式
function selectSortBy(value: any) {
  if (value && value.selectedValues && value.selectedValues.length > 0) {
    sortByValue.value = value.selectedValues[0]
  }
  else if (value && typeof value.value === 'string') {
    // 直接处理列选择的情况
    sortByValue.value = value.value
  }
  updateFilter('sortBy', sortByValue.value)
}

// 选择排序顺序
function selectSortOrder(value: any) {
  if (value && value.selectedValues && value.selectedValues.length > 0) {
    sortOrderValue.value = value.selectedValues[0]
  }
  else if (value && typeof value.value === 'string') {
    // 直接处理列选择的情况
    sortOrderValue.value = value.value
  }
  updateFilter('sortOrder', sortOrderValue.value)
}

// 更新过滤条件
function updateFilter(type: string, value: string) {
  const updatedFilters = { ...props.filters }
  updatedFilters[type as keyof MeterReadingFilterOptions] = value
  emit('update:filters', updatedFilters)
}
</script>

<template>
  <div
    v-show="isOpen"
    class="meter-reading-filter"
    @click.self="emit('update:isOpen', false)"
  >
    <div class="filter-container">
      <!--      &lt;!&ndash; 抄表状态筛选 - 下拉列表 &ndash;&gt; -->
      <!--      <div class="filter-section"> -->
      <!--        <div class="filter-title"> -->
      <!--          抄表状态 -->
      <!--        </div> -->
      <!--        <div class="dropdown-wrapper"> -->
      <!--          <van-field -->
      <!--            v-model="statusText" -->
      <!--            readonly -->
      <!--            is-link -->
      <!--            name="status" -->
      <!--            class="search-field__input" -->
      <!--            placeholder="选择抄表状态" -->
      <!--            @click="showStatusPicker = true" -->
      <!--          /> -->
      <!--          <van-popup v-model:show="showStatusPicker" position="bottom"> -->
      <!--            <van-picker -->
      <!--              :columns="statusOptions" -->
      <!--              show-toolbar -->
      <!--              title="选择抄表状态" -->
      <!--              :option-height="50" -->
      <!--              @change="selectStatus" -->
      <!--              @confirm="showStatusPicker = false" -->
      <!--              @cancel="showStatusPicker = false" -->
      <!--            /> -->
      <!--          </van-popup> -->
      <!--        </div> -->
      <!--      </div> -->

      <!-- 抄表册号筛选 - 下拉列表 -->
      <div class="filter-section">
        <div class="filter-title">
          抄表册号
        </div>
        <div class="dropdown-wrapper">
          <van-field
            v-model="meterBookNameText"
            readonly
            is-link
            name="searchType"
            class="search-field__input"
            placeholder="选择抄表册"
            :disabled="loading || props.bookOptions.length === 0"
            @click="showMeterBookPicker = true"
          />
          <van-popup v-model:show="showMeterBookPicker" position="bottom">
            <van-picker
              :columns="dropdownBookOptions"
              show-toolbar
              title="选择抄表册"
              :option-height="50"
              @change="selectBookNumber"
              @confirm="showMeterBookPicker = false"
              @cancel="showMeterBookPicker = false"
            />
          </van-popup>
          <span v-if="loading" class="loading-text">加载中...</span>
          <span v-else-if="props.bookOptions.length === 0" class="no-data-text">暂无册号数据</span>
        </div>
      </div>

      <!-- 排序方式筛选 - 下拉列表 -->
      <div class="filter-section">
        <div class="filter-title">
          排序设置
        </div>
        <div class="sort-row">
          <!-- 排序方式 -->
          <div class="dropdown-wrapper sort-item">
            <van-field
              v-model="sortByText"
              readonly
              is-link
              name="sortBy"
              class="search-field__input"
              placeholder="选择排序方式"
              @click="showSortByPicker = true"
            />
            <van-popup v-model:show="showSortByPicker" position="bottom">
              <van-picker
                :columns="sortByOptions"
                show-toolbar
                title="选择排序方式"
                :option-height="50"
                @change="selectSortBy"
                @confirm="showSortByPicker = false"
                @cancel="showSortByPicker = false"
              />
            </van-popup>
          </div>

          <!-- 排序顺序 -->
          <div class="dropdown-wrapper sort-item">
            <van-field
              v-model="sortOrderText"
              readonly
              is-link
              name="sortOrder"
              class="search-field__input"
              placeholder="选择排序顺序"
              @click="showSortOrderPicker = true"
            />
            <van-popup v-model:show="showSortOrderPicker" position="bottom">
              <van-picker
                :columns="sortOrderOptions"
                show-toolbar
                title="选择排序顺序"
                :option-height="50"
                @change="selectSortOrder"
                @confirm="showSortOrderPicker = false"
                @cancel="showSortOrderPicker = false"
              />
            </van-popup>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.meter-reading-filter {
  position: fixed;
  top: 104px;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 50;

  .filter-container {
    max-width: 750px;
    margin: 0 auto;
  }

  .filter-section {
    padding: 12px 16px;
    border-bottom: 1px solid #f2f2f2;

    &:last-child {
      border-bottom: none;
    }
  }

  .filter-title {
    font-size: 12px;
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
  }

  .filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .filter-option {
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    padding: 6px 14px;
    border-radius: 6px;
    background-color: #f3f4f6;
    min-width: 60px;
    text-align: center;

    &:hover {
      background-color: #ebf5fe;
      color: #1989fa;
    }

    &.active {
      color: #fff;
      background-color: #1989fa;
    }
  }

  .loading-text,
  .no-data-text {
    color: #969799;
    font-size: 14px;
    padding: 6px 0;
    display: block;
    margin-top: 5px;
  }

  .dropdown-wrapper {
    :deep(.van-field) {
      background-color: #f7f8fa;
      border-radius: 4px;
      height: 36px;
      padding: 0 12px;

      .van-field__label {
        display: none;
      }

      .van-field__value {
        .van-field__body {
          height: 36px;

          input {
            height: 36px;
            line-height: 36px;
            font-size: 14px;
          }
        }
      }

      &.van-field--disabled {
        background-color: #f5f5f5;
        color: #c8c9cc;
      }
    }
  }

  .sort-row {
    display: flex;
    gap: 10px;

    .sort-item {
      flex: 1;
    }
  }
}
</style>
