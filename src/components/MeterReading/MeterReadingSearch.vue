<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits(['update:modelValue', 'filter', 'search'])

const searchValue = ref(props.modelValue)

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== searchValue.value) {
    searchValue.value = newVal
  }
})

// 监听输入变化
watch(searchValue, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理搜索事件
function handleSearch() {
  emit('search')
}
</script>

<template>
  <div class="meter-reading-search">
    <div class="search-container">
      <van-search
        v-model="searchValue"
        shape="round"
        placeholder="搜索用户..."
        background="#ffffff"
        show-action
        @search="handleSearch"
      >
        <template #action>
          <div @click="handleSearch">
            搜索
          </div>
        </template>
      </van-search>
      <van-button
        class="filter-button"
        icon="filter-o"
        size="small"
        @click="emit('filter')"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
.meter-reading-search {
  position: fixed;
  top: 46px;
  left: 0;
  right: 0;
  z-index: 40;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .search-container {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    max-width: 750px;
    margin: 0 auto;

    :deep(.van-search) {
      flex: 1;
      padding: 0;
      margin-right: 8px;

      .van-search__content {
        background-color: #f7f8fa;
      }

      .van-search__action {
        color: var(--van-primary-color);
        font-size: 14px;
      }
    }

    .filter-button {
      flex-shrink: 0;
      background-color: #f7f8fa;
      border: none;
      border-radius: 4px;
      height: 36px;
      padding: 0 10px;
    }
  }
}
</style>
