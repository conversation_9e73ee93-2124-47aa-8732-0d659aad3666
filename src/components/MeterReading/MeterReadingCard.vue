<script setup lang="ts">
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import { ref, watch } from 'vue'
import { FileUploader } from '../common'

export interface MeterReadingUser {
  f_userinfo_id: number
  f_userfiles_id: number
  f_user_id: number
  f_userinfo_code: number
  f_user_name: string
  f_address: string
  f_meternumber: string
  f_last_input_date: string
  f_last_tablebase: string
  monthlyAverage: number
  f_book_name: string
  f_meter_book_sort: string
  f_meter_book_num: string
  f_arrears_fees: number
  f_tablebase: string
  readingStatus: string
  fileList: []
  remark: string
  showMoreOptions: boolean
  readingError: string
  f_meter_type: string
  f_meter_brand: string
  f_meter_style: string
  f_user_type: string
  f_gasproperties: string
  f_residential_area: string
  f_price_id: string
  f_user_level: string
  f_stairprice_id: string
  f_balance: number
  f_total_usegas_amount: number
}

const props = defineProps<{
  user: MeterReadingUser
}>()

const emit = defineEmits(['update:user', 'save', 'submit'])

const localUser = ref<MeterReadingUser>({ ...props.user })
const showPicker = ref(false)
const states = ref([
  { text: '正常抄表', value: '正常抄表' },
])

// 监听外部用户数据变更
watch(() => props.user, (newVal) => {
  localUser.value = { ...newVal }
}, { deep: true })

// 更新本地用户数据并发出事件
function updateUser() {
  emit('update:user', localUser.value)
}

// 验证读数
function validateReading() {
  if (!localUser.value.f_tablebase) {
    localUser.value.readingError = ''
    updateUser()
    return
  }

  const current = Number.parseFloat(localUser.value.f_tablebase)
  const last = Number.parseFloat(localUser.value.f_last_tablebase)

  if (current < last) {
    localUser.value.readingError = '当前读数不能小于上次读数'
  }
  else {
    localUser.value.readingError = ''
  }

  updateUser()
}

// 文件上传相关
function onFileAdded(): void {
}

function onFileRemoved(): void {
}

// 提交抄表记录
function submitReading() {
  if (!localUser.value.f_tablebase) {
    return
  }

  if (localUser.value.readingError) {
    return
  }
  console.log('localUser.value', localUser.value)
  emit('submit', localUser.value)
}

// 提交抄表记录
function handReadStatusSelected(value) {
  // 更新本地用户数据中的抄表状态
  localUser.value.readingStatus = value.selectedValues[0]

  // 关闭选择器弹窗
  showPicker.value = false

  // 更新用户数据并发出事件
  updateUser()

  console.log('已选择抄表状态:', value.selectedValues[0])
}
</script>

<template>
  <CardContainer>
    <div class="meter-reading-card">
      <!-- 用户基本信息 -->
      <div class="user-info">
        <div>
          <h3 class="user-name">
            {{ user.f_user_name }}
            <span class="user-id">#{{ user.f_userinfo_code }}</span>
          </h3>
          <p class="user-address">
            {{ user.f_address }}
          </p>
        </div>
        <div class="user-status-tags">
          <span
            class="status-tag"
            :class="user.f_arrears_fees > 0 ? 'overdue-tag' : 'paid-tag'"
          >
            {{ user.f_arrears_fees > 0 ? `欠费 ${user.f_arrears_fees} 元` : '无欠费' }}
          </span>
        </div>
      </div>

      <!-- 表计详情 -->
      <div class="meter-details">
        <div class="detail-item">
          <span class="detail-label">表号：</span>
          <span class="detail-value">{{ user.f_meternumber }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">抄表册：</span>
          <span class="detail-value">{{ user.f_book_name }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">册内位置：</span>
          <span class="detail-value">{{ user.f_meter_book_sort }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">上次抄表：</span>
          <span class="detail-value">{{ user.f_last_input_date ? user.f_last_input_date.substring(0, 10) : '' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">上次底数：</span>
          <span class="detail-value">{{ user.f_last_tablebase }} m³</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">气表类型：</span>
          <span class="detail-value">{{ user.f_meter_type }}</span>
        </div>
      </div>

      <!-- 抄表操作区域 -->
      <div class="reading-operation">
        <div class="reading-input-section">
          <!-- 当前读数和抄表状态 -->
          <div class="reading-inputs">
            <div class="input-group">
              <div class="input-label">
                当前读数
              </div>
              <div class="input-container">
                <van-field
                  v-model="localUser.f_tablebase"
                  type="digit"
                  placeholder="请输入当前读数"
                  :error="!!localUser.readingError"
                  right-icon="chart-trending-o"
                  @input="validateReading"
                />
                <p v-if="localUser.readingError" class="error-message">
                  {{ localUser.readingError }}
                </p>
              </div>
            </div>

            <div class="input-group">
              <div class="input-label">
                抄表状态
              </div>
              <div class="input-container">
                <van-field
                  v-model="localUser.readingStatus"

                  readonly is-link
                  placeholder="选择抄表状态"
                  @click="() => { showPicker = true }"
                />
                <van-popup
                  v-model:show="showPicker"
                  round
                  position="bottom"
                  :style="{ height: '30%' }"
                  teleport="body"
                >
                  <van-picker
                    :columns="states"
                    show-toolbar
                    title="选择抄表状态"
                    @cancel="showPicker = false"
                    @confirm="handReadStatusSelected"
                  />
                </van-popup>
              </div>
            </div>
          </div>

          <!-- 按钮区域 -->
          <div class="action-buttons">
            <div>
              <van-button
                size="small"
                plain
                :icon="localUser.showMoreOptions ? 'arrow-up' : 'arrow-down'"
                class="no-border"
                @click="localUser.showMoreOptions = !localUser.showMoreOptions"
              >
                {{ localUser.showMoreOptions ? '收起选项' : '更多选项' }}
              </van-button>
            </div>
            <div class="operation-buttons">
              <van-button
                size="small"
                type="primary"
                @click="submitReading"
              >
                提交抄表
              </van-button>
            </div>
          </div>
        </div>

        <!-- 更多选项（使用 v-show 替代 van-collapse-transition） -->
        <div v-show="localUser.showMoreOptions" class="more-options">
          <!-- 拍照上传 -->
          <div class="upload-section">
            <FileUploader
              v-model:file-list="localUser.fileList"
              :multiple="true"
              title="拍照上传"
              @file-added="onFileAdded"
              @file-removed="onFileRemoved"
            />
          </div>

          <!-- 备注 -->
          <div class="remark-section">
            <div class="input-label">
              备注
            </div>
            <van-field
              v-model="localUser.remark"
              type="textarea"
              rows="2"
              placeholder="请输入备注信息"
              @input="updateUser"
            />
          </div>
        </div>
      </div>
    </div>
  </CardContainer>
</template>

<style lang="less" scoped>
.meter-reading-card {
  padding: 6px;

  .user-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .user-name {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
      padding: 0;
      color: #323233;

      .user-id {
        font-size: 12px;
        color: #969799;
        margin-left: 6px;
        font-weight: normal;
      }
    }

    .user-address {
      font-size: 12px;
      color: #969799;
      margin: 4px 0 0;
      padding: 0;
    }

    .user-status-tags {
      display: flex;
      flex-direction: column;
      gap: 6px;
      align-items: flex-end;

      .status-tag {
        font-size: 10px;
        padding: 2px 8px;
        border-radius: 10px;
        font-weight: 500;
      }

      .overdue-tag {
        background-color: #ffece8;
        color: #ee0a24;
      }

      .paid-tag {
        background-color: #eafaf1;
        color: #07c160;
      }

      .reading-tag {
        background-color: #e6f7ff;
        color: #1989fa;
      }
    }
  }

  .meter-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 6px 12px;
    margin-bottom: 12px;

    .detail-item {
      font-size: 13px;

      .detail-label {
        color: #969799;
      }

      .detail-value {
        font-weight: 550;
      }
    }
  }

  .reading-operation {
    padding-top: 10px;
    border-top: 1px solid #f2f2f2;

    .reading-input-section {
      .reading-inputs {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin-bottom: 12px;

        .input-group {
          display: flex;
          flex-direction: column;

          .input-label {
            font-size: 12px;
            color: #646566;
            margin-bottom: 4px;
            font-weight: 500;
          }

          .input-container {
            position: relative;

            .error-message {
              position: absolute;
              bottom: -20px;
              left: 0;
              font-size: 12px;
              color: #ee0a24;
              margin: 0;
              z-index: 10;
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        margin-bottom: 4px;
        z-index: 5;

        .no-border {
          border: none;
        }
        .operation-buttons {
          display: flex;
          gap: 8px;
        }
      }
    }

    .more-options {
      margin-top: 12px;

      .upload-section {
        margin-bottom: 12px;

        .input-label {
          font-size: 12px;
          color: #646566;
          margin-bottom: 4px;
          font-weight: 500;
        }

        .upload-container {
          padding: 12px;
          border: 1px dashed #dcdee0;
          border-radius: 4px;
          background-color: #f7f8fa;
          text-align: center;
        }

        .image-preview {
          position: relative;
          display: inline-block;

          .preview-image {
            width: 100%;
            max-width: 240px;
            border-radius: 4px;
          }

          .remove-image {
            position: absolute;
            top: 8px;
            right: 8px;
          }
        }
      }

      .remark-section {
        .input-label {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }
      }
    }
  }
}
</style>
