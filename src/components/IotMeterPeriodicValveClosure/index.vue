<script setup lang="ts">
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from 'af-mobile-client-vue3/src/stores/modules/user'
import { showDialog, showToast } from 'vant'
import { onMounted, ref } from 'vue'
import useBusinessStore from '@/stores/modules/business'

const emit = defineEmits(['closeOperation', 'complete'])
// 初始化状态
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
function paramSet() {
  const data = {
    userId: `${userInfo.value.f_userfiles_id}`,
    title: '设置表具参数',
    content: {
      setType: '设置运行参数',
      closingValveCycle: 180,
      lostCloseValveDays: 30,
    },
    alias: `${userInfo.value.f_alias}`,
    inputtor: `${currUser.resources.name}`,
    moduleName: 'WeiSiDaSyncNB',
    f_userfiles_id: userInfo.value.f_userfiles_id,
    f_meternumber: userInfo.value.f_meternumber,
    f_userinfo_id: userInfo.value.f_userinfo_id,
    f_user_id: userInfo.value.f_user_id,
    f_orgid: currUser.resources.orgid,
    f_orgname: currUser.resources.orgs,
    f_depid: currUser.resources.depids,
    f_depname: currUser.resources.deps,
    f_operator: currUser.resources.name,
    f_operatorid: currUser.resources.id,
  }
  runLogic('out_conacnt', data, 'af-revenue').then((res) => {
    console.log(res)
    emit('complete', { status: true })
    showToast(`周期开阀成功`)
  }).catch((error) => {
    console.error(error)
    showToast(`周期开阀失败${error.message || error.msg || '未知错误'}`)
  })
}
onMounted(() => {
  showDialog({
    title: '提示',
    message: `确认要对用户${userInfo.value.f_user_name}进行参数设置（周期关阀天数：180，失联关阀天数：30）？`,
    showCancelButton: true,
  }).then(() => {
    paramSet()
  }).catch((error) => {
    console.error(error)
    emit('closeOperation')
  })
})
</script>

<template>
  <div />
</template>

<style scoped lang="less">

</style>
