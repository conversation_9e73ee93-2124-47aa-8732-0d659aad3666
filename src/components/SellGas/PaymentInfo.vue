<script setup lang="ts">
import { ref, watch } from 'vue'
import { FormField } from '../common'

const props = defineProps({
  amount: {
    type: String,
    required: true,
  },
  collection: {
    type: String,
    required: true,
  },
  previousBalance: {
    type: String,
    default: '0',
  },
  curBalance: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: '收款信息',
  },
  icon: {
    type: String,
    default: 'fa-money-bill-wave',
  },
})

const emit = defineEmits(['receivedAmountChange', 'balanceChange'])

// 本地状态
const receivedAmount = ref(props.collection)
const curBalance = ref(props.curBalance)
const showWarning = ref(false)
const isValid = ref(true)

// 计算属性

// 验证收款金额
function validateReceivedAmount() {
  // 使用Number转换确保类型相同
  isValid.value = Number(receivedAmount.value) + Number(props.previousBalance) >= Number(props.amount)
  showWarning.value = !isValid.value
  // 通知父组件状态变化
  emit('receivedAmountChange', receivedAmount.value)
}

// 监听amount变化，自动更新收款金额
watch(() => props.collection, (newVal) => {
  // if (Number(receivedAmount.value) < Number(newVal) || receivedAmount.value === '') {
  receivedAmount.value = newVal
  // }
  validateReceivedAmount()
}, { immediate: true })

// 监听amount变化，自动更新收款金额
watch(() => props.curBalance, (newVal) => {
  curBalance.value = newVal
}, { immediate: true })
</script>

<template>
  <div class="payment-info">
    <FormField label="收款金额">
      <div class="payment-info__input">
        <van-field
          v-model="receivedAmount"
          type="number"
          input-align="right"
          placeholder="0.00"
          @input="validateReceivedAmount"
        >
          <template #left-icon>
            <span class="field-unit">¥</span>
          </template>
          <template #right-icon>
            <span class="field-unit">元</span>
          </template>
        </van-field>
      </div>

      <div v-if="showWarning" class="payment-info__warning">
        <van-icon name="warning-o" color="#ee0a24" />
        <span class="payment-info__warning-text">收款金额不能小于购气金额</span>
      </div>
    </FormField>

    <FormField label="本期结余">
      <div class="payment-info__input payment-info__input--readonly">
        <van-field
          v-model="curBalance"
          input-align="right"
          :disabled="true"
        >
          <template #left-icon>
            <span class="field-unit">¥</span>
          </template>
          <template #right-icon>
            <span class="field-unit">元</span>
          </template>
        </van-field>
      </div>
    </FormField>
  </div>
</template>

<style lang="less" scoped>
.payment-info {
  &__input {
    position: relative;

    &--readonly {
      :deep(.van-field) {
        background-color: #f9fafb;
        border-radius: 6px;
      }
    }

    :deep(.van-field__left-icon) {
      padding-left: 10px;
    }

    :deep(.van-field__right-icon) {
      padding-right: 10px;
    }
  }

  &__warning {
    display: flex;
    align-items: center;
    margin-top: 4px;
    color: #ee0a24;
  }

  &__warning-text {
    font-size: 12px;
    margin-left: 4px;
  }
}

.field-unit {
  color: #666;
  font-size: 14px;
}
</style>
