<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { showDialog, showFailToast, showToast } from 'vant'
import { onMounted, reactive, ref } from 'vue'
import { businessGetLimitGas, commonCal, getOpenFee } from '@/services/api/business'
import { initCard } from '@/services/cardService'
import useBusinessStore from '@/stores/modules/business'
import { ChargePrintSelectorAndRemarks, CodePayment, FileUploader, InfoCard, PaymentMethodSelectorCard, ReceiptModal } from '../common'
import GasExchangeCalculator from './GasExchangeCalculator.vue'
import PaymentInfo from './PaymentInfo.vue'

interface FormData {
  money: string
  gas: string
  collection: string
  balance: string
  payment: string
  print: string
  comments: string
  files: any[]
  dyMoney: number
  totalCost: string
  writeCardMoney: number
  open_fee?: number
  curBalance?: string
  voucher_number?: string
  serial_number?: string
  serial_id?: string
  payments?: any[]
  [key: string]: any
}

interface ReceiptData {
  transactionId: string
  datetime: string
}

// 定义config配置接口
interface ConfigOptions {
  calculatePreByCollection: boolean // 可以根据收款来反向计算预购
  hasPrint: boolean // 是否打票
  floor: boolean // 是否取整收费
  allowedMethods: string[] | null
  printType: string // 收据/电子票/专用发票/国税发票
  payment: string // 默认支付方式
  printLogic: string // 打票逻辑
}

const emit = defineEmits(['closeOperation', 'complete'])

// 定义内部config配置
const config = reactive<ConfigOptions>({
  calculatePreByCollection: false, // 可以根据收款来反向计算预购
  hasPrint: false, // 默认打票
  floor: true, // 是否取整收费
  allowedMethods: null,
  printType: '普通收据', // 收据/电子票/专用发票/国税发票
  payment: '现金缴费',
  printLogic: '',
})

const fileList = ref<FileItem[]>([])

// 表单数据
const formData = reactive<FormData>({
  money: '',
  gas: '',
  collection: null,
  balance: '0',
  payment: '现金缴费',
  print: '',
  comments: '',
  files: [],
  dyMoney: 0,
  totalCost: '',
  writeCardMoney: 0,
  open_fee: 0,
  curBalance: '0',
  voucher_number: '',
  serial_number: '',
  serial_id: '',
  payments: [],
})

// 初始化状态
const loading = ref(true)

// 限购相关状态
const hasLimit = ref(false)
const maxGas = ref(99999999)
const maxMoney = ref(99999999)
const allLimit = ref(null)
const limitReason = ref('')
const limitGas = ref(false)
const limitMoney = ref(false)
const gasPrice = ref('')
// 收款验证状态
const paymentValid = ref(false)
// 当前用户
const currUser = useUserStore().getLogin().f
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
console.log(userInfo)
const receiptData = ref<ReceiptData>({
  transactionId: '',
  datetime: '',
})
// 子组件的响应组件引用
const printRef = ref()
// 其他状态
const clickConfirm = ref(false)
const showQrCodePayment = ref(false)
// 计算公式显示
const calculateDetail = ref('')

// 初始化配置信息
async function initConfig() {
  try {
    // 使用 Promise API 获取配置
    const res = await getConfigByNameAsync('mobile_ShowCardSellGas')

    // 更新配置
    Object.assign(config, res)

    // 初始化表单数据中依赖config的字段
    formData.payment = config.payment
    formData.print = config.printType
    formData.balance = userInfo.value.f_user_balance.toFixed(4)
  }
  catch (error) {
    console.error('初始化配置信息失败', error)
  }
}

function cleanGasAndMoney() {
  formData.gas = ''
  formData.money = ''
  formData.totalCost = ''
  formData.collection = ''
}

// 输入气量，换算金额
async function calculateByGas(gas: number) {
  try {
    if (gas && gas > 0) {
      if (userInfo.value.f_isdecimal === '是') {
        formData.gas = Number(gas).toFixed(userInfo.value.f_gas_decimal || 4)
      }
      else {
        formData.gas = Math.floor(Number(gas)).toString()
      }
      // 验证气量限制
      if (gas > Number(maxGas.value)) {
        showFailToast(`气量超过限制: ${maxGas.value}m³，请重新输入`)
        cleanGasAndMoney()
        paymentValid.value = false
        return
      }

      // 对气量进行验证
      if (userInfo.value.f_topup_ceil > 0 && gas > userInfo.value.f_topup_ceil) {
        showFailToast(`气量不能大于该表的充值上限: ${userInfo.value.f_topup_ceil}m³`)
        formData.gas = ''
        return
      }

      // 调用接口获取计算结果
      const getAmount = await commonCal({
        f_userinfo_id: userInfo.value.f_userinfo_id,
        f_card_id: userInfo.value.f_card_id,
        f_meternumber: userInfo.value.f_meternumber,
        f_userfiles_id: userInfo.value.f_userfiles_id,
        gas,
      })
      console.log('气量计算结果：', getAmount)
      if (getAmount) {
        formData.money = getAmount.money
        // 验证金额限制
        if (Number(formData.money) > Number(maxMoney.value)) {
          showFailToast(`购气金额超过限制: ${maxMoney.value}元，请重新输入`)
          cleanGasAndMoney()
          paymentValid.value = false
          return
        }
        formData.totalCost = Number(userInfo.value.f_user_balance) > Number(getAmount.money) ? '0' : (Number(getAmount.money) - Number(userInfo.value.f_user_balance)).toFixed(userInfo.value.f_fee_decimal || 4)
        formData.collection = formData.totalCost
        // 如果配置了取整收费
        if (config.floor) {
          formData.collection = Math.ceil(Number(formData.collection)).toString()
        }
        // 计算本期结余
        calculateCurrentBalance()
        console.log(formData)
        // 阶梯价格展示
        calculateDetail.value = getAmount.calText
        // 跨阶梯提示
        if (getAmount.crossStepCount > 1) {
          showToast('本次购气已跨阶梯')
        }
      }
      paymentValid.value = true
    }
  }
  catch (error) {
    console.error('气量计算失败', error)
    showFailToast('气量计算失败')
  }
}

// 输入金额，换算气量
async function calculateByAmount(amount: number) {
  try {
    if (amount && amount > 0) {
      if (userInfo.value.f_isdecimal === '是') {
        formData.money = Number(amount).toFixed(userInfo.value.f_gas_decimal || 4)
      }
      else {
        formData.money = Math.floor(Number(amount)).toString()
      }
      // 验证金额限制
      if (Number(amount) > Number(maxMoney.value)) {
        showFailToast(`购气金额超过限制: ${maxMoney.value}元，请重新输入`)
        cleanGasAndMoney()
        paymentValid.value = false
        return
      }

      // 调用接口获取计算结果
      const getGas = await commonCal({
        f_userinfo_id: userInfo.value.f_userinfo_id,
        f_card_id: userInfo.value.f_card_id,
        f_meternumber: userInfo.value.f_meternumber,
        f_userfiles_id: userInfo.value.f_userfiles_id,
        money: amount,
      })

      console.log('金额计算结果：', getGas)

      if (getGas && getGas.gas) {
        if (userInfo.value.f_isdecimal === '是') {
          formData.gas = Number(getGas.gas).toFixed(userInfo.value.f_gas_decimal || 4)
        }
        else {
          formData.gas = Math.floor(Number(getGas.gas)).toString()
        }

        if (Number(formData.gas) > Number(maxGas.value)) {
          showFailToast(`购气气量超过限制: ${maxGas.value}m³，请重新输入`)
          cleanGasAndMoney()
          paymentValid.value = false
          return
        }
        formData.money = getGas.money.toString()
        formData.totalCost = Number(userInfo.value.f_user_balance) > Number(formData.money) ? '0' : (Number(formData.money) - Number(userInfo.value.f_user_balance)).toFixed(userInfo.value.f_fee_decimal || 4)
        formData.collection = formData.totalCost

        // 如果配置了取整收费
        if (config.floor) {
          formData.collection = Math.ceil(Number(formData.collection)).toString()
        }

        // 计算本期结余
        calculateCurrentBalance()

        // 阶梯价格展示
        calculateDetail.value = getGas.calText
        paymentValid.value = true
        // 跨阶梯提示
        if (getGas.crossStepCount > 1) {
          showToast('本次购气已跨阶梯')
        }
      }
    }
    else {
      formData.money = ''
    }
  }
  catch (error) {
    console.error('金额计算失败', error)
    showFailToast('金额计算失败')
  }
}

// 根据收款金额反向计算预购气量和金额
async function calculateByReceivedAmount() {
  try {
    // 通过收款金额 + 余额计算总费用
    const calFee = Number(formData.collection) + Number(userInfo.value.f_user_balance)
    // 通过收款进行划价
    const getGas = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      money: calFee,
    })
    console.log('收款金额反向计算结果：', getGas)

    if (getGas && getGas.gas) {
      // 气量计算（按整数处理）
      formData.gas = getGas.gas

      // 再根据气量进行正向计算以获取准确值
      const redundant = await commonCal({
        f_userinfo_id: userInfo.value.f_userinfo_id,
        f_card_id: userInfo.value.f_card_id,
        f_meternumber: userInfo.value.f_meternumber,
        f_userfiles_id: userInfo.value.f_userfiles_id,
        money: calFee,
      })
      formData.totalCost = redundant.money
      formData.money = redundant.money

      // 计算本期结余
      calculateCurrentBalance()
      // 阶梯价格展示
      calculateDetail.value = redundant.calText
      // 跨阶梯提示
      if (redundant.crossStepCount > 1) {
        showToast('本次购气已跨阶梯')
      }
      paymentValid.value = true
    }
  }
  catch (error) {
    console.error('收款金额反向计算失败', error)
    showFailToast('收款金额反向计算失败，请重试')
  }
}

// 计算本期结余
function calculateCurrentBalance() {
  if (config.calculatePreByCollection) {
    formData.curBalance = (Number(formData.collection) + Number(formData.balance) - Number(formData.money)).toFixed(userInfo.value.f_fee_decimal || 4)
    return
  }

  if (formData.money) {
    if (Number(formData.balance) > Number(formData.money)) {
      formData.curBalance = (Number(formData.balance) - Number(formData.money) + Number(formData.collection)).toFixed(userInfo.value.f_fee_decimal || 4)
    }
    else {
      formData.curBalance = (Number(formData.collection) - Number(formData.totalCost)).toFixed(userInfo.value.f_fee_decimal || 4)
    }
  }
  else {
    formData.curBalance = formData.balance.toString()
  }
}

// 收款金额更新处理
async function receivedAmountChange(value) {
  formData.collection = value
  // 验证金额限制
  if (Number(formData.collection) > (maxMoney.value - 0)) {
    showFailToast(`购气金额超过限制: ${maxMoney.value}元，请重新输入`)
    cleanGasAndMoney()
    paymentValid.value = false
    return
  }
  validateReceivedAmount()

  // 如果配置了通过收款金额反向计算，则执行反向计算
  if (config.calculatePreByCollection) {
    await calculateByReceivedAmount()
  }
  // 计算本期结余
  calculateCurrentBalance()
}

// 验证收款金额
function validateReceivedAmount() {
  // 收款金额必须大于等于应收金额 + 开卡费
  if (Number(formData.totalCost) > 0) {
    paymentValid.value = Number(formData.collection) >= Number(formData.totalCost) + (formData.open_fee - 0)
  }
}

// 获取燃气限购值
async function getLimitGas() {
  try {
    const response = await businessGetLimitGas(
      userInfo.value.f_userinfo_id,
      userInfo.value.f_user_id,
      userInfo.value.f_stairprice_id,
    )
    console.log('获取限购值结果：', response)
    // 处理限购逻辑
    hasLimit.value = response.hasLimit
    if (hasLimit.value) {
      if (response.f_limit_value || response.f_limit_amount) {
        if (response.f_limit_value < 0 || response.f_limit_amount < 0) {
          maxMoney.value = 0
          allLimit.value = response.all_limit ? Number(response.all_limit) : null
          limitReason.value = response?.f_limit_comments
          limitMoney.value = true
          showToast(response.msg || '您已达到限购上限')
        }
        else {
          if (response.f_limit_value) {
            allLimit.value = response.all_limit ? Number(response.all_limit) : null
            limitReason.value = response?.f_limit_comments
            maxGas.value = Number(response.f_limit_value)
            limitGas.value = true
          }
          if (response.f_limit_amount) {
            allLimit.value = response.all_limit ? Number(response.all_limit) : null
            limitReason.value = response?.f_limit_comments
            maxMoney.value = Number(response.f_limit_amount)
            limitMoney.value = true
          }
        }
      }
    }
  }
  catch (error: any) {
    console.error('获取限购值失败', error)
    showFailToast('获取限购值失败')
  }
}

// 获取气价信息
async function getPrice() {
  try {
    const result = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      gas: 0,
    })
    console.log('获取气价信息：', result)
    gasPrice.value = result.gasPrice
  }
  catch (error) {
    console.error('获取气价失败', error)
    showFailToast('获取气价失败')
  }
}

// 获取开卡费
async function initGetOpenFee() {
  const result = await getOpenFee({ f_user_type: userInfo.value.f_user_type })
  formData.open_fee = result.value
}

// 表单提交
async function handleSubmit() {
  try {
    // 验证必填字段
    if (!formData.payment) {
      showToast('请选择付款方式')
      return
    }
    if (!formData.print) {
      showToast('请选择打印格式')
      return
    }
    clickConfirm.value = true
    showDialog({
      title: '确认',
      message: `对客户${userInfo.value.f_user_name}进行发卡售气操作，请确保已插入正确的燃气卡`,
      showCancelButton: true,
    }).then(async () => {
      // 微信/支付宝支付走单独的流程，通过事件回调处理支付结果
      if (formData.payment.includes('支付宝') || formData.payment.includes('微信')) {
        showQrCodePayment.value = true
      }
      // 其他支付方式直接调用收费接口进行处理
      else {
        await processPayment()
      }
    }).catch(() => {
      clickConfirm.value = false
    })
  }
  catch (error) {
    console.error('发卡售气失败', error)
    showFailToast('发卡售气失败，请重试')
    clickConfirm.value = false
  }
}

// 抽取支付处理逻辑为单独函数
async function processPayment(tradeNo = ''): Promise<any> {
  try {
    formData.writeCardMoney = Number.parseFloat(formData.money)
    // 提取文件结果数组
    if (fileList.value && fileList.value.length > 0) {
      formData.files = fileList.value.map(file => file.result?.id).filter(Boolean)
    }
    // 组织操作人信息
    const operator = {
      orgId: currUser.resources.orgid,
      orgName: currUser.resources.orgs,
      depId: currUser.resources.depids,
      depName: currUser.resources.deps,
      operator: currUser.resources.name,
      operatorId: currUser.resources.id,
    }

    // 如果有凭证号，则设置
    if (tradeNo) {
      formData.serial_id = tradeNo
    }

    const result = await initCard(formData, userInfo.value, operator)
    console.log('发卡售气结果：', result)
    if (config.hasPrint) {
      // 显示电子凭证
      receiptData.value = {
        transactionId: result.id,
        datetime: new Date().toLocaleString(),
      }
      await printRef.value.printParams(config.printLogic, receiptData.value.transactionId)
    }
    else {
      clickConfirm.value = false
      printRef.value.close()
    }
  }
  catch (error) {
    console.error(error)
    showToast(`发卡售气失败${error.message || error.msg || '未知错误'}`)
    clickConfirm.value = false
    throw error
  }
}

async function handleQrCodePaymentSuccess(paymentResult) {
  try {
    // 使用公共处理函数处理支付
    await processPayment(paymentResult.tradeNo)
    showQrCodePayment.value = false
    clickConfirm.value = false
  }
  catch (error) {
    console.error(error)
    clickConfirm.value = false
    showToast(`发卡售气失败${error.message || error.msg || '未知错误'}`)
  }
}

function handleQrCodePaymentCancel() {
  showQrCodePayment.value = false
  clickConfirm.value = false
}

// 取消操作
function cancel() {
  showDialog({
    title: '确认取消',
    message: '是否确认取消当前收费操作？',
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '返回',
    confirmButtonColor: '#2563eb',
  }).then((result) => {
    if (result === 'confirm') {
      emit('closeOperation')
    }
  })
}

// 文件上传相关
function onFileAdded() {

}

function onFileRemoved() {
}

// 组件挂载时执行
onMounted(async () => {
  try {
    // 初始化配置信息
    await initConfig()
    // 初始化气价信息
    await getPrice()
    // 获取限购值
    await getLimitGas()
    // 获取开卡费
    await initGetOpenFee()
  }
  catch (error) {
    console.error('发卡售气初始化失败', error)
  }
  finally {
    loading.value = false
  }
})
</script>

<template>
  <div v-if="loading" class="loading-overlay">
    <van-loading size="24px">
      加载中...
    </van-loading>
  </div>

  <div v-else class="sell-gas-form">
    <!-- 提示信息卡片 -->
    <InfoCard
      type="info"
      main-text="请填写购气信息，系统将自动为当前用户进行充值操作。"
      :sub-text="`当前气价：${gasPrice} 元/m³${calculateDetail ? `(计算公式：${calculateDetail})` : ''}`"
    />

    <form @submit.prevent="handleSubmit">
      <div class="sell-gas-form__content">
        <!-- 气量和购气金额区域 -->
        <CardContainer>
          <CardHeader title="购气数量" />
          <GasExchangeCalculator
            :amount="formData.money"
            :gas="formData.gas"
            :max-amount="maxMoney"
            :max-gas-amount="maxGas"
            :limit-gas="limitGas"
            :limit-money="limitMoney"
            @calculate-by-gas="calculateByGas"
            @calculate-by-amount="calculateByAmount"
          />
        </CardContainer>

        <div class="sell-gas-form__row">
          <CardContainer>
            <CardHeader title="收款信息" />
            <!-- 收款信息区域 -->
            <PaymentInfo
              :amount="(formData.totalCost + formData.open_fee).toString()"
              :collection="formData.collection"
              :previous-balance="formData.balance"
              :cur-balance="formData.curBalance"
              @received-amount-change="receivedAmountChange"
            />
          </CardContainer>

          <ChargePrintSelectorAndRemarks
            v-model="formData.print"
            v-model:remarks="formData.comments"
            class="sell-gas-form__section-margin"
          />
        </div>

        <!-- 支付方式 -->
        <PaymentMethodSelectorCard
          v-model="formData.payment"
          title="支付方式"
          :allowed-methods="config.allowedMethods"
          class="sell-gas-form__section-margin"
        />

        <!-- 上传附件组件 -->
        <CardContainer class="sell-gas-form__section-margin">
          <FileUploader
            v-model:file-list="fileList"
            title="上传附件"
            :multiple="true"
            :max-size="10 * 1024 * 1024"
            :allowed-types="['image/png', 'image/jpeg', 'application/pdf']"
            @file-added="onFileAdded"
            @file-removed="onFileRemoved"
          />
        </CardContainer>
      </div>

      <!-- 实收金额和购气信息区域 -->
      <div class="sell-gas-form__summary">
        <div class="sell-gas-form__summary-content">
          <p class="sell-gas-form__summary-main">
            实收金额：<span class="sell-gas-form__summary-amount">¥ {{ formData.collection }}</span>
          </p>
          <p class="sell-gas-form__summary-sub">
            购气：¥{{ formData.money }} / {{ formData.gas }}m³
          </p>
        </div>
      </div>
      <!-- 按钮区域 -->
      <div class="sell-gas-form__actions">
        <van-button
          plain type="default"
          :loading="clickConfirm"
          @click="cancel"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          native-type="submit"
          :loading="clickConfirm"
          :disabled="!paymentValid"
        >
          确认售气
        </van-button>
      </div>
    </form>

    <!-- 电子小票弹窗 -->
    <ReceiptModal ref="printRef" />
    <!-- 二维码支付弹窗 -->
    <CodePayment
      v-if="showQrCodePayment"
      v-model:show="showQrCodePayment"
      :payment="formData.payment"
      :collection="formData.collection"
      type="发卡售气"
      @payment-success="handleQrCodePaymentSuccess"
      @payment-cancel="handleQrCodePaymentCancel"
    />
  </div>
</template>

<style lang="less" scoped>
.sell-gas-form {
  &__content {
    margin-bottom: 16px;
  }

  &__row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 16px;
    margin-bottom: 16px;

    @media screen and (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  &__hint {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
  }

  &__section-margin {
    margin-bottom: 16px;
  }

  &__summary {
    background: linear-gradient(to right, #e6f7ff, #f0f7ff);
    border: 1px solid #d6e8ff;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
  }

  &__summary-content {
    width: 100%;
  }

  &__summary-main {
    font-size: 16px;
    font-weight: 500;
    color: #4b5563;
    margin: 0 0 4px 0;
  }

  &__summary-amount {
    font-size: 20px;
    font-weight: 600;
    color: #2563eb;
  }

  &__summary-sub {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
  }
}
.loading-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style>
