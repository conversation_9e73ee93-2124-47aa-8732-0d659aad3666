# 发卡售气业务组件

这个文件夹包含了发卡售气业务流程中使用的组件，基于 Vue3 组合式 API 和 Vant UI 组件库开发。这些组件可以用于构建一个完整的发卡售气业务办理界面。

## 组件列表

### SellGasForm

发卡售气表单主组件，整合了气量计算、支付方式选择、收据打印等功能。

**Props:**
- `userInfo`：用户信息对象，必需，包含 `id`, `name`, `meterNumber` 等属性
- `gasPrice`：气价，默认为 5.0 元/m³
- `minAmount`：最小购气金额，默认为 10 元
- `maxAmount`：最大购气金额，默认为 500 元
- `defaultAmount`：默认购气金额，默认为 50 元
- `operatorName`：操作员姓名，默认为 "营业员"
- `companyName`：公司名称，默认为 "城市燃气服务有限公司"
- `servicePhone`：客服电话，默认为 "************"

**Events:**
- `cancel`：取消操作事件
- `submit`：提交表单事件，参数为包含所有表单数据的对象

**使用示例:**
```vue
<SellGasForm
  :user-info="userInfo"
  :gas-price="5.0"
  :default-amount="50"
  @cancel="handleCancel"
  @submit="handleSubmit"
/>
```

### GasExchangeCalculator

气量和购气金额换算组件，用于计算气量和金额之间的关系。

**Props:**
- `gasPrice`：气价，默认为 5.0 元/m³
- `minAmount`：最小购气金额，默认为 10 元
- `maxAmount`：最大购气金额，默认为 500 元
- `minGasAmount`：最小购气量，默认为 2 m³
- `maxGasAmount`：最大购气量，默认为 100 m³
- `defaultAmount`：默认购气金额，默认为 50 元
- `title`：组件标题，默认为 "购气数量"
- `icon`：图标类名，默认为 "fa-calculator"

**Events:**
- `update:amount`：金额更新事件
- `update:gasAmount`：气量更新事件

**使用示例:**
```vue
<GasExchangeCalculator
  v-model:amount="amount"
  v-model:gasAmount="gasAmount"
  :gas-price="5.0"
/>
```

### PaymentInfo

收款信息组件，用于录入收款金额并计算找零。

**Props:**
- `amount`：购气金额，必需
- `title`：组件标题，默认为 "收款信息"
- `icon`：图标类名，默认为 "fa-money-bill-wave"

**Events:**
- `update:receivedAmount`：收款金额更新事件
- `update:balance`：结余金额更新事件
- `valid`：验证状态更新事件，当收款金额不小于购气金额时为 true

**使用示例:**
```vue
<PaymentInfo
  v-model:receivedAmount="receivedAmount"
  v-model:balance="balance"
  :amount="50"
  @valid="isValid = $event"
/>
```

### PrintOptions

打印及备注组件，用于选择收据打印方式和添加备注信息。

**Props:**
- `receiptType`：收据类型，默认为 "electronic"
- `remarks`：备注信息，默认为空字符串
- `receiptOptions`：收据选项列表，默认提供电子票据、普通收据和两者都需要三种选项
- `title`：组件标题，默认为 "打印及备注"
- `icon`：图标类名，默认为 "fa-print"

**Events:**
- `update:receiptType`：收据类型更新事件
- `update:remarks`：备注信息更新事件

**使用示例:**
```vue
<PrintOptions
  v-model:receiptType="receiptType"
  v-model:remarks="remarks"
/>
```

### GasReceipt

气表收据组件，用于展示购气交易凭证。

**Props:**
- `transactionId`：交易单号，必需
- `userName`：用户姓名，必需
- `userId`：用户编号，必需
- `cardNo`：卡号/表号，必需
- `amount`：购气金额，必需
- `gasAmount`：购气量，必需
- `paymentMethod`：支付方式，默认为 "cash"
- `receivedAmount`：实收金额，必需
- `balance`：找零金额，必需
- `remarks`：备注信息，默认为空字符串
- `operator`：操作员，默认为 "营业员"
- `datetime`：交易时间，默认为当前时间
- `qrcode`：二维码图片链接，默认为空字符串
- `companyName`：公司名称，默认为 "城市燃气服务有限公司"
- `servicePhone`：客服电话，默认为 "************"

**使用示例:**
```vue
<GasReceipt
  transaction-id="GS20240501001"
  user-name="张三"
  user-id="1001256"
  card-no="GS20241001256"
  :amount="50"
  :gas-amount="10"
  payment-method="cash"
  :received-amount="100"
  :balance="50"
  operator="营业员小王"
/>
```

## 示例页面

在 `src/views/SellGas/index.vue` 中有一个完整的示例页面，展示了如何使用这些组件构建一个发卡售气业务界面。

## 组件使用

这些组件可以单独导入使用：

```js
import { SellGasForm, GasExchangeCalculator } from '@/components/sellGas';

// 或者直接导入默认组件
import SellGasForm from '@/components/sellGas';
```
