import type { RouteRecordRaw } from 'vue-router'
import XForm from '@af-mobile-client-vue3/components/data/XForm/index.vue'
import mixRoutes from '@af-mobile-client-vue3/router/routes'
import BusinessHandlerPage from '@/views/BusinessHandler/index.vue'
import CardReplacement from '@/views/cardReplacement/index.vue'
import DanWeiDetail from '@/views/DanWeiManage/DanWeiDetail.vue'
import DanWeiManage from '@/views/DanWeiManage/index.vue'
import MeterlenList from '@/views/DanWeiManage/MeterlenList.vue'
import example1 from '@/views/example/example1/index.vue'
import example2 from '@/views/example/example2/index.vue'
import example from '@/views/example/index.vue'
import GasCompensation from '@/views/gasCompensation/index.vue'
import GasTransfer from '@/views/gasTransfer/index.vue'
import MeterReplaceMent from '@/views/meterReplaceMent/meterReplaceMent.vue'
import meterReset from '@/views/meterReset/meterReset.vue'
import OnlineRevenuePage from '@/views/OnlineRevenue/index.vue'
import overUsageCharge from '@/views/overUsageCharge/index.vue'
import OwnershipTransfer from '@/views/ownershipTransfer/index.vue'
import ReturnPremium from '@/views/ReturnPremium/index.vue'
import statusChange from '@/views/statusChange/index.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'root',
    redirect: { name: 'example' },
    children: [],
  },
  {
    path: '/meter-reading',
    name: 'MeterReading',
    component: () => import('@/views/MeterReading/index.vue'),
    meta: {
      title: '在线抄表',
      index: 0,
    },
  },
  {
    path: '/meterInfoManage',
    name: 'meterInfoManage',
    component: () => import('@/views/meterInfoManage/index.vue'),
    meta: {
      title: '表计管理',
      index: 0,
    },
  },
  {
    path: '/Components/XForm',
    name: 'XForm',
    component: XForm,
    props: route => ({
      groupFormItems: JSON.parse(decodeURIComponent(route.query.groupFormItems as string)),
      serviceName: route.query.serviceName,
      formData: JSON.parse(decodeURIComponent(route.query.formData as string)),
      mode: route.query.mode,
    }),
    meta: { title: '新增/修改表单' },
  },
  {
    path: '/example',
    name: 'example',
    component: example,
    meta: {
      index: 0,
    },
  },
  {
    path: '/example1',
    name: 'example1',
    component: example1,
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/example2',
    name: 'example2',
    component: example2,
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/Component/DanWeiManage',
    name: 'DanWeiManage',
    component: DanWeiManage,
  },
  {
    path: '/Component/DanWeiDetail/:id',
    name: 'DanWeiDetail',
    component: DanWeiDetail,
  },
  {
    path: '/Component/MeterlenList',
    name: 'MeterlenList',
    component: MeterlenList,
  },
  {
    path: '/CardReplacement',
    name: 'CardReplacement',
    component: CardReplacement,
    meta: {
      title: '补卡',
      keepAlive: false,
    },
  },
  {
    path: '/GasCompensation',
    name: 'GasCompensation',
    component: GasCompensation,
    meta: {
      title: '补气',
      keepAlive: false,
    },
  },
  {
    path: '/meterReplacement',
    name: 'meterReplacement',
    component: MeterReplaceMent,
    meta: {
      title: '换表',
      keepAlive: false,
    },
  },
  {
    path: '/statusChange',
    name: 'statusChange',
    component: statusChange,
    meta: {
      title: '停用&启用',
      keepAlive: false,
    },
  },
  {
    path: '/meterReset',
    name: 'meterReset',
    component: meterReset,
    meta: {
      title: '清零',
      keepAlive: false,
    },
  },
  {
    path: '/GasTransfer',
    name: 'GasTransfer',
    component: GasTransfer,
    meta: {
      title: '转气',
      keepAlive: false,
    },
  },
  {
    path: '/OwnershipTransfer',
    name: 'OwnershipTransfer',
    component: OwnershipTransfer,
    meta: {
      title: '过户',
      keepAlive: false,
    },
  },
  {
    path: '/overUsageCharge',
    name: 'overUsageCharge',
    component: overUsageCharge,
    meta: {
      title: '超用收费',
      keepAlive: false,
    },
  },
  {
    path: '/OnlineRevenue',
    name: 'OnlineRevenue',
    component: OnlineRevenuePage,
    meta: {
      title: '在线操作',
      keepAlive: true,
    },
  },
  {
    path: '/OnlineRevenueBind',
    name: 'OnlineRevenueBind',
    component: OnlineRevenuePage,
    meta: {
      title: '在线操作',
      keepAlive: true,
      type: 'bind',
    },
  },
  {
    path: '/BusinessHandler',
    name: 'BusinessHandler',
    component: BusinessHandlerPage,
    meta: {
      title: '业务办理',
      keepAlive: false,
    },
  },
  {
    path: '/ReturnPremium',
    name: 'ReturnPremium',
    component: ReturnPremium,
    meta: {
      title: '退费',
      keepAlive: false,
    },
  },
  {
    path: '/:catchAll(.*)',
    redirect: {
      name: '404',
    },
  },
]

// 除了元素path 属性是 / 的元素 将公共路由全部混入
if (mixRoutes && mixRoutes.length)
  routes.push(...mixRoutes.filter(route => route.path !== '/' && route.path !== '/:catchAll(.*)'))

export default routes
