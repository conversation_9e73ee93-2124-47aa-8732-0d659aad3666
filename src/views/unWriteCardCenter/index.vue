<script setup lang="ts">
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { showConfirmDialog, showDialog, showFailToast, showSuccessToast } from 'vant'
import { computed, onMounted, reactive, ref } from 'vue'
import { FileUploader, GridFileUploader } from '@/components/common'
import ReceiptModal from '@/components/common/ReceiptModal.vue'
import { offlineWrite } from '@/services/cardService'
import useBusinessStore from '@/stores/modules/business'
// 定义组件事件
const emit = defineEmits<{
  (e: 'complete', data?: any): void
  (e: 'closeOperation'): void
}>()

// 获取store
const businessStore = useBusinessStore()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showResult = ref(false)
const resultMessage = ref('')
const operationSuccess = ref(false)

// 配置对象
const config = reactive({
  fileTypes: [],
})

const gridFileUploaderRef = ref()

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

async function initConfig() {
  try {
    const res = await getConfigByNameAsync('mobile_unWriteCardConfig')
    if (res) {
      Object.assign(config, res)
    }
  }
  catch (error) {
    console.error('初始化配置失败', error)
  }
}

// 子组件引用
const printRef = ref()

// 表单数据
const formData = reactive({
  remarks: '',
  files: [],
})

// 获取当前用户信息
const currentUser = computed(() => businessStore.currentUser)
const currentCard = computed(() => businessStore.currentCard)
const currUser = computed(() => userStore.getLogin().f)

// 未写卡记录
const unWriteCardRecord = computed(() => businessStore.unWriteCardRecord)

// 验证未写卡记录数量
function validateUnWriteCardRecord() {
  const records = unWriteCardRecord.value

  if (!records || records.length === 0) {
    showDialog({
      title: '提示',
      message: '不存在未写卡信息，返回操作页面',
      confirmButtonText: '返回',
      confirmButtonColor: '#ff6b6b',
    }).then(() => {
      emit('closeOperation')
    })
    return false
  }

  if (records.length > 1) {
    showDialog({
      title: '提示',
      message: '存在多条未写卡信息，请核实后重试',
      confirmButtonText: '返回',
      confirmButtonColor: '#ff6b6b',
    }).then(() => {
      emit('closeOperation')
    })
    return false
  }

  return true
}

// 格式化的待写卡数据
const formattedRecord = computed(() => {
  const records = unWriteCardRecord.value
  if (!records || records.length !== 1) {
    return {
      date: '暂无记录',
      operator: '暂无',
      amount: '0.00',
      gas: '0.00',
      money: '0.00',
      paymentMethod: '暂无',
    }
  }

  const record = records[0]
  return {
    date: record.f_date || '暂无记录',
    operator: record.f_operator || '暂无',
    amount: record.f_preamount?.toFixed(2) || '0.00',
    gas: record.f_pregas?.toFixed(2) || '0.00',
    money: record.f_collection?.toFixed(2) || '0.00',
    paymentMethod: record.f_payment || '暂无',
  }
})

// 是否可以写卡
const canWriteCard = computed(() => {
  const records = unWriteCardRecord.value
  return records && records.length === 1 && (Number(records[0].f_pregas || 0) > 0)
})

// 初始化
onMounted(async () => {
  await initConfig()

  console.log('线下写卡组件初始化', {
    currentUser: currentUser.value,
    currentCard: currentCard.value,
    unWriteCardRecord: unWriteCardRecord.value,
  })

  // 验证未写卡记录
  validateUnWriteCardRecord()
})

// 开始写卡操作
async function startWriteCard() {
  if (!canWriteCard.value) {
    showFailToast('当前没有可写卡的记录')
    return
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return
    }
  }

  const record = unWriteCardRecord.value[0]

  try {
    // 确认写卡
    let message = '确认要写入气量：'
    const totalGas = Number(record.f_pregas || 0)
    const totalAmount = Number(record.f_preamount || 0)

    message += `${totalGas.toFixed(2)}m³（金额：¥${totalAmount.toFixed(2)}）到IC卡上吗？`

    await showConfirmDialog({
      title: '确认写卡',
      message,
      confirmButtonText: '确认写卡',
      cancelButtonText: '取消',
      confirmButtonColor: '#07c160',
    })

    // 开始写卡流程
    await performWriteCard()
  }
  catch (error) {
    // 用户取消操作
    console.log('用户取消写卡操作')
  }
}

// 执行写卡操作
async function performWriteCard() {
  loading.value = true
  resultMessage.value = '正在写卡...'

  try {
    // 提取文件结果数组
    let fileResults = []
    if (formData.files?.length > 0) {
      fileResults = formData.files
        .filter(file => file.result)
        .map(file => file.result.id)
    }

    const record = unWriteCardRecord.value[0]

    // 调用写卡API
    const result = await offlineWrite({
      ...record,
      f_comments: formData.remarks,
      files: fileResults.length > 0 ? fileResults : null,
    }, currentUser.value, {
      f_operator: currUser.value.resources.name,
      f_operatorid: currUser.value.resources.id,
      f_orgid: currUser.value.resources.orgid,
      f_orgname: currUser.value.resources.orgs,
      f_depid: currUser.value.resources.depids,
      f_depname: currUser.value.resources.deps,
    })

    operationSuccess.value = true
    resultMessage.value = '写卡成功，请取回IC卡。'
    showResult.value = true

    // 生成小票信息
    await printRef.value.printParams('mobile_unWriteCardPrintParams', result)

    showSuccessToast('写卡成功')
    emit('complete', { status: true, readCard: true })
  }
  catch (error: any) {
    operationSuccess.value = false
    resultMessage.value = `写卡失败：${error.message || error.msg || '请检查读卡器连接或IC卡状态后重试'}`
    showResult.value = true
    showFailToast(`写卡失败：${error.message || error.msg || '未知错误'}`)
  }
  finally {
    loading.value = false
  }
}

// 关闭结果弹窗
function closeResult() {
  showResult.value = false
  if (operationSuccess.value) {
    emit('complete', { status: true, readCard: true })
  }
}

// 取消操作
function handleCancel() {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    emit('closeOperation')
  }, 300)
}
</script>

<template>
  <div class="write-card-page">
    <!-- 待写卡数据 -->
    <CardContainer v-if="canWriteCard" title="待写卡数据" class="mb-16">
      <div class="record-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
        <div class="record-item" style="grid-column: span 2;">
          <dt class="record-label">
            交易时间
          </dt>
          <dd class="record-value">
            {{ formattedRecord.date }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            支付方式
          </dt>
          <dd class="record-value">
            {{ formattedRecord.paymentMethod }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            收款人
          </dt>
          <dd class="record-value">
            {{ formattedRecord.operator }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            交易金额
          </dt>
          <dd class="record-value">
            ¥{{ formattedRecord.amount }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            交易气量
          </dt>
          <dd class="record-value">
            {{ formattedRecord.gas }} m³
          </dd>
        </div>
      </div>
    </CardContainer>

    <!-- 备注信息 -->
    <CardContainer title="备注信息" class="mb-16">
      <van-field
        v-model="formData.remarks"
        type="textarea"
        rows="3"
        placeholder="请输入写卡原因或备注信息"
        maxlength="200"
        show-word-limit
        class="remarks-field"
      />
    </CardContainer>

    <GridFileUploader
      v-if="useGridUploader"
      ref="gridFileUploaderRef"
      v-model:file-list="formData.files"
      :file-types="config.fileTypes"
      title="上传附件"
    />
    <!-- 文件上传 -->
    <CardContainer v-else class="mb-16">
      <FileUploader
        v-model:file-list="formData.files"
        :multiple="true"
        use-type="线下写卡"
      />
    </CardContainer>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button
        plain
        class="cancel-btn"
        :loading="loading"
        @click="handleCancel"
      >
        取消
      </van-button>
      <van-button
        type="success"
        class="write-card-btn"
        :loading="loading"
        :disabled="!canWriteCard"
        @click="startWriteCard"
      >
        <van-icon name="edit" class="mr-1" />
        确认写卡
      </van-button>
    </div>

    <!-- 结果弹窗 -->
    <van-overlay v-model:show="showResult" class="result-overlay">
      <div class="result-content">
        <div class="result-icon">
          <van-icon
            :name="operationSuccess ? 'success' : 'close'"
            :color="operationSuccess ? '#07c160' : '#ee0a24'"
            size="48"
          />
        </div>
        <h3 class="result-title">
          {{ operationSuccess ? '写卡成功' : '写卡失败' }}
        </h3>
        <p class="result-message">
          {{ resultMessage }}
        </p>
        <van-button
          type="primary"
          class="result-btn"
          @click="closeResult"
        >
          确定
        </van-button>
      </div>
    </van-overlay>

    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" />
  </div>
</template>

<style lang="less" scoped>
.write-card-page {
  position: relative;
  box-sizing: border-box;

  .card-info {
    .card-header {
      padding: 12px;
    }

    .balance-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }
  }

  .card-info-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 12px !important;

    .card-info-item {
      padding: 10px;
      background-color: #eff6ff;
      border-radius: 6px;

      &.full-width {
        grid-column: 1 / -1 !important;
      }

      &.empty {
        background-color: transparent;
      }

      .card-info-label {
        color: #6b7280;
        font-size: 12px;
        margin: 0 0 4px 0;
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .card-info-value {
        color: #2563eb;
        font-size: 16px;
        margin: 0;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* 手机端样式调整 */
      @media screen and (max-width: 767px) {
        padding: 8px;

        .card-info-label {
          font-size: 12px;
          margin-bottom: 2px;
        }

        .card-info-value {
          font-size: 14px;
          white-space: normal;
          word-break: break-word;
        }
      }
    }

    /* 响应式布局 */
    @media screen and (max-width: 767px) {
      grid-template-columns: repeat(3, 1fr) !important;
      gap: 8px !important;
    }
  }

  .record-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 12px !important;

    .record-item {
      padding: 10px;
      background-color: #f9fafb;
      border-radius: 6px;

      &.full-width {
        grid-column: 1 / -1 !important;
      }

      &.empty {
        background-color: transparent;
      }

      .record-label {
        color: #666;
        font-size: 14px;
        margin: 0 0 4px 0;
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .record-value {
        color: #333;
        font-size: 14px;
        margin: 0;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* 手机端样式调整 */
      @media screen and (max-width: 767px) {
        padding: 8px;

        .record-label {
          font-size: 12px;
          margin-bottom: 2px;
        }

        .record-value {
          font-size: 12px;
          white-space: normal;
          word-break: break-word;
        }
      }
    }

    /* 响应式布局 */
    @media screen and (max-width: 767px) {
      grid-template-columns: repeat(3, 1fr) !important;
      gap: 8px !important;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 16px;

    .cancel-btn {
      border-color: #d1d5db;
      color: #4b5563;
    }

    .write-card-btn {
      background-color: #07c160;
      border-color: #07c160;

      &:disabled {
        background-color: #f3f4f6;
        border-color: #d1d5db;
        color: #9ca3af;
      }

      .mr-1 {
        margin-right: 4px;
      }
    }
  }

  // 结果弹窗样式
  .result-overlay {
    display: flex;
    align-items: center;
    justify-content: center;

    .result-content {
      background: white;
      border-radius: 12px;
      padding: 32px 24px;
      text-align: center;
      max-width: 320px;
      width: 90%;

      .result-icon {
        margin-bottom: 16px;
      }

      .result-title {
        font-size: 18px;
        font-weight: 500;
        color: #111827;
        margin-bottom: 8px;
      }

      .result-message {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 24px;
      }

      .result-btn {
        width: 100%;
      }
    }
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  // 移动端样式
  @media screen and (max-width: 767px) {
    .card-info {
      .balance-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  // 平板模式样式
  @media screen and (min-width: 768px) {
    .action-buttons {
      width: 85%;
      max-width: 960px;
      margin: 0 auto;
    }
  }
}
</style>

<style>
/* 全局样式 */
.write-card-page .van-field,
.write-card-page .van-cell {
  background-color: #f9fafb !important;
}

:deep(.van-field__control) {
  text-align: left !important;
}

:deep(.van-field) {
  background-color: #f9fafb;
  border-radius: 4px;
  padding: 2px 8px !important;
  margin-bottom: 4px;
}

:deep(textarea.van-field__control) {
  text-align: left;
  padding: 8px !important;
}

.write-card-page :deep(.remarks-field) {
  .van-field__control {
    text-align: left !important;
  }
}
</style>
