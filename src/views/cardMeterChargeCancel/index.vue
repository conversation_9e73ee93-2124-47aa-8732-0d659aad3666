<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { showConfirmDialog, showDialog, showFailToast, showSuccessToast, showToast } from 'vant'
import { computed, onMounted, reactive, ref } from 'vue'
import { FileUploader, GridFileUploader } from '@/components/common'
import ReceiptModal from '@/components/common/ReceiptModal.vue'
import { getLastCardCharge } from '@/services/api/business'
import { cancelCard } from '@/services/cardService'
import useBusinessStore from '@/stores/modules/business'

// 定义组件事件
const emit = defineEmits<{
  (e: 'complete', data?: any): void
  (e: 'closeOperation'): void
}>()

// 获取store
const businessStore = useBusinessStore()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showResult = ref(false)
const resultMessage = ref('')
const operationSuccess = ref(false)

const fileList = ref<FileItem[]>([])

// 配置对象
const config = reactive({
  fileTypes: [],
})

const gridFileUploaderRef = ref()

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// 子组件引用
const printRef = ref()

// 表单数据
const formData = reactive({
  remarks: '',
  files: [],
})

// 获取当前用户信息
const currentUser = computed(() => businessStore.currentUser)
const currentCard = computed(() => businessStore.currentCard)
const currUser = computed(() => userStore.getLogin().f)

// 卡片信息
const cardInfo = computed(() => {
  if (!currentCard.value) {
    return {
      cardNumber: '',
      currentGas: '0.00',
      currentMoney: '0.00',
      hasGas: false,
      hasMoney: false,
    }
  }

  const gas = Number(currentCard.value.Gas || 0)
  const money = Number(currentCard.value.Money || 0)

  return {
    cardNumber: currentCard.value.CardID || '',
    currentGas: gas.toFixed(2),
    currentMoney: money.toFixed(2),
    hasGas: gas > 0,
    hasMoney: money > 0,
  }
})

// 最后一次购气记录
const lastRecord = ref(null)

// 获取最后一次购气记录
async function fetchLastRecord() {
  if (!currentUser.value?.f_userinfo_id)
    return

  try {
    const result = await getLastCardCharge({
      f_userinfo_id: `${currentUser.value.f_userinfo_id}`,
    })
    lastRecord.value = result
  }
  catch (error) {
    console.error('获取最后一次购气记录失败:', error)
    // 如果获取失败，使用默认值
    lastRecord.value = null
  }

  if (!lastRecord.value) {
    showDialog({
      title: '提示',
      message: '暂无可撤销购气记录,返回操作页面',
      confirmButtonText: '返回',
      confirmButtonColor: '#ff6b6b',
    }).then(() => {
      emit('closeOperation')
    })
  }

  if (lastRecord.value.fillgas) {
    showToast('当前IC卡关联补气记录，请谨慎核对')
  }
}

// 格式化的最后一次购气记录
const formattedLastRecord = computed(() => {
  if (!lastRecord.value) {
    return {
      date: '暂无记录',
      operator: '暂无',
      amount: '0.00',
      gas: '0.00',
      money: '0.00',
      paymentMethod: '暂无',
    }
  }

  return {
    date: lastRecord.value.f_date || '暂无记录',
    operator: lastRecord.value.f_operator || '暂无',
    amount: lastRecord.value.f_preamount?.toFixed(2) || '0.00',
    gas: lastRecord.value.f_pregas?.toFixed(2) || '0.00',
    money: lastRecord.value.f_collection?.toFixed(2) || '0.00',
    paymentMethod: lastRecord.value.f_payment || '暂无',
    fillgas: lastRecord.value.fillgas || null,
  }
})

// 是否可以撤销
const canRefund = computed(() => {
  return businessStore.canRefundGas
})

// 初始化
onMounted(async () => {
  console.log('购气撤销组件初始化', {
    currentUser: currentUser.value,
    currentCard: currentCard.value,
    cardInfo: cardInfo.value,
  })

  // 获取最后一次购气记录
  await fetchLastRecord()
})

// 开始撤销操作
async function startRefund() {
  if (!canRefund.value) {
    showFailToast('当前IC卡上没有可撤销的气量或金额')
    return
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return
    }
  }

  // 验证卡上数据与最后一次购气记录是否一致
  if (!await validateRefundData()) {
    return
  }

  try {
    // 确认撤销
    let message = '确认要撤销IC卡上的'
    if (cardInfo.value.hasGas && cardInfo.value.hasMoney) {
      message += `气量${cardInfo.value.currentGas}m³和金额${cardInfo.value.currentMoney}元`
    }
    else if (cardInfo.value.hasGas) {
      message += `气量${cardInfo.value.currentGas}m³`
    }
    else if (cardInfo.value.hasMoney) {
      message += `金额${cardInfo.value.currentMoney}元`
    }
    message += '吗？此操作不可撤销！'

    await showConfirmDialog({
      title: '确认撤销',
      message,
      confirmButtonText: '确认撤销',
      cancelButtonText: '取消',
      confirmButtonColor: '#ff6b6b',
    })

    // 开始撤销流程
    await performRefund()
  }
  catch (error) {
    // 用户取消操作
    console.log('用户取消撤销操作')
  }
}

// 验证撤销数据一致性
async function validateRefundData() {
  if (!lastRecord.value) {
    showFailToast('无法获取最后一次购气记录，无法进行撤销验证')
    return false
  }

  // 获取用户表类型，判断是气量表还是金额表

  // 将数值转换为两位小数进行比较
  const cardGas = Number(cardInfo.value.currentGas).toFixed(2)
  const cardMoney = Number(cardInfo.value.currentMoney).toFixed(2)
  const recordGas = Number(lastRecord.value.f_pregas || 0).toFixed(2)
  const recordAmount = Number(lastRecord.value.f_preamount || 0).toFixed(2)

  // 根据表类型进行不同的验证
  if (currentUser.value.f_collection_type === '按气量') {
    // 气量表：验证气量是否一致
    if (cardGas !== recordGas) {
      await showDialog({
        title: '提示',
        message: `卡上气量(${cardGas}m³)与最后一次购气记录(${recordGas}m³)不一致，无法撤销`,
        confirmButtonText: '确定',
        confirmButtonColor: '#ff6b6b',
      }).then(() => {
        emit('closeOperation')
      })
      return false
    }
  }
  else {
    // 金额表：验证金额是否一致
    if (cardMoney !== recordAmount) {
      await showDialog({
        title: '提示',
        message: `卡上金额(¥${cardMoney})与最后一次购气记录(¥${recordAmount})不一致，无法撤销`,
        confirmButtonText: '确定',
        confirmButtonColor: '#ff6b6b',
      }).then(() => {
        return emit('closeOperation')
      })
      return false
    }
  }

  return true
}

// 执行撤销操作
async function performRefund() {
  loading.value = true
  resultMessage.value = '正在撤销气量...'

  try {
    // 提取文件结果数组
    const fileResults = fileList.value.map(file => file.result?.id).filter(Boolean)

    // 计算总气量金额 收费+补气
    const f_pregas = Number(lastRecord.value.f_pregas || 0) + Number(lastRecord.value.fillgas?.f_pregas || 0)
    const f_preamount = Number(lastRecord.value.f_preamount || 0) + Number(lastRecord.value.fillgas?.f_preamount || 0)
    // 调用撤销API
    const result = await cancelCard({
      ...lastRecord.value,
      f_pregas,
      f_preamount,
      files: fileResults.length > 0 ? fileResults : null,
    }, currentUser.value, currentCard.value, {
      f_operator: currUser.value.resources.name,
      f_operatorid: currUser.value.resources.id,
      f_orgid: currUser.value.resources.orgid,
      f_orgname: currUser.value.resources.orgs,
      f_depid: currUser.value.resources.depids,
      f_depname: currUser.value.resources.deps,
    })

    operationSuccess.value = true
    resultMessage.value = '气量撤销成功，请取回IC卡。'
    showResult.value = true

    // 生成小票信息
    if (printRef.value && result) {
      await printRef.value.printParams('mobile_cardMeterChargeCancelPrintParams', result)
    }

    showSuccessToast('购气撤销成功')
    emit('complete', { status: true, readCard: true })
  }
  catch (error: any) {
    operationSuccess.value = false
    resultMessage.value = `撤销失败：${error.message || error.msg || '请检查读卡器连接或IC卡状态后重试'}`
    showResult.value = true
    showFailToast(`撤销失败：${error.message || error.msg || '未知错误'}`)
  }
  finally {
    loading.value = false
  }
}

// 关闭结果弹窗
function closeResult() {
  showResult.value = false
  if (operationSuccess.value) {
    emit('complete', { status: true })
  }
}

// 取消操作
function handleCancel() {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    emit('closeOperation')
  }, 300)
}

async function initConfig() {
  const res = await getConfigByNameAsync('mobile_cardMeterChargeCancelConfig')
  if (res) {
    Object.assign(config, res)
  }
}

onMounted(async () => {
  await initConfig()
})
</script>

<template>
  <div class="gas-refund-page">
    <!-- IC卡信息 -->
    <CardContainer title="IC卡信息" class="mb-16">
      <div class="card-info">
        <div class="card-info-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
          <!-- 卡号 -->
          <div class="card-info-item full-width" style="grid-column: 1 / -1;">
            <dt class="card-info-label">
              卡号
            </dt>
            <dd class="card-info-value">
              {{ cardInfo.cardNumber || '未读取' }}
            </dd>
          </div>

          <!-- 气量和金额 -->
          <div class="card-info-item">
            <dt class="card-info-label">
              卡上气量
            </dt>
            <dd class="card-info-value">
              {{ cardInfo.currentGas }} m³
            </dd>
          </div>
          <div class="card-info-item">
            <dt class="card-info-label">
              卡上金额
            </dt>
            <dd class="card-info-value">
              ¥{{ cardInfo.currentMoney }}
            </dd>
          </div>
          <!-- 添加空项保持布局平衡 -->
          <div class="card-info-item empty" style="background-color: transparent;" />
        </div>
      </div>
    </CardContainer>

    <!-- 最后一次购气记录 -->
    <CardContainer v-if="canRefund" title="最后一次购气记录" class="mb-16">
      <div class="record-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
        <div class="record-item" style="grid-column: span 2;">
          <dt class="record-label">
            购气时间
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.date }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            支付方式
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.paymentMethod }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            收款人
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.operator }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            购气金额
          </dt>
          <dd class="record-value">
            ¥{{ formattedLastRecord.amount }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            购气气量
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.gas }} m³
          </dd>
        </div>
      </div>
    </CardContainer>

    <!-- 最后一次购气 关联补气记录 -->
    <CardContainer v-if="canRefund && lastRecord?.value?.fillgas" title="关联补气记录" class="mb-16">
      <div class="record-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
        <div class="record-item" style="grid-column: span 2;">
          <dt class="record-label">
            购气时间
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.fillgas?.date }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            操作人
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.fillgas?.operator }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            补气金额
          </dt>
          <dd class="record-value">
            ¥{{ formattedLastRecord.fillgas?.amount }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            补气气量
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.fillgas?.gas }} m³
          </dd>
        </div>
      </div>
    </CardContainer>

    <!-- 备注信息 -->
    <CardContainer title="备注信息" class="mb-16">
      <van-field
        v-model="formData.remarks"
        type="textarea"
        rows="3"
        placeholder="请输入撤销原因或备注信息"
        maxlength="200"
        show-word-limit
        class="remarks-field"
      />
    </CardContainer>

    <GridFileUploader
      v-if="useGridUploader"
      ref="gridFileUploaderRef"
      v-model:file-list="fileList"
      :file-types="config.fileTypes"
      title="上传附件"
    />
    <!-- 文件上传 -->
    <CardContainer v-else class="mb-16">
      <FileUploader
        v-model:file-list="fileList"
        :multiple="true"
        use-type="购气撤销"
      />
    </CardContainer>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button
        plain
        class="cancel-btn"
        :loading="loading"
        @click="handleCancel"
      >
        取消
      </van-button>
      <van-button
        type="danger"
        class="refund-btn"
        :loading="loading"
        :disabled="!canRefund"
        @click="startRefund"
      >
        <van-icon name="replay" class="mr-1" />
        确认撤销
      </van-button>
    </div>

    <!-- 结果弹窗 -->
    <van-overlay v-model:show="showResult" class="result-overlay">
      <div class="result-content">
        <div class="result-icon">
          <van-icon
            :name="operationSuccess ? 'success' : 'close'"
            :color="operationSuccess ? '#07c160' : '#ee0a24'"
            size="48"
          />
        </div>
        <h3 class="result-title">
          {{ operationSuccess ? '撤销成功' : '撤销失败' }}
        </h3>
        <p class="result-message">
          {{ resultMessage }}
        </p>
        <van-button
          type="primary"
          class="result-btn"
          @click="closeResult"
        >
          确定
        </van-button>
      </div>
    </van-overlay>

    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" />
  </div>
</template>

<style lang="less" scoped>
.gas-refund-page {
  position: relative;
  box-sizing: border-box;

  .card-info {
    .card-header {
      padding: 12px;
    }

    .balance-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }
  }

  .card-info-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 12px !important;

    .card-info-item {
      padding: 10px;
      background-color: #eff6ff;
      border-radius: 6px;

      &.full-width {
        grid-column: 1 / -1 !important;
      }

      &.empty {
        background-color: transparent;
      }

      .card-info-label {
        color: #6b7280;
        font-size: 12px;
        margin: 0 0 4px 0;
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .card-info-value {
        color: #2563eb;
        font-size: 16px;
        margin: 0;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* 手机端样式调整 */
      @media screen and (max-width: 767px) {
        padding: 8px;

        .card-info-label {
          font-size: 12px;
          margin-bottom: 2px;
        }

        .card-info-value {
          font-size: 14px;
          white-space: normal;
          word-break: break-word;
        }
      }
    }

    /* 响应式布局 */
    @media screen and (max-width: 767px) {
      grid-template-columns: repeat(3, 1fr) !important;
      gap: 8px !important;
    }
  }

  .record-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 12px !important;

    .record-item {
      padding: 10px;
      background-color: #f9fafb;
      border-radius: 6px;

      &.full-width {
        grid-column: 1 / -1 !important;
      }

      &.empty {
        background-color: transparent;
      }

      .record-label {
        color: #666;
        font-size: 14px;
        margin: 0 0 4px 0;
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .record-value {
        color: #333;
        font-size: 14px;
        margin: 0;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* 手机端样式调整 */
      @media screen and (max-width: 767px) {
        padding: 8px;

        .record-label {
          font-size: 12px;
          margin-bottom: 2px;
        }

        .record-value {
          font-size: 12px;
          white-space: normal;
          word-break: break-word;
        }
      }
    }

    /* 响应式布局 */
    @media screen and (max-width: 767px) {
      grid-template-columns: repeat(3, 1fr) !important;
      gap: 8px !important;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 16px;

    .cancel-btn {
      border-color: #d1d5db;
      color: #4b5563;
    }

    .refund-btn {
      background-color: #dc2626;
      border-color: #dc2626;

      &:disabled {
        background-color: #f3f4f6;
        border-color: #d1d5db;
        color: #9ca3af;
      }

      .mr-1 {
        margin-right: 4px;
      }
    }
  }

  // 结果弹窗样式
  .result-overlay {
    display: flex;
    align-items: center;
    justify-content: center;

    .result-content {
      background: white;
      border-radius: 12px;
      padding: 32px 24px;
      text-align: center;
      max-width: 320px;
      width: 90%;

      .result-icon {
        margin-bottom: 16px;
      }

      .result-title {
        font-size: 18px;
        font-weight: 500;
        color: #111827;
        margin-bottom: 8px;
      }

      .result-message {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 24px;
      }

      .result-btn {
        width: 100%;
      }
    }
  }

  // 移动端样式
  @media screen and (max-width: 767px) {
    .card-info {
      .balance-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  // 平板模式样式
  @media screen and (min-width: 768px) {
    .action-buttons {
      width: 85%;
      max-width: 960px;
      margin: 0 auto;
    }
  }
}
</style>

<style>
/* 全局样式 */
.gas-refund-page .van-field,
.gas-refund-page .van-cell {
  background-color: #f9fafb !important;
}

:deep(.van-field__control) {
  text-align: left !important;
}

:deep(.van-field) {
  background-color: #f9fafb;
  border-radius: 4px;
  padding: 2px 8px !important;
  margin-bottom: 4px;
}

:deep(textarea.van-field__control) {
  text-align: left;
  padding: 8px !important;
}

.gas-refund-page :deep(.remarks-field) {
  .van-field__control {
    text-align: left !important;
  }
}
</style>
