<script setup lang="ts">
import type { PickerOption } from 'vant'
import type { FileItem } from '@/components/common/FileUploader.vue'
import type { CardUser, TransferSaveParam } from '@/services/cardService/types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import { showConfirmDialog, showToast } from 'vant'
import { computed, defineEmits, reactive, ref } from 'vue'
import {
  FileUploader,
  GridFileUploader,
  InfoCard,
  PaymentMethodSelectorCard,
  ReceiptModal,
} from '@/components/common'
import { transferSave } from '@/services/api/business'
import useBusinessStore from '@/stores/modules/business'
import 'vant/es/toast/style'
import 'vant/es/dialog/style'

const emit = defineEmits(['closeOperation', 'complete'])
// 用户信息（实际项目中应从状态管理或API获取）
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
console.log('用户信息', userInfo.value)

// 过户表单数据
interface FormData {
  newOwnerName: string
  newOwnerPhone: string
  backupPhone: string
  newResidents: number
  idType: string
  idNumber: string
  contractNumber: string
  transferFee: string
  receiptType: string
  paymentMethod: string
  needInvoice: boolean
  remarks: string
  invoiceInfo?: {
    taxpayerId: string
    invoiceName: string
    addressPhone: string
    bankAccount: string
    email: string
  }
}

const formData = reactive<FormData>({
  newOwnerName: '',
  newOwnerPhone: '',
  backupPhone: '',
  newResidents: 4,
  idType: '',
  idNumber: '',
  contractNumber: '',
  transferFee: '',
  receiptType: '',
  paymentMethod: '',
  needInvoice: false,
  remarks: '',
  invoiceInfo: {
    taxpayerId: '',
    invoiceName: '',
    addressPhone: '',
    bankAccount: '',
    email: '',
  },
})

// 配置选项接口
interface ConfigOptions {
  hasPrint: boolean
  floor: boolean
  printType: string
  allowedMethods: string[] | null
  payment: string
  printLogic: string
  fileTypes?: {
    userType: string
    picMinNum: number
    description?: string
  }[]
}

// 配置对象
const config = reactive<ConfigOptions>({
  hasPrint: false,
  floor: true,
  printType: '普通收据',
  allowedMethods: null,
  payment: '现金缴费',
  printLogic: '',
  fileTypes: [],
})

// 证件类型选项
const idTypeColumns = ref<PickerOption[]>([
  { text: '身份证', value: '身份证' },
  { text: '军官证', value: '军官证' },
  { text: '营业执照', value: '营业执照' },
])

// 收据打印选项
const receiptTypeColumns = ref<PickerOption[]>([
  { text: '普通票据', value: '普通票据' },
  { text: '电子票据', value: '电子票据' },
])

// 控制选择器显示
const showIdTypePicker = ref(false)
const showReceiptTypePicker = ref(false)

const uploadedFiles = ref<FileItem[]>([])

// 子组件的响应组件引用
const printRef = ref()
const gridFileUploaderRef = ref()

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// 选择证件类型确认
function onIdTypeConfirm(event: any) {
  if (event && event.selectedOptions && event.selectedOptions.length > 0) {
    formData.idType = event.selectedOptions[0].value
    showIdTypePicker.value = false
    showToast(`已选择：${event.selectedOptions[0].text}`)
    updateIdNumberValidation()
  }
}

// 选择收据类型确认
function onReceiptTypeConfirm(event: any) {
  if (event && event.selectedOptions && event.selectedOptions.length > 0) {
    formData.receiptType = event.selectedOptions[0].value
    showReceiptTypePicker.value = false
    showToast(`已选择：${event.selectedOptions[0].text}`)
  }
}

// 更新证件号码验证规则
function updateIdNumberValidation() {
  // 在实际项目中，这里需要更新表单验证规则
  console.log('更新证件号码验证规则:', formData.idType)
}

// 计算显示文本
const idTypeDisplayText = computed(() => {
  switch (formData.idType) {
    case '身份证': return '身份证'
    case '军官证': return '军官证'
    case '营业执照': return '营业执照'
    default: return ''
  }
})

const receiptTypeDisplayText = computed(() => {
  switch (formData.receiptType) {
    case '电子票据': return '电子票据'
    case '普通票据': return '普通票据'
    default: return ''
  }
})

// 切换发票信息显示/隐藏
function toggleInvoiceInfo() {
  console.log('切换发票信息显示:', formData.needInvoice)
}

// 取消操作
function cancelOperation() {
  // 通知父页面关闭操作界面
  // 如果在实际环境中，还可能需要调用父组件中的方法
  showConfirmDialog({
    title: '提示',
    message: '确定取消过户操作吗？',
  }).then(() => {
    // 确认取消
    emit('closeOperation')
    showToast('已取消过户')
  }).catch(() => {
    // 返回继续操作
  })
}

// 文件上传相关处理
function handleFileAdded() {
}

function handleFileRemoved() {
}

// 验证表单
async function validateForm() {
  if (!formData.newOwnerName) {
    showToast('请输入新户主姓名')
    return false
  }

  if (!formData.newOwnerPhone) {
    showToast('请输入新户主电话')
    return false
  }

  // 手机号码格式验证
  if (!/^1[3-9]\d{9}$/.test(formData.newOwnerPhone)) {
    showToast('请输入正确的手机号码')
    return false
  }

  if (!formData.idType) {
    showToast('请选择证件类型')
    return false
  }

  if (!formData.idNumber) {
    // 军官证不需要必填校验
    if (formData.idType !== '军官证') {
      showToast('请输入证件号码')
      return false
    }
  }

  // 身份证号码格式验证
  if (formData.idType === '身份证' && formData.idNumber) {
    const idCardReg = /^\d{15}$|^\d{18}$|^\d{17}[\dX]$/i
    if (!idCardReg.test(formData.idNumber)) {
      showToast('请输入正确的身份证号码')
      return false
    }
  }

  if (!formData.transferFee || Number.parseFloat(formData.transferFee) < 0) {
    showToast('请输入有效的过户费用')
    return false
  }

  if (!formData.receiptType) {
    showToast('请选择打印格式')
    return false
  }

  if (!formData.paymentMethod) {
    showToast('请选择支付方式')
    return false
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return false
    }
  }

  return true
}

// 提交表单
async function submitForm() {
  const onsubmit = await validateForm()
  if (!onsubmit) {
    return
  }

  // 调用Api
  try {
    console.log('正在进行过户操作...')

    // 准备过户参数
    const param: TransferSaveParam & { files?: string[] } = {
      f_bill_style: formData.receiptType,
      f_comments: formData.remarks,
      f_new_people_num: formData.newResidents,
      f_newaddress_phone: formData.invoiceInfo.addressPhone,
      f_newcredentials: formData.idType,
      f_newemail: formData.invoiceInfo.email,
      f_newidnumber: formData.idNumber,
      f_newpaper_account: formData.invoiceInfo.bankAccount,
      f_newpaper_name: formData.invoiceInfo.invoiceName,
      f_newrent_phone: formData.backupPhone,
      f_newtaxpayer_id: formData.invoiceInfo.taxpayerId,
      f_newuser_name: formData.newOwnerName,
      f_newuser_phone: formData.newOwnerPhone,
      f_payment: formData.paymentMethod,
      f_transfer_fees: formData.transferFee,
      f_contract_id: formData.contractNumber,
    }

    // 处理文件上传
    if (uploadedFiles.value && uploadedFiles.value.length > 0) {
      param.files = uploadedFiles.value.map(file => file.result?.id).filter(Boolean)
    }

    console.log('过户参数', param)
    // 调用过户服务
    const result = await transferSave(param, userInfo.value as unknown as CardUser) as { value: string | number }
    console.log('过户操作成功', result)
    // 生成小票信息
    if (config.hasPrint) {
      await printRef.value.printParams(config.printLogic, result.value)
    }
  }
  catch (error) {
    console.error('过户操作失败', error)
    showToast({
      type: 'fail',
      message: `过户失败：${error.message || error.msg || '未知错误'}`,
    })
    throw error
  }
}

// 初始化
onMounted(async () => {
  console.log('用户信息:', userInfo.value)
  try {
    // 获取配置
    const transferConfig = await getConfigByNameAsync('mobile_transferSaveConfig')

    if (transferConfig) {
      // 更新配置
      Object.assign(config, transferConfig)

      // 更新表单默认值
      formData.receiptType = config.printType
      formData.paymentMethod = config.payment
    }
  }
  catch (error) {
    console.error('初始化过户配置失败', error)
  }
})
</script>

<template>
  <div class="ownership-transfer-form">
    <form class="ownership-transfer-form__container" @submit.prevent="submitForm">
      <!-- 顶部信息提示卡片 -->
      <InfoCard
        type="info"
        main-text="用户过户 - 请填写新户主信息"
        :sub-text="`当前用户: ${userInfo.f_user_name}`"
        class="ownership-transfer-form__section-margin"
      />

      <!-- 新户主基本信息区域 -->
      <CardContainer class="ownership-transfer-form__section-margin">
        <CardHeader title="新户主基本信息" />

        <div class="ownership-transfer-form__field">
          <label class="ownership-transfer-form__label">新姓名<span class="text-red-500">*</span></label>
          <div class="ownership-transfer-form__input-wrapper">
            <van-field
              v-model="formData.newOwnerName"
              placeholder="请输入新户主姓名"
              input-align="right"
            />
          </div>
        </div>

        <div class="ownership-transfer-form__field">
          <label class="ownership-transfer-form__label">新电话<span class="text-red-500">*</span></label>
          <div class="ownership-transfer-form__input-wrapper">
            <van-field
              v-model="formData.newOwnerPhone"
              type="tel"
              placeholder="请输入新户主电话"
              maxlength="11"
              input-align="right"
            />
          </div>
        </div>

        <div class="ownership-transfer-form__field">
          <label class="ownership-transfer-form__label">备用电话</label>
          <div class="ownership-transfer-form__input-wrapper">
            <van-field
              v-model="formData.backupPhone"
              type="tel"
              placeholder="请输入备用电话"
              maxlength="11"
              input-align="right"
            />
          </div>
        </div>

        <div class="ownership-transfer-form__field">
          <label class="ownership-transfer-form__label">新人口数</label>
          <div class="ownership-transfer-form__input-wrapper">
            <van-field
              v-model="formData.newResidents"
              type="digit"
              placeholder="请输入人口数"
              input-align="right"
            />
          </div>
        </div>
      </CardContainer>

      <!-- 证件信息区域 -->
      <CardContainer class="ownership-transfer-form__section-margin">
        <CardHeader title="证件信息" />

        <div class="ownership-transfer-form__field">
          <label class="ownership-transfer-form__label">证件类型<span class="text-red-500">*</span></label>
          <div class="ownership-transfer-form__input-wrapper">
            <van-field
              readonly
              :model-value="idTypeDisplayText"
              placeholder="请选择证件类型"
              input-align="right"
              @click="showIdTypePicker = true"
            >
              <template #right-icon>
                <van-icon name="arrow" class="ownership-transfer-form__field-arrow" />
              </template>
            </van-field>
          </div>
        </div>

        <div class="ownership-transfer-form__field">
          <label class="ownership-transfer-form__label">证件号码<span v-if="formData.idType !== '军官证'" class="text-red-500">*</span></label>
          <div class="ownership-transfer-form__input-wrapper">
            <van-field
              v-model="formData.idNumber"
              placeholder="请输入证件号码"
              input-align="right"
            />
          </div>
        </div>

        <div class="ownership-transfer-form__field">
          <label class="ownership-transfer-form__label">新合同编号</label>
          <div class="ownership-transfer-form__input-wrapper">
            <van-field
              v-model="formData.contractNumber"
              placeholder="请输入新合同编号"
              input-align="right"
            />
          </div>
        </div>
      </CardContainer>

      <!-- 费用信息区域 -->
      <CardContainer class="ownership-transfer-form__section-margin">
        <CardHeader title="费用信息" />

        <div class="ownership-transfer-form__field">
          <label class="ownership-transfer-form__label">过户费(元)<span class="text-red-500">*</span></label>
          <div class="ownership-transfer-form__field-with-unit">
            <van-field
              v-model="formData.transferFee"
              type="number"
              placeholder="0.00"
              input-align="right"
            />
          </div>
        </div>

        <div class="ownership-transfer-form__field">
          <label class="ownership-transfer-form__label">打印格式<span class="text-red-500">*</span></label>
          <div class="ownership-transfer-form__input-wrapper">
            <van-field
              readonly
              :model-value="receiptTypeDisplayText"
              placeholder="请选择打印格式"
              input-align="right"
              @click="showReceiptTypePicker = true"
            >
              <template #right-icon>
                <van-icon name="arrow" class="ownership-transfer-form__field-arrow" />
              </template>
            </van-field>
          </div>
        </div>

        <!-- 支付方式 -->
        <PaymentMethodSelectorCard
          v-model="formData.paymentMethod"
          title="支付方式"
          :allowed-methods="config.allowedMethods"
          class="over-usage-charge-form__section-margin"
        />
      </CardContainer>

      <!-- 票据信息区域（选填） -->
      <CardContainer class="ownership-transfer-form__section-margin">
        <div class="ownership-transfer-form__section-header">
          <div class="ownership-transfer-form__header-with-checkbox">
            <h4 class="ownership-transfer-form__header-title">
              票据信息（选填）
            </h4>
            <div class="ownership-transfer-form__checkbox">
              <van-checkbox v-model="formData.needInvoice" shape="square" class="ownership-transfer-form__checkbox-control" @change="toggleInvoiceInfo">
                <span class="ownership-transfer-form__checkbox-text">需要开票</span>
              </van-checkbox>
            </div>
          </div>
          <div class="ownership-transfer-form__divider" />
        </div>

        <template v-if="formData.needInvoice">
          <div class="ownership-transfer-form__field">
            <label class="ownership-transfer-form__label">纳税人识别号</label>
            <div class="ownership-transfer-form__input-wrapper">
              <van-field
                v-model="formData.invoiceInfo.taxpayerId"
                placeholder="请输入纳税人识别号"
                input-align="right"
              />
            </div>
          </div>

          <div class="ownership-transfer-form__field">
            <label class="ownership-transfer-form__label">开票名称</label>
            <div class="ownership-transfer-form__input-wrapper">
              <van-field
                v-model="formData.invoiceInfo.invoiceName"
                placeholder="请输入开票名称"
                input-align="right"
              />
            </div>
          </div>

          <div class="ownership-transfer-form__field">
            <label class="ownership-transfer-form__label">地址电话</label>
            <div class="ownership-transfer-form__input-wrapper">
              <van-field
                v-model="formData.invoiceInfo.addressPhone"
                placeholder="请输入地址电话"
                input-align="right"
              />
            </div>
          </div>

          <div class="ownership-transfer-form__field">
            <label class="ownership-transfer-form__label">开户行及账号</label>
            <div class="ownership-transfer-form__input-wrapper">
              <van-field
                v-model="formData.invoiceInfo.bankAccount"
                placeholder="请输入开户行及账号"
                input-align="right"
              />
            </div>
          </div>

          <div class="ownership-transfer-form__field">
            <label class="ownership-transfer-form__label">推送票据邮箱</label>
            <div class="ownership-transfer-form__input-wrapper">
              <van-field
                v-model="formData.invoiceInfo.email"
                type="email"
                placeholder="请输入邮箱"
                input-align="right"
              />
            </div>
          </div>
        </template>
        <template v-else>
          <div class="ownership-transfer-form__empty-placeholder">
            <p class="ownership-transfer-form__empty-text">
              勾选"需要开票"选项可以填写发票信息
            </p>
          </div>
        </template>
      </CardContainer>

      <!-- 备注区域 -->
      <CardContainer class="ownership-transfer-form__section-margin">
        <CardHeader title="备注" />
        <div class="ownership-transfer-form__field">
          <van-field
            v-model="formData.remarks"
            rows="3"
            autosize
            type="textarea"
            maxlength="200"
            placeholder="可填写其他需要说明的信息"
            show-word-limit
          />
        </div>
      </CardContainer>

      <!-- 上传附件区域 -->
      <!-- 宫格上传组件（多种文件类型时使用） -->
      <GridFileUploader
        v-if="useGridUploader"
        ref="gridFileUploaderRef"
        v-model:file-list="uploadedFiles"
        :file-types="config.fileTypes"
        title="上传附件"
        class="ownership-transfer-form__section-margin"
      />
      <CardContainer
        v-else
        class="ownership-transfer-form__section-margin"
      >
        <!-- 普通上传组件（单一文件类型或无配置时使用） -->
        <FileUploader
          v-model:file-list="uploadedFiles"
          title="上传附件"
          accept="image/png,image/jpeg,application/pdf"
          :multiple="true"
          :max-size="10 * 1024 * 1024"
          :allowed-types="['image/png', 'image/jpeg', 'application/pdf']"
          :user-type="config.fileTypes && config.fileTypes.length > 0 ? config.fileTypes[0].userType : '过户'"
          @file-added="handleFileAdded"
          @file-removed="handleFileRemoved"
        />
      </CardContainer>

      <!-- 底部操作区域 -->
      <div class="ownership-transfer-form__buttons">
        <van-button plain type="default" class="ownership-transfer-form__cancel-btn" @click="cancelOperation">
          取消
        </van-button>
        <van-button type="primary" native-type="submit" class="ownership-transfer-form__confirm-btn">
          确认过户
        </van-button>
      </div>
    </form>

    <!-- 选择器弹出层 -->
    <van-popup
      v-model:show="showIdTypePicker"
      round
      position="bottom"
      teleport="body"
      :overlay="true"
      :close-on-click-overlay="false"
      z-index="2000"
    >
      <van-picker
        title="选择证件类型"
        :columns="idTypeColumns"
        show-toolbar
        @confirm="onIdTypeConfirm"
        @cancel="showIdTypePicker = false"
      />
    </van-popup>

    <van-popup
      v-model:show="showReceiptTypePicker"
      round
      position="bottom"
      teleport="body"
      :overlay="true"
      :close-on-click-overlay="false"
      z-index="2000"
    >
      <van-picker
        title="选择打印格式"
        :columns="receiptTypeColumns"
        show-toolbar
        @confirm="onReceiptTypeConfirm"
        @cancel="showReceiptTypePicker = false"
      />
    </van-popup>

    <!-- 电子小票弹窗 -->
    <ReceiptModal ref="printRef" />
  </div>
</template>

<style lang="less" scoped>
.ownership-transfer-form {
  &__container {
    margin-bottom: 16px;
  }

  &__field {
    margin-bottom: 16px;
  }

  &__label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 4px;
  }

  &__input-wrapper {
    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__field-with-unit {
    position: relative;

    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__unit {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 14px;
    pointer-events: none;
  }

  &__field-arrow {
    color: #6b7280;
    font-size: 14px;
  }

  &__section-header {
    margin-bottom: 16px;
  }

  &__header-with-checkbox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  &__header-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  &__divider {
    height: 1px;
    background-color: #f0f0f0;
    width: 100%;
  }

  &__checkbox {
    margin-right: 16px;
    display: flex;
    align-items: center;
    height: 24px;

    &-control {
      :deep(.van-checkbox__icon) {
        font-size: 16px;
        margin-top: -1px;
      }

      :deep(.van-icon) {
        transform: scale(1);
      }

      :deep(.van-checkbox__label) {
        line-height: 20px;
      }
    }

    &-text {
      font-size: 14px;
      font-weight: normal;
      color: #1f2937;
    }
  }

  &__empty-placeholder {
    padding: 20px 0;
    text-align: center;
  }

  &__empty-text {
    color: #6b7280;
    font-size: 14px;
  }

  &__total-content {
    width: 100%;
  }

  &__total-label {
    font-size: 16px;
    font-weight: 500;
    color: #4b5563;
    margin: 0 0 4px 0;
  }

  &__total-value {
    font-size: 20px;
    font-weight: 600;
    color: #2563eb;
  }

  &__total-sub {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
  }

  &__buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  &__cancel-btn {
    border-color: #d1d5db;
    color: #4b5563;
  }

  &__confirm-btn {
    background-color: #2563eb;
  }

  &__section-margin {
    margin-bottom: 16px;
  }

  &__summary {
    background: linear-gradient(to right, #ebf4ff, #e0f2fe);
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  &__explanation {
    background-color: #f0f7ff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
  }

  &__explanation-title {
    color: #003366;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
  }

  &__explanation-list {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      margin-bottom: 6px;
      line-height: 1.4;
      font-size: 14px;
      color: #2196f3;
      position: relative;
      padding-left: 16px;
      display: flex;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: #2563eb;
        border-radius: 50%;
        margin-right: 10px;
        flex-shrink: 0;
        margin-top: 6px;
      }
    }
  }

  &__payment-selector {
    :deep(.payment-method-selector__header) {
      display: none;
    }
  }
}

// 小票相关样式
.receipt-content {
  padding: 0 12px;
}

.receipt-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.receipt-label {
  color: #64748b;
  font-size: 14px;
}

.receipt-value {
  color: #1e293b;
  font-size: 14px;
  font-weight: 400;
}

.receipt-divider {
  height: 1px;
  background-color: #e2e8f0;
  width: 100%;
}

.receipt-qrcode {
  margin-top: 16px;
}

.qrcode-container {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  margin: 0 auto;
}

// 工具类
.text-red-500 {
  color: #ef4444;
}

.mt-4 {
  margin-top: 16px;
}

.pt-4 {
  padding-top: 16px;
}

.border-t {
  border-top-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.border-dashed {
  border-style: dashed;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-600 {
  color: #4b5563;
}

.text-5xl {
  font-size: 13px;
}

.text-center {
  text-align: center;
}

.break-words {
  word-break: break-word;
}

.font-medium {
  font-weight: 500;
}

.my-3 {
  margin-top: 12px;
  margin-bottom: 12px;
}

.mb-0 {
  margin-bottom: 0;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}
</style>
