<script setup lang="ts">
import type { MeterReadingUser } from '@/components/MeterReading'
import type { MeterReadingBookOption, MeterReadingFilterOptions } from '@/components/MeterReading/MeterReadingFilter.vue'
import { MeterReadingCard, MeterReadingFilter, MeterReadingSearch } from '@/components/MeterReading'
import { getMeterBookList, meteReadingHandSubmit, onlineMeteReadingSearchUser } from '@/services/api/business'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { onMounted, ref, watch } from 'vue'

// 页面状态
const searchQuery = ref('')
const isFilterOpen = ref(false)
const filterOptions = ref<MeterReadingFilterOptions>({
  status: 'all',
  bookNumber: 'all',
  sortBy: 'f_userinfo_code',
  sortOrder: 'desc',
})

// 表册列表数据
const meterBookList = ref<MeterReadingBookOption[]>([])
const bookListLoading = ref(false)

// 用户数据
const users = ref<MeterReadingUser[]>([])
const loading = ref(false)
// 当前用户
const currUser = useUserStore().getLogin().f

// 下拉刷新和上拉加载状态
const refreshing = ref(false)
const finished = ref(false)
const pageInfo = ref({
  pageNo: 1,
  pageSize: 5,
  total: 0,
})
// 添加一个标志来防止重复查询
const isSubmitting = ref(false)

// 获取表册列表
async function fetchMeterBookList() {
  bookListLoading.value = true
  try {
    const response = await getMeterBookList()
    if (response) {
      // 转换API返回的数据为筛选组件需要的格式
      meterBookList.value = response.map((item: any) => ({
        value: item.value,
        label: item.label,
      }))
    }
  }
  catch (error) {
    console.error('获取表册列表失败:', error)
  }
  finally {
    bookListLoading.value = false
  }
}

// 搜索用户
async function searchUsers(isLoadMore = false, isRefresh = false) {
  // 不再检查是否有搜索条件，允许查询所有数据
  // 如果是下拉刷新，重置页码
  if (isRefresh) {
    pageInfo.value.pageNo = 1
    finished.value = false
  }

  // 如果不是下拉刷新或加载更多，则显示加载提示
  if (!isRefresh && !isLoadMore) {
    loading.value = true
    showLoadingToast({
      message: '正在搜索...',
      forbidClick: true,
    })
  }

  try {
    // 构建查询参数JSON对象
    const queryParams = {
      keyword: '',
      bookNumber: '',
    }
    // 只有当有搜索关键词时才添加关键词参数
    if (searchQuery.value.trim()) {
      queryParams.keyword = searchQuery.value.trim()
    }

    // 只有当选择了非"全部"的册号时才添加册号参数
    if (filterOptions.value.bookNumber !== 'all' && filterOptions.value.bookNumber !== '') {
      queryParams.bookNumber = filterOptions.value.bookNumber
    }

    // 构建完整的参数对象，包含分页信息、查询参数JSON和排序参数
    const params = {
      pageNo: pageInfo.value.pageNo,
      pageSize: pageInfo.value.pageSize,
      queryParams,
      sortBy: filterOptions.value.sortBy,
      sortOrder: filterOptions.value.sortOrder,
    }

    const result = await onlineMeteReadingSearchUser(params)

    if (!isRefresh && !isLoadMore) {
      closeToast()
    }

    if (result) {
      // 转换API返回的数据格式为组件需要的格式
      const newUsers = (result || []).map((item: any) => ({
        f_userinfo_id: item.f_userinfo_id,
        f_userfiles_id: item.f_userfiles_id,
        f_user_id: item.f_user_id,
        f_userinfo_code: item.f_userinfo_code,
        f_user_name: item.f_user_name,
        f_address: item.f_address,
        f_meternumber: item.f_meternumber,
        f_last_input_date: item.f_last_input_date,
        f_last_tablebase: item.f_last_tablebase,
        monthlyAverage: item.monthlyAverage,
        f_book_name: item.f_book_name,
        f_meter_book_sort: item.f_meter_book_sort,
        f_meter_book_num: item.mbid,
        f_arrears_fees: item.f_arrears_fees,
        f_tablebase: item.f_tablebase,
        readingStatus: item.readingStatus,
        fileList: [],
        remark: '',
        showMoreOptions: false,
        readingError: '',
        f_meter_type: item.f_meter_classify,
        f_meter_brand: item.f_meter_brand,
        f_meter_style: item.f_meter_style,
        f_user_type: item.f_user_type,
        f_stairprice_id: item.f_stairprice_id,
        f_gasproperties: item.f_gasproperties,
        f_residential_area: item.f_residential_area,
        f_price_id: item.f_price_id,
        f_balance: item.f_balance,
        f_user_level: item.f_user_level,
        f_total_usegas_amount: item.f_total_usegas_amount,
      }))

      // 如果是刷新，替换全部数据
      if (isRefresh) {
        users.value = newUsers
      }
      // 如果是加载更多，追加数据
      else if (isLoadMore) {
        users.value = [...users.value, ...newUsers]
      }
      // 否则是常规搜索，替换全部数据
      else {
        users.value = newUsers
      }

      // 第一次加载且无数据时提示
      if (!isLoadMore && !isRefresh && users.value.length === 0) {
        showToast('未找到符合条件的用户')
      }

      // 如果返回的数据条数小于请求的pageSize，说明已经没有更多数据了
      if (newUsers.length < pageInfo.value.pageSize) {
        finished.value = true
      }
    }
    else {
      if (!isLoadMore && !isRefresh) {
        showToast(result?.message || '搜索失败')
        users.value = []
      }
    }
  }
  catch (error) {
    if (!isRefresh && !isLoadMore) {
      closeToast()
      showToast('搜索出错，请稍后重试')
      console.error('搜索用户失败:', error)
      users.value = []
    }
    finished.value = true
  }
  finally {
    loading.value = false
    refreshing.value = false
    // 如果是刷新或常规搜索（非加载更多），则滚动到顶部
    if (!isLoadMore) {
      // 使用setTimeout确保DOM更新后再滚动
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      }, 100)
    }
  }
}

// 处理下拉刷新
function onRefresh() {
  pageInfo.value.pageNo = 1
  refreshing.value = true
  finished.value = false
  searchUsers(false, true)
}

// 处理上拉加载更多
async function onLoadMore() {
  // 如果已完成加载或正在提交抄表，则不触发加载更多
  if (finished.value || isSubmitting.value)
    return

  // 记录当前数据条数
  const currentCount = users.value.length

  // 增加页码
  pageInfo.value.pageNo++

  // 加载更多数据
  await searchUsers(true, false)

  // 如果加载后数据条数没有变化，说明没有更多数据了
  if (users.value.length === currentCount) {
    finished.value = true
    refreshing.value = false
    showToast('没有更多数据了')
  }
}

// 切换筛选面板
function toggleFilter() {
  isFilterOpen.value = !isFilterOpen.value
}

// 处理搜索操作
function handleSearch() {
  // 如果正在查询，则不触发新的查询
  if (isSubmitting.value)
    return

  // 设置标志，防止重复查询
  isSubmitting.value = true

  try {
    pageInfo.value.pageNo = 1
    finished.value = false
    if (isFilterOpen.value) {
      isFilterOpen.value = false
    }
    searchUsers()
  }
  finally {
    // 延迟重置标志，给足够时间完成查询
    setTimeout(() => {
      isSubmitting.value = false
    }, 500)
  }
}

// 监听筛选条件变化，重新搜索
watch(filterOptions, () => {
  // 如果正在提交，则不触发查询
  if (users.value.length > 0 && !isSubmitting.value) {
    pageInfo.value.pageNo = 1
    finished.value = false
    searchUsers()
  }
}, { deep: true })

// 提交抄表记录
async function submitReading(user: MeterReadingUser) {
  showLoadingToast({
    message: '提交中...',
    forbidClick: true,
  })

  // 设置标志，防止重复查询
  isSubmitting.value = true

  try {
    // 构建提交参数
    const params = {
      model: user,
      // 组织操作人信息
      operator: {
        orgId: currUser.resources.orgid,
        orgName: currUser.resources.orgs,
        depId: currUser.resources.depids,
        depName: currUser.resources.deps,
        operator: currUser.resources.name,
        operatorId: currUser.resources.id,
      },
    }

    const response = await meteReadingHandSubmit(params)
    closeToast()

    if (response.id) {
      showToast('抄表成功')
      pageInfo.value.pageNo = 1
      await searchUsers(false, true)

      // 滚动到对应的用户卡片位置
      setTimeout(() => {
        const userCard = document.querySelector(`[data-user-id="${user.f_userinfo_id}"]`)
        if (userCard) {
          userCard.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }, 300)
    }
    else {
      showToast(response.message || '提交失败')
    }
  }
  catch (error) {
    closeToast()
    showToast('提交出错，请稍后重试')
    console.error('提交抄表记录失败:', error)
  }
  finally {
    // 重置标志
    isSubmitting.value = false
  }
}

// 更新用户数据
function updateUser(user: MeterReadingUser) {
  const index = users.value.findIndex(u => u.f_userinfo_id === user.f_userinfo_id)
  if (index !== -1) {
    users.value[index] = { ...user }
  }
}

// 页面加载时获取表册列表和初始数据
onMounted(() => {
  // 确保页码从1开始
  pageInfo.value.pageNo = 1
  finished.value = false

  // 获取表册列表
  fetchMeterBookList()

  // 加载第一页数据
  searchUsers(false, true)
})
</script>

<template>
  <div class="meter-reading-page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="在线抄表"
      left-arrow
      placeholder
      fixed
      @click-left="$router.back()"
    />

    <!-- 搜索栏 -->
    <MeterReadingSearch
      v-model="searchQuery"
      @filter="toggleFilter"
      @update:model-value="searchQuery = $event"
      @search="handleSearch"
    />

    <!-- 筛选面板 -->
    <MeterReadingFilter
      v-model:filters="filterOptions"
      v-model:is-open="isFilterOpen"
      :book-options="meterBookList"
      :loading="bookListLoading"
    />

    <!-- 主内容区域 -->
    <div class="content-container">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="refreshing"
          :finished="finished"
          finished-text="没有更多数据了"
          :immediate-check="false"
          @load="onLoadMore"
        >
          <div class="card-list">
            <!-- 加载状态 -->
            <van-loading v-if="loading" class="loading-indicator" type="spinner" color="#1989fa" size="24px">
              正在搜索...
            </van-loading>

            <!-- 无数据提示 -->
            <template v-else-if="users.length === 0">
              <div class="search-hint">
                <van-empty description="点击搜索按钮查询用户数据" image="search" />
              </div>
            </template>

            <template v-else>
              <!-- 数据列表 -->
              <template v-if="users.length > 0">
                <div
                  v-for="user in users"
                  :key="user.f_userinfo_id"
                  class="card-item"
                >
                  <MeterReadingCard
                    :user="user"
                    @update:user="updateUser"
                    @submit="submitReading"
                  />
                </div>
              </template>

              <!-- 筛选后无数据 -->
              <template v-else>
                <van-empty description="暂无符合条件的抄表数据" />
              </template>
            </template>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<style lang="less" scoped>
.meter-reading-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 20px;

  .content-container {
    padding-top: 50px;
  }

  .card-list {
    padding: 12px;
    max-width: 750px;
    margin: 0 auto;
    min-height: calc(100vh - 104px);

    .card-item {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .loading-indicator {
      display: flex;
      justify-content: center;
      margin: 30px 0;
    }

    .search-hint {
      margin-top: 20px; /* 将原来的60px减小为20px */
    }

    /* 以下样式可以保留，虽然已经不再使用加载更多按钮 */
    .load-more {
      display: flex;
      justify-content: center;
      margin: 20px 0;
    }

    .load-finished {
      margin: 16px 0;
      color: #969799;
      font-size: 14px;
    }
  }
}
</style>
