<!-- 咸阳特殊业务 换主板 -->
<script setup lang="ts">
import MeterReplaceMent from '@/views/meterReplaceMent/meterReplaceMent.vue'

const emit = defineEmits<{
  (e: 'complete', status: boolean, changeMeter: boolean): void
}>()

function handleComplete(status: boolean, changeMeter: boolean) {
  emit('complete', status, changeMeter)
}
</script>

<template>
  <MeterReplaceMent type="换主板" @complete="handleComplete" />
</template>
