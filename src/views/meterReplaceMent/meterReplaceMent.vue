<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import type { MeterReplacementFormData } from '@/components/meterReplacement/types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { getConfigByNameAsync } from 'af-mobile-client-vue3/src/services/api/common'
import { showFailToast, showSuccessToast, showToast } from 'vant'
import { computed, onMounted, provide, reactive, ref } from 'vue'
import { CodePayment, FileUploader, GridFileUploader, ReceiptModal } from '@/components/common'
import ExtendedInfo from '@/components/meterReplacement/ExtendedInfo.vue'
import GasSupplementInfo from '@/components/meterReplacement/GasSupplementInfo.vue'
import NewMeterForm from '@/components/meterReplacement/NewMeterForm.vue'
import NewMeterFormXy from '@/components/meterReplacement/NewMeterFormXy.vue'
import OldMeterInfo from '@/components/meterReplacement/OldMeterInfo.vue'
import { commonCal } from '@/services/api/business'

import useBusinessStore from '@/stores/modules/business'

const props = defineProps({
  type: {
    type: String,
    default: '换新表',
  },
})

const emit = defineEmits(['closeOperation', 'complete', 'update:modelValue'])

const newMeterFormRef = ref(null)

const fileUploaderRef = ref(null)
const gridFileUploaderRef = ref(null)

const fileList = ref<FileItem[]>([])

const loading = ref(false)

// 添加配置参数和欠费相关属性
let config = reactive({
  hasArrearsChange: false, // 有欠费是否能换表
  allowedMethods: null,
  // 是否展示抄表册选择
  showMeterBook: true,
  // 是否展示气价选择
  showGasPrice: true,
  // 是否展示阶梯用量选择
  showSetGasSync: true,
  changeMeterFeeList: [
    0,
    398,
  ],
  $globalProp: {
    tenantAlias: 'standard',
  },
  // 最小附件数量 （后期新表旧表照片可能单独字段 目前存储在filelist中无法区分 暂时这么判断）
  minFileCount: 0,
  // 是否默认关闭扩展信息
  extInfoStatus: true,
  // 文件上传类型配置
  fileTypes: [],
})

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// 子组件的响应组件引用
const printRef = ref()

// 当前用户
const currUser = useUserStore().getLogin().f

// 用户信息对象
const userInfo = useBusinessStore().currentUser

const showQrCodePayment = ref(false)

// 表单数据
const formData = reactive<MeterReplacementFormData>({
  f_imei: '',
  f_using_base_old: null,
  f_meter_type: '',
  f_meter_brand: '',
  f_meter_model: '',
  f_meter_base: null,
  f_meternumber: '',
  brandid: '',
  modelid: '',
  f_alias: '',
  f_type: '换新表',
  f_stair_use: null,
  f_ladder_sync: true,
  f_remanent_gas: 0,
  f_remanent_price: 0,
  f_remanent_money: 0,
  f_change_operator: '',
  f_approve_operator: '',
  f_changemeter_fee: null,
  f_metertitles: '',
  f_meter_book_num: null,
  f_serial_number: '',
  f_price_id: null,
  receiptType: '',
  paymentMethod: '',
  remarks: '',
  files: [],
  f_serial_id: '',
})

// 使用provide向子组件提供formData
provide('formData', formData)

// 是否显示支付方式
const showPaymentMethod = computed(() => Number(formData.f_changemeter_fee) > 0)

// 处理取消
function handleCancel() {
  loading.value = true
  emit('complete', { status: true })
}

async function handleQrCodePaymentSuccess(paymentResult) {
  try {
    // 使用公共处理函数处理支付
    await processPayment(paymentResult.tradeNo)
    showQrCodePayment.value = false
  }
  catch (error) {
    console.error(error)

    showToast(`补卡失败${error.message || error.msg || '未知错误'}`)
  }
}

function handleQrCodePaymentCancel() {
  showQrCodePayment.value = false
}

// 抽取支付处理逻辑为单独函数
async function processPayment(tradeNo = ''): Promise<any> {
  try {
    formData.files = fileList.value?.map(item => item.result.id).filter(Boolean)
    // 如果有凭证号，则设置
    if (tradeNo) {
      formData.f_serial_id = tradeNo
    }
    // 调用换表逻辑
    const result = await runLogic<{ id: number }>('mobile_changeMeter', {
      model: formData,
      user: userInfo,
      config,
      operator: {
        f_operator: currUser.resources.name,
        f_operatorid: currUser.resources.id,
        f_orgid: currUser.resources.orgid,
        f_orgname: currUser.resources.orgs,
        f_depid: currUser.resources.depids,
        f_depname: currUser.resources.deps,
      },
    })

    if (result) {
      showSuccessToast('换表成功')
      // 显示收据
      await printRef.value.printParams('mobile_changeMeterPrintParams', result.id)
    }
    else {
      showFailToast('换表失败，请检查输入信息')
    }
  }
  catch (error) {
    console.error(error)
    showToast(`换表失败${error.message || error.msg || '未知错误'}`)
    throw error
  }
}

// 处理提交
async function handleSubmit() {
  if (!validateForm()) {
    return
  }
  loading.value = true
  try {
    // 微信/支付宝支付走单独的流程，通过事件回调处理支付结果
    if (formData.paymentMethod.includes('支付宝') || formData.paymentMethod.includes('微信')) {
      showQrCodePayment.value = true
    }
    // 其他支付方式直接调用收费接口进行处理
    else {
      // 执行换表操作
      await processPayment()
    }
  }
  catch (error) {
    loading.value = false
    showFailToast(error.msg || '提交失败')
  }
  loading.value = false
}

// 校验表单
function validateForm() {
  // 调用新表信息组建的内部校验
  // 因为咸阳和产品的校验不太一样 暂时只能这样处理
  if (newMeterFormRef.value && !newMeterFormRef.value?.validate()) {
    return false
  }

  // 补气金额 补气量不能小于0 补气单价不能小于0
  if (formData.f_remanent_money < 0 || formData.f_remanent_gas < 0 || formData.f_remanent_price < 0) {
    showFailToast('补气金额、补气量不能小于0')
    return false
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return false
    }
  }

  if (config.$globalProp.tenantAlias === 'Xianyang') {
    if (!formData.f_approve_operator) {
      showFailToast('请填写扩展信息中的审核人')
      return false
    }

    if (formData.f_changemeter_fee === null || formData.f_changemeter_fee === undefined) {
      showFailToast('请选择扩展信息中的换表费用')
      return false
    }
  }

  return true
}

// 文件上传相关
function onFileAdded(): void {
}

function onFileRemoved(): void {
}

// 组件挂载时调用初始化方法
onMounted(async () => {
  try {
    // 获取有欠费是否能换表的配置
    const configResponse = await getConfigByNameAsync('ChangeMeterConfig')
    config = Object.assign(config, configResponse)
    formData.f_type = props.type
    initMeterReplacement()
  }
  catch (error) {
    console.error('初始化失败:', error)
    showFailToast('初始化失败')
  }
})

/**
 * 初始化换表数据
 */
async function initMeterReplacement() {
  try {
    // 初始化其他状态
    formData.remarks = ''
    formData.files = []

    // 获取阶梯气量
    try {
      if (userInfo.f_price_type === '固定气价') {
        userInfo.f_stair_use = 0
      }
      else {
        const getAmount = await commonCal({
          f_userfiles_id: userInfo.f_userfiles_id,
          gas: userInfo.f_total_gas,
        })
        userInfo.f_stair_use = getAmount.sumAmount || 0
      }
      // 更新表单数据中的阶梯用量值
      formData.f_stair_use = userInfo.f_stair_use
    }
    catch (error) {
      formData.f_stair_use = 0
    }
  }
  catch (error) {
    console.error('初始化失败:', error)
    showFailToast('初始化失败')
  }
}

function uploadFile(type: string) {
  if (useGridUploader.value) {
    gridFileUploaderRef.value.triggerFileUpload(type)
  }
  else {
    fileUploaderRef.value.triggerFileInput(type)
  }
}
</script>

<template>
  <div class="mobile-view meter-replacement">
    <!-- 旧表信息组件，恒定为3列布局 -->
    <div class="info-card">
      <OldMeterInfo :info="userInfo" />
    </div>

    <!-- 换表基本信息卡片 -->
    <!-- 咸阳特殊业务 目前表单没配置暂时只能这么处理 -->
    <div v-if="config?.$globalProp?.tenantAlias === 'Xianyang'" class="info-card">
      <NewMeterFormXy ref="newMeterFormRef" :type="props.type" @meter-reading-complete="emit('complete')" @upload-file="uploadFile" />
    </div>
    <div v-else class="info-card">
      <NewMeterForm ref="newMeterFormRef" @meter-reading-complete="emit('complete')" @upload-file="uploadFile" />
    </div>

    <!-- 补气信息卡片 -->
    <div v-if="formData.gasbrand?.length > 0" class="info-card">
      <GasSupplementInfo
        :show-set-gas-sync="config.showSetGasSync"
        :tenant-alias="config?.$globalProp?.tenantAlias"
      />
    </div>

    <!-- 修改为带折叠功能的扩展信息卡片 -->
    <CardContainer class="info-card" title="扩展信息" :collapsible="true" :default-collapsed="config.extInfoStatus">
      <ExtendedInfo
        :show-meter-book="config.showMeterBook"
        :show-gas-price="config.showGasPrice"
        :config="config"
      />
    </CardContainer>

    <!-- 备注信息卡片 -->
    <ChargePrintSelectorAndRemarks
      v-model="formData.receiptType"
      :remarks="formData.remarks"
      @update:remarks="(val) => formData.remarks = val"
    />

    <!-- 支付方式组件 - 从扩展信息中提取出来 -->
    <div v-if="showPaymentMethod" class="info-card">
      <PaymentMethodSelectorCard
        v-model="formData.paymentMethod"
        title="支付方式"
        :allowed-methods="config.allowedMethods"
        class="other-charge-form__section-margin"
      />
    </div>

    <!-- 宫格上传组件（多种文件类型时使用） -->
    <GridFileUploader
      v-if="useGridUploader"
      ref="gridFileUploaderRef"
      v-model:file-list="fileList"
      :file-types="config.fileTypes"
      title="上传附件"
      class="other-charge-form__section-margin"
    />
    <CardContainer
      v-else
      class="other-charge-form__section-margin"
    >
      <!-- 普通上传组件（单一文件类型或无配置时使用） -->
      <FileUploader
        ref="fileUploaderRef"
        v-model:file-list="fileList"
        :multiple="true"
        :user-type="config.fileTypes && config.fileTypes.length > 0 ? config.fileTypes[0].userType : '换表'"
        @file-added="onFileAdded"
        @file-removed="onFileRemoved"
      />
    </CardContainer>

    <!-- 按钮区域 -->
    <div class="card-replacement-form__buttons">
      <van-button
        plain
        type="default"
        class="card-replacement-form__cancel-btn"
        :loading="loading"
        @click="handleCancel"
      >
        取消
      </van-button>
      <van-button
        type="primary"
        class="card-replacement-form__confirm-btn"
        :loading="loading"
        @click="handleSubmit"
      >
        确认提交
      </van-button>
    </div>

    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" />
    <!-- 二维码支付弹窗 -->
    <CodePayment
      v-if="showQrCodePayment"
      v-model:show="showQrCodePayment"
      :payment="formData.paymentMethod"
      :collection="formData.f_changemeter_fee || 0"
      :type="props.type"
      @payment-success="handleQrCodePaymentSuccess"
      @payment-cancel="handleQrCodePaymentCancel"
    />
  </div>
</template>

<style lang="less" scoped>
.meter-replacement {
  .content-area {
    padding: 16px;
  }

  /* 添加加载遮罩样式 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;

    .loading-text {
      margin-top: 16px;
      color: #fff;
      font-size: 16px;
    }
  }

  .bottom-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 24px;
  }

  /* 移动端样式 */
  &.mobile-view {
    .content-area {
      padding: 12px;
    }

    .info-card {
      margin-bottom: 12px;
    }

    .bottom-buttons {
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }
  }

  /* 适应不同设备的响应式样式 */
  @media screen and (max-width: 767px) {
    .content-area {
      padding: 12px;
    }

    .info-card {
      margin-bottom: 12px;
    }

    .van-cell-group {
      :deep(.van-cell) {
        padding: 10px 12px;
      }
    }
  }

  /* 平板特定样式（覆盖上面的平板和桌面端样式） */
  @media screen and (min-width: 768px) and (max-width: 1024px) {
    /* 移除这里可能有冲突的样式，使用组件内部的样式控制 */
  }

  /* 确保所有表单项的背景为白色 */
  .van-field,
  .van-cell,
  [class*='bg-'] {
    background-color: #f9fafb !important;
  }

  /* 修改Vant组件样式 */
  .van-field {
    background-color: #f9fafb;
    border-radius: 6px;
  }

  /* iPad特定样式 */
  @media screen and (min-width: 768px) {
    .info-card.upload-card {
      :deep(.file-uploader__container) {
        display: grid;
        grid-template-columns: 1fr 1fr; /* 强制使用两列布局 */
        gap: 16px;
      }

      :deep(.file-uploader__list-container) {
        grid-column: 1 / span 2; /* 文件列表占据整行 */
      }
    }
  }

  /* 移动端特定样式 */
  @media screen and (max-width: 767px) {
    .form-item {
      margin-bottom: 4px;
    }

    .van-field {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  /* 小票样式 */
  .receipt-popup {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
  }

  .receipt-preview {
    display: flex;
    flex-direction: column;
    max-height: 80vh;
  }

  .receipt-content {
    padding: 20px;
    overflow-y: auto;
  }

  .receipt-header {
    text-align: center;
    margin-bottom: 16px;
  }

  .receipt-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .receipt-subtitle {
    font-size: 14px;
    color: #666;
  }

  .receipt-info {
    .info-section {
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px dashed #eaeaea;

      &.no-border {
        border-bottom: none;
        padding-bottom: 0;
      }
    }

    .info-header {
      font-weight: 600;
      margin-bottom: 8px;
      font-size: 15px;
    }

    .info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;
      font-size: 14px;
    }

    .info-label {
      color: #666;
    }

    .info-value {
      font-weight: 500;

      &.highlight {
        color: #ff6b00;
      }
    }
  }

  .receipt-footer-text {
    text-align: center;
    margin-top: 20px;
    color: #999;
    font-size: 12px;

    p {
      margin: 4px 0;
    }
  }

  .receipt-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    padding: 16px;
    border-top: 1px solid #eaeaea;
  }

  /* 表单字段样式占位符 */
  .field-placeholder {
    .van-field__control {
      color: #999;
    }
  }

  /* 调整按钮区域样式 */
  .other-charge-form__buttons {
    display: flex;
    justify-content: flex-end; /* 按钮向右对齐 */
    gap: 16px;
    margin-top: 20px;
    margin-bottom: 40px;
  }

  .other-charge-form__cancel-btn,
  .other-charge-form__confirm-btn {
    min-width: 110px;
    height: 40px;
  }

  /* 按钮样式 */
  .card-replacement-form__buttons {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .card-replacement-form__cancel-btn {
    border-color: #d1d5db;
    color: #4b5563;
  }

  .card-replacement-form__confirm-btn {
    background-color: #2563eb;
  }

  /* 移动端样式 */
  @media screen and (max-width: 767px) {
    .content-area {
      padding: 12px;
    }

    .info-card {
      margin-bottom: 12px;
    }

    .card-replacement-form__buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  /* 平板模式样式 */
  @media screen and (min-width: 768px) {
    .card-replacement-form__buttons {
      width: 85%;
      max-width: 960px;
      margin: 0 auto;
    }
  }
}
</style>
