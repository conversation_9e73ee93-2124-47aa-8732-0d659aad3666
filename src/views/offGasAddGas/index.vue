<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { showConfirmDialog, showDialog, showFailToast, showSuccessToast, showToast } from 'vant'
import { computed, onMounted, reactive, ref } from 'vue'
import { FileUploader, GridFileUploader } from '@/components/common'
import ReceiptModal from '@/components/common/ReceiptModal.vue'
import { getLastCardCharge } from '@/services/api/business'
import { offGasAddGas } from '@/services/cardService'
import useBusinessStore from '@/stores/modules/business'
// 定义组件事件
const emit = defineEmits<{
  (e: 'complete', data?: any): void
  (e: 'closeOperation'): void
}>()

// 获取store
const businessStore = useBusinessStore()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showResult = ref(false)
const resultMessage = ref('')
const operationSuccess = ref(false)

// 子组件引用
const printRef = ref()

// 表单数据
const formData = reactive({
  remarks: '',
  files: [],
  includeInAccumulated: false, // 是否计入累购，默认为false（不计入）
})

// 获取当前用户信息
const currentUser = computed(() => businessStore.currentUser)
const currentCard = computed(() => businessStore.currentCard)
const currUser = computed(() => userStore.getLogin().f)

// 卡片信息
const cardInfo = computed(() => {
  if (!currentCard.value) {
    return {
      cardNumber: '',
      currentGas: '0.00',
      currentMoney: '0.00',
      hasGas: false,
      hasMoney: false,
    }
  }

  const gas = Number(currentCard.value.Gas || 0)
  const money = Number(currentCard.value.Money || 0)

  return {
    cardNumber: currentCard.value.CardID || '',
    currentGas: gas.toFixed(2),
    currentMoney: money.toFixed(2),
    hasGas: gas > 0,
    hasMoney: money > 0,
  }
})

// 最后一次购气记录
const lastRecord = ref(null)

const fileList = ref<FileItem[]>([])

// 配置对象
const config = reactive({
  fileTypes: [],
})

const gridFileUploaderRef = ref()

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

async function initConfig() {
  try {
    const res = await getConfigByNameAsync('mobile_offGasAddGasConfig')
    if (res) {
      Object.assign(config, res)
    }
  }
  catch (error) {
    console.error('初始化配置失败', error)
  }
}

// 获取最后一次购气记录
async function fetchLastRecord() {
  if (!currentUser.value?.f_userinfo_id)
    return

  try {
    const result = await getLastCardCharge({
      f_userinfo_id: `${currentUser.value.f_userinfo_id}`,
    })
    lastRecord.value = result
  }
  catch (error) {
    console.error('获取最后一次购气记录失败:', error)
    // 如果获取失败，使用默认值
    lastRecord.value = null
  }

  if (!lastRecord.value) {
    showDialog({
      title: '提示',
      message: '暂无可补气记录，返回操作页面',
      confirmButtonText: '返回',
      confirmButtonColor: '#ff6b6b',
    }).then(() => {
      emit('closeOperation')
    })
  }

  if (lastRecord.value?.fillgas) {
    showToast('当前IC卡关联补气记录，请谨慎核对')
  }
}

// 格式化的最后一次购气记录
const formattedLastRecord = computed(() => {
  if (!lastRecord.value) {
    return {
      date: '暂无记录',
      operator: '暂无',
      amount: '0.00',
      gas: '0.00',
      money: '0.00',
      paymentMethod: '暂无',
      fillgas: null,
    }
  }

  return {
    date: lastRecord.value.f_date || '暂无记录',
    operator: lastRecord.value.f_operator || '暂无',
    amount: lastRecord.value.f_preamount?.toFixed(2) || '0.00',
    gas: lastRecord.value.f_pregas?.toFixed(2) || '0.00',
    money: lastRecord.value.f_collection?.toFixed(2) || '0.00',
    paymentMethod: lastRecord.value.f_payment || '暂无',
    fillgas: lastRecord.value.fillgas
      ? {
          date: lastRecord.value.fillgas.f_date || '暂无记录',
          operator: lastRecord.value.fillgas.f_operator || '暂无',
          amount: lastRecord.value.fillgas.f_preamount?.toFixed(2) || '0.00',
          gas: lastRecord.value.fillgas.f_pregas?.toFixed(2) || '0.00',
        }
      : null,
  }
})

// 是否可以补气
const canFillGas = computed(() => {
  return lastRecord.value && (Number(lastRecord.value.f_pregas || 0) > 0)
})

// 初始化
onMounted(async () => {
  await initConfig()

  console.log('掉气补气组件初始化', {
    currentUser: currentUser.value,
    currentCard: currentCard.value,
    cardInfo: cardInfo.value,
  })

  // 获取最后一次购气记录
  await fetchLastRecord()
})

// 开始补气操作
async function startFillGas() {
  if (!canFillGas.value) {
    showFailToast('当前没有可补气的记录')
    return
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return
    }
  }

  try {
    // 确认补气
    let message = '确认要补充气量：'
    const totalGas = Number(lastRecord.value.f_pregas || 0) + Number(lastRecord.value.fillgas?.f_pregas || 0)
    const totalAmount = Number(lastRecord.value.f_preamount || 0) + Number(lastRecord.value.fillgas?.f_preamount || 0)

    message += `${totalGas.toFixed(2)}m³（金额：¥${totalAmount.toFixed(2)}）到IC卡上吗？`

    await showConfirmDialog({
      title: '确认补气',
      message,
      confirmButtonText: '确认补气',
      cancelButtonText: '取消',
      confirmButtonColor: '#07c160',
    })

    // 开始补气流程
    await performFillGas()
  }
  catch (error) {
    // 用户取消操作
    console.log('用户取消补气操作')
  }
}

// 执行补气操作
async function performFillGas() {
  loading.value = true
  resultMessage.value = '正在补充气量...'

  try {
    // 提取文件结果数组
    const fileResults = fileList.value.map(file => file.result?.id).filter(Boolean)

    // 计算总气量金额 收费+补气
    const f_pregas = Number(lastRecord.value.f_pregas || 0) + Number(lastRecord.value.fillgas?.f_pregas || 0)
    const f_preamount = Number(lastRecord.value.f_preamount || 0) + Number(lastRecord.value.fillgas?.f_preamount || 0)

    // 调用补气API
    const result = await offGasAddGas({
      ...lastRecord.value,
      f_pregas,
      f_preamount,
      f_comments: formData.remarks,
      f_add_gas: formData.includeInAccumulated ? '计入' : '不计入', // 是否计入累购 存储中文需要展示在前台作为条件和列表
      files: fileResults.length > 0 ? fileResults : null,
    }, currentUser.value, {
      f_operator: currUser.value.resources.name,
      f_operatorid: currUser.value.resources.id,
      f_orgid: currUser.value.resources.orgid,
      f_orgname: currUser.value.resources.orgs,
      f_depid: currUser.value.resources.depids,
      f_depname: currUser.value.resources.deps,
    })

    operationSuccess.value = true
    resultMessage.value = '补气成功，请取回IC卡。'
    showResult.value = true

    // 生成小票信息
    if (printRef.value && result && typeof result === 'object' && 'id' in result && result.id) {
      await printRef.value.printParams('mobile_fillGasPrintParams', result)
    }

    showSuccessToast('补气成功')
    emit('complete', { status: true, readCard: false })
  }
  catch (error: any) {
    operationSuccess.value = false
    resultMessage.value = `补气失败：${error.message || error.msg || '请检查读卡器连接或IC卡状态后重试'}`
    showResult.value = true
    showFailToast(`补气失败：${error.message || error.msg || '未知错误'}`)
  }
  finally {
    loading.value = false
  }
}

// 关闭结果弹窗
function closeResult() {
  showResult.value = false
  if (operationSuccess.value) {
    emit('complete', { status: true, readCard: true })
  }
}

// 取消操作
function handleCancel() {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    emit('closeOperation')
  }, 300)
}
</script>

<template>
  <div class="gas-refund-page">
    <!-- 最后一次购气记录 -->
    <CardContainer v-if="canFillGas" title="最后一次购气记录" class="mb-16">
      <div class="record-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
        <div class="record-item" style="grid-column: span 2;">
          <dt class="record-label">
            购气时间
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.date }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            支付方式
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.paymentMethod }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            收款人
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.operator }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            购气金额
          </dt>
          <dd class="record-value">
            ¥{{ formattedLastRecord.amount }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            购气气量
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.gas }} m³
          </dd>
        </div>
      </div>
    </CardContainer>

    <!-- 最后一次购气 关联补气记录 -->
    <CardContainer v-if="canFillGas && lastRecord?.value?.fillgas" title="关联补气记录" class="mb-16">
      <div class="record-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
        <div class="record-item" style="grid-column: span 2;">
          <dt class="record-label">
            购气时间
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.fillgas?.date }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            操作人
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.fillgas?.operator }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            补气金额
          </dt>
          <dd class="record-value">
            ¥{{ formattedLastRecord.fillgas?.amount }}
          </dd>
        </div>
        <div class="record-item">
          <dt class="record-label">
            补气气量
          </dt>
          <dd class="record-value">
            {{ formattedLastRecord.fillgas?.gas }} m³
          </dd>
        </div>
      </div>
    </CardContainer>

    <!-- 累购设置 -->
    <CardContainer title="累购设置" class="mb-16">
      <div class="accumulate-setting">
        <van-cell center>
          <template #title>
            <span class="setting-label">是否计入累购</span>
            <span class="setting-desc">选择补气是否计入用户累计购气量</span>
          </template>
          <template #right-icon>
            <van-switch
              v-model="formData.includeInAccumulated"
              size="20"
              :active-color="formData.includeInAccumulated ? '#07c160' : '#ee0a24'"
            />
          </template>
        </van-cell>
        <div class="setting-hint">
          <van-icon name="info-o" size="14" color="#666" />
          <span>{{ formData.includeInAccumulated ? '计入累购：此次补气将计入用户累计购气量统计' : '不计入累购：此次补气不计入用户累计购气量统计' }}</span>
        </div>
      </div>
    </CardContainer>

    <!-- 备注信息 -->
    <CardContainer title="备注信息" class="mb-16">
      <van-field
        v-model="formData.remarks"
        type="textarea"
        rows="3"
        placeholder="请输入补充原因或备注信息"
        maxlength="200"
        show-word-limit
        class="remarks-field"
      />
    </CardContainer>

    <!-- 文件上传 -->
    <GridFileUploader
      v-if="useGridUploader"
      ref="gridFileUploaderRef"
      v-model:file-list="fileList"
      :file-types="config.fileTypes"
      title="上传附件"
    />
    <CardContainer v-else class="mb-16">
      <FileUploader
        v-model:file-list="fileList"
        :multiple="true"
        :user-type="config.fileTypes && config.fileTypes.length > 0 ? config.fileTypes[0].userType : '掉气补气'"
      />
    </CardContainer>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button
        plain
        class="cancel-btn"
        :loading="loading"
        @click="handleCancel"
      >
        取消
      </van-button>
      <van-button
        type="success"
        class="fill-gas-btn"
        :loading="loading"
        :disabled="!canFillGas"
        @click="startFillGas"
      >
        <van-icon name="plus" class="mr-1" />
        确认补气
      </van-button>
    </div>

    <!-- 结果弹窗 -->
    <van-overlay v-model:show="showResult" class="result-overlay">
      <div class="result-content">
        <div class="result-icon">
          <van-icon
            :name="operationSuccess ? 'success' : 'close'"
            :color="operationSuccess ? '#07c160' : '#ee0a24'"
            size="48"
          />
        </div>
        <h3 class="result-title">
          {{ operationSuccess ? '补气成功' : '补气失败' }}
        </h3>
        <p class="result-message">
          {{ resultMessage }}
        </p>
        <van-button
          type="primary"
          class="result-btn"
          @click="closeResult"
        >
          确定
        </van-button>
      </div>
    </van-overlay>

    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" />
  </div>
</template>

<style lang="less" scoped>
.gas-refund-page {
  position: relative;
  box-sizing: border-box;

  .card-info {
    .card-header {
      padding: 12px;
    }

    .balance-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }
  }

  .card-info-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 12px !important;

    .card-info-item {
      padding: 10px;
      background-color: #eff6ff;
      border-radius: 6px;

      &.full-width {
        grid-column: 1 / -1 !important;
      }

      &.empty {
        background-color: transparent;
      }

      .card-info-label {
        color: #6b7280;
        font-size: 12px;
        margin: 0 0 4px 0;
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .card-info-value {
        color: #2563eb;
        font-size: 16px;
        margin: 0;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* 手机端样式调整 */
      @media screen and (max-width: 767px) {
        padding: 8px;

        .card-info-label {
          font-size: 12px;
          margin-bottom: 2px;
        }

        .card-info-value {
          font-size: 14px;
          white-space: normal;
          word-break: break-word;
        }
      }
    }

    /* 响应式布局 */
    @media screen and (max-width: 767px) {
      grid-template-columns: repeat(3, 1fr) !important;
      gap: 8px !important;
    }
  }

  .record-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 12px !important;

    .record-item {
      padding: 10px;
      background-color: #f9fafb;
      border-radius: 6px;

      &.full-width {
        grid-column: 1 / -1 !important;
      }

      &.empty {
        background-color: transparent;
      }

      .record-label {
        color: #666;
        font-size: 14px;
        margin: 0 0 4px 0;
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .record-value {
        color: #333;
        font-size: 14px;
        margin: 0;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* 手机端样式调整 */
      @media screen and (max-width: 767px) {
        padding: 8px;

        .record-label {
          font-size: 12px;
          margin-bottom: 2px;
        }

        .record-value {
          font-size: 12px;
          white-space: normal;
          word-break: break-word;
        }
      }
    }

    /* 响应式布局 */
    @media screen and (max-width: 767px) {
      grid-template-columns: repeat(3, 1fr) !important;
      gap: 8px !important;
    }
  }

  .accumulate-setting {
    .setting-label {
      font-size: 16px;
      font-weight: 500;
      color: #111827;
      display: block;
      margin-bottom: 4px;
    }

    .setting-desc {
      font-size: 12px;
      color: #6b7280;
      display: block;
    }

    .setting-hint {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 12px 16px 8px;
      font-size: 12px;
      color: #666;
      background: #f8f9fa;
      border-radius: 0 0 8px 8px;
      margin-top: -1px;

      span {
        line-height: 1.4;
      }
    }

    :deep(.van-cell) {
      padding: 16px;
      background: white;
      border-radius: 8px 8px 0 0;
    }

    :deep(.van-switch) {
      transform: scale(1.1);
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 16px;

    .cancel-btn {
      border-color: #d1d5db;
      color: #4b5563;
    }

    .fill-gas-btn {
      background-color: #07c160;
      border-color: #07c160;

      &:disabled {
        background-color: #f3f4f6;
        border-color: #d1d5db;
        color: #9ca3af;
      }

      .mr-1 {
        margin-right: 4px;
      }
    }
  }

  // 结果弹窗样式
  .result-overlay {
    display: flex;
    align-items: center;
    justify-content: center;

    .result-content {
      background: white;
      border-radius: 12px;
      padding: 32px 24px;
      text-align: center;
      max-width: 320px;
      width: 90%;

      .result-icon {
        margin-bottom: 16px;
      }

      .result-title {
        font-size: 18px;
        font-weight: 500;
        color: #111827;
        margin-bottom: 8px;
      }

      .result-message {
        font-size: 14px;
        color: #6b7280;
        margin-bottom: 24px;
      }

      .result-btn {
        width: 100%;
      }
    }
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  // 移动端样式
  @media screen and (max-width: 767px) {
    .card-info {
      .balance-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  // 平板模式样式
  @media screen and (min-width: 768px) {
    .action-buttons {
      width: 85%;
      max-width: 960px;
      margin: 0 auto;
    }
  }
}
</style>

<style>
/* 全局样式 */
.gas-refund-page .van-field,
.gas-refund-page .van-cell {
  background-color: #f9fafb !important;
}

:deep(.van-field__control) {
  text-align: left !important;
}

:deep(.van-field) {
  background-color: #f9fafb;
  border-radius: 4px;
  padding: 2px 8px !important;
  margin-bottom: 4px;
}

:deep(textarea.van-field__control) {
  text-align: left;
  padding: 8px !important;
}

.gas-refund-page :deep(.remarks-field) {
  .van-field__control {
    text-align: left !important;
  }
}
</style>
