<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import type { MeterReplacementFormData } from '@/components/meterReset/types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import { runLogic } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { getConfigByNameAsync } from 'af-mobile-client-vue3/src/services/api/common'
import { showFailToast, showSuccessToast } from 'vant'
import { computed, onMounted, provide, reactive, ref } from 'vue'
import { ChargePrintSelectorAndRemarks, FileUploader, GridFileUploader, PaymentMethodSelectorCard, ReceiptModal } from '@/components/common'
import ExtendedInfo from '@/components/meterReset/MrExtendedInfo.vue'
import GasSupplementInfo from '@/components/meterReset/MrGasSupplementInfo.vue'
import NewMeterForm from '@/components/meterReset/MrNewMeterForm.vue'
import OldMeterInfo from '@/components/meterReset/MrOldMeterInfo.vue'
import { commonCal } from '@/services/api/business'
import useBusinessStore from '@/stores/modules/business'

const emit = defineEmits(['closeOperation', 'complete', 'update:modelValue'])
const loading = ref(false)
// 添加配置参数和欠费相关属性
let config = reactive({
  hasArrearsChange: false, // 有欠费是否能换表
  allowedMethods: null,
  // 是否展示抄表册选择
  showMeterBook: true,
  // 是否展示气价选择
  showGasPrice: true,
  // 配置打印参数Logic
  printLogic: '',
  changeMeterFeeList: [
    0,
    398,
  ],
  $globalProp: {
    tenantAlias: 'standard',
  },
  // 最小附件数量 （后期新表旧表照片可能单独字段 目前存储在filelist中无法区分 暂时这么判断）
  minFileCount: 0,
  // 是否默认显示扩展信息
  extInfoStatus: true,
  // 文件上传类型配置
  fileTypes: [],
})

// 当前用户
const currUser = useUserStore().getLogin().f

const fileList = ref<FileItem[]>([])

const fileUploaderRef = ref(null)
const gridFileUploaderRef = ref(null)

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// 用户信息对象
const userInfo = useBusinessStore().currentUser

// 表单数据
const formData = reactive<MeterReplacementFormData>({
  f_using_base_old: null,
  f_meter_type: '',
  f_meter_brand: '',
  f_meter_model: '',
  f_meter_base: 0,
  f_meternumber: '',
  brandid: '',
  modelid: '',
  f_alias: '',
  f_type: '气表清零',
  f_stair_use: null,
  f_ladder_sync: true,
  f_remanent_gas: 0,
  f_remanent_price: 0,
  f_remanent_money: 0,
  f_change_operator: '',
  f_approve_operator: '',
  f_changemeter_fee: 0,
  f_metertitles: '',
  f_meter_book_num: null,
  f_serial_number: '',
  f_price_id: null,
  receiptType: '',
  paymentMethod: '',
  remarks: '',
  files: [],
})

// 使用provide向子组件提供formData
provide('formData', formData)

// 是否显示支付方式
const showPaymentMethod = computed(() => Number(formData.f_changemeter_fee) > 0)

// 子组件的响应组件引用
const printRef = ref()

// 处理取消
function handleCancel() {
  loading.value = true
  emit('complete', { status: true })
}

// 处理提交
async function handleSubmit() {
  if (!validateForm()) {
    return
  }
  loading.value = true
  try {
    formData.files = fileList.value?.map(item => item.result.id).filter(Boolean)
    // 调用换表逻辑
    const result = await runLogic<{ id: number }>('mobile_changeMeter', {
      model: formData,
      user: userInfo,
      config,
      operator: {
        f_operator: currUser.resources.name,
        f_operatorid: currUser.resources.id,
        f_orgid: currUser.resources.orgid,
        f_orgname: currUser.resources.orgs,
        f_depid: currUser.resources.depids,
        f_depname: currUser.resources.deps,
      },
    })

    if (result) {
      showSuccessToast('气表清零成功')

      // 显示收据
      await printRef.value.printParams(config.printLogic, result.id)
    }
    else {
      showFailToast('换表失败，请检查输入信息')
    }
  }
  catch (error) {
    loading.value = false
    showFailToast(error.message || error.msg || '提交失败')
  }
  loading.value = false
}

// 校验表单
function validateForm() {
  // 验证各必填字段
  if (!formData.f_using_base_old) {
    showFailToast('请输入旧表最新底数')
    return false
  }

  // 补气金额 补气量不能小于0 补气单价不能小于0
  if (formData.f_remanent_money < 0 || formData.f_remanent_gas < 0) {
    showFailToast('补气金额、补气量不能小于0')
    return false
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return false
    }
  }

  if (fileList.value.length < config.minFileCount) {
    showFailToast(`请至少上传${config.minFileCount}张照片`)
    return false
  }

  if (config.$globalProp.tenantAlias === 'Xianyang') {
    if (!formData.f_approve_operator) {
      showFailToast('请填写扩展信息中的审核人')
      return
    }

    if (formData.f_changemeter_fee === null || formData.f_changemeter_fee === undefined) {
      showFailToast('请选择扩展信息中的换表费用')
      return false
    }
  }

  return true
}

// 文件上传相关
function onFileAdded(): void {
}

function onFileRemoved(): void {
}

// 组件挂载时调用初始化方法
onMounted(async () => {
  try {
    // 获取有欠费是否能换表的配置
    const configResponse = await getConfigByNameAsync('mobile_MeterResetConfig')
    if (configResponse) {
      config = Object.assign(config, configResponse)
      // 获取到组件分公司目录
    }
    initMeterReplacement()
  }
  catch (error) {
    console.error('初始化失败:', error)
    showFailToast('初始化失败')
  }
})

/**
 * 初始化换表数据
 */
async function initMeterReplacement() {
  try {
    // 获取阶梯气量
    try {
      if (userInfo.f_price_type === '固定气价') {
        userInfo.f_stair_use = 0
      }
      else {
        const getAmount = await commonCal({
          f_userfiles_id: userInfo.f_userfiles_id,
          gas: userInfo.f_total_gas,
        })
        userInfo.f_stair_use = getAmount.sumAmount || 0
      }
      // 更新表单数据中的阶梯用量值
      formData.f_stair_use = userInfo.f_stair_use
    }
    catch (error) {
      formData.f_stair_use = 0
    }
  }
  catch (error) {
    console.error('初始化失败:', error)
    showFailToast('初始化失败')
  }
}

function uploadFile(type: string) {
  if (useGridUploader.value) {
    gridFileUploaderRef.value.triggerFileUpload(type)
  }
  else {
    fileUploaderRef.value.triggerFileInput(type)
  }
}
</script>

<template>
  <div class="meter-replacement mobile-view">
    <!-- 旧表信息组件，恒定为3列布局 -->
    <div class="info-card">
      <OldMeterInfo :info="userInfo" />
    </div>

    <!-- 换表基本信息卡片 -->
    <div class="info-card">
      <NewMeterForm :tenant-alias="config.$globalProp.tenantAlias" @meter-reading-complete="emit('complete')" @upload-file="uploadFile" />
    </div>

    <!-- 补气信息卡片 -->
    <div class="info-card">
      <GasSupplementInfo />
    </div>

    <!-- 修改为带折叠功能的扩展信息卡片 -->
    <CardContainer class="info-card" title="扩展信息" :collapsible="true" :default-collapsed="config.extInfoStatus">
      <ExtendedInfo :show-meter-book="config.showMeterBook" :show-gas-price="config.showGasPrice" :config="config" />
    </CardContainer>

    <!-- 备注信息卡片 -->
    <ChargePrintSelectorAndRemarks
      v-model="formData.receiptType"
      :remarks="formData.remarks"
      @update:remarks="(val) => formData.remarks = val"
    />

    <!-- 支付方式组件 - 从扩展信息中提取出来 -->
    <div v-if="showPaymentMethod" class="info-card">
      <PaymentMethodSelectorCard
        v-model="formData.paymentMethod"
        title="支付方式"
        :allowed-methods="config.allowedMethods"
        class="other-charge-form__section-margin"
      />
    </div>

    <!-- 宫格上传组件（多种文件类型时使用） -->
    <GridFileUploader
      v-if="useGridUploader"
      ref="gridFileUploaderRef"
      v-model:file-list="fileList"
      :file-types="config.fileTypes"
      title="上传附件"
      class="other-charge-form__section-margin"
    />
    <CardContainer
      v-else
      class="other-charge-form__section-margin"
    >
      <!-- 普通上传组件（单一文件类型或无配置时使用） -->
      <FileUploader
        ref="fileUploaderRef"
        v-model:file-list="fileList"
        :multiple="true"
        :user-type="config.fileTypes && config.fileTypes.length > 0 ? config.fileTypes[0].userType : '气表清零'"
        @file-added="onFileAdded"
        @file-removed="onFileRemoved"
      />
    </CardContainer>

    <!-- 按钮区域 -->
    <div class="card-replacement-form__buttons">
      <van-button
        plain
        type="default"
        class="card-replacement-form__cancel-btn"
        :loading="loading"
        @click="handleCancel"
      >
        取消
      </van-button>
      <van-button
        type="primary"
        class="card-replacement-form__confirm-btn"
        :loading="loading"
        @click="handleSubmit"
      >
        确认提交
      </van-button>
    </div>

    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" />
  </div>
</template>

<style lang="less" scoped>
.meter-replacement {
  .content-area {
    padding: 16px;
  }

  /* 添加加载遮罩样式 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;

    .loading-text {
      margin-top: 16px;
      color: #fff;
      font-size: 16px;
    }
  }

  .bottom-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 24px;
  }

  /* 移动端样式 */
  &.mobile-view {
    .content-area {
      padding: 12px;
    }

    .info-card {
      margin-bottom: 12px;
    }

    .bottom-buttons {
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }
  }

  /* 适应不同设备的响应式样式 */
  @media screen and (max-width: 767px) {
    .content-area {
      padding: 12px;
    }

    .info-card {
      margin-bottom: 12px;
    }

    .van-cell-group {
      :deep(.van-cell) {
        padding: 10px 12px;
      }
    }
  }

  /* 平板特定样式（覆盖上面的平板和桌面端样式） */
  @media screen and (min-width: 768px) and (max-width: 1024px) {
    /* 移除这里可能有冲突的样式，使用组件内部的样式控制 */
  }

  /* 确保所有表单项的背景为白色 */
  .van-field,
  .van-cell,
  [class*='bg-'] {
    background-color: #f9fafb !important;
  }

  /* 修改Vant组件样式 */
  .van-field {
    background-color: #f9fafb;
    border-radius: 6px;
  }

  /* iPad特定样式 */
  @media screen and (min-width: 768px) {
    .info-card.upload-card {
      :deep(.file-uploader__container) {
        display: grid;
        grid-template-columns: 1fr 1fr; /* 强制使用两列布局 */
        gap: 16px;
      }

      :deep(.file-uploader__list-container) {
        grid-column: 1 / span 2; /* 文件列表占据整行 */
      }
    }
  }

  /* 移动端特定样式 */
  @media screen and (max-width: 767px) {
    .form-item {
      margin-bottom: 4px;
    }

    .van-field {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  /* 小票样式 */
  .receipt-popup {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
  }

  .receipt-preview {
    display: flex;
    flex-direction: column;
    max-height: 80vh;
  }

  .receipt-content {
    padding: 20px;
    overflow-y: auto;
  }

  .receipt-header {
    text-align: center;
    margin-bottom: 16px;
  }

  .receipt-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .receipt-subtitle {
    font-size: 14px;
    color: #666;
  }

  .receipt-info {
    .info-section {
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px dashed #eaeaea;

      &.no-border {
        border-bottom: none;
        padding-bottom: 0;
      }
    }

    .info-header {
      font-weight: 600;
      margin-bottom: 8px;
      font-size: 15px;
    }

    .info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;
      font-size: 14px;
    }

    .info-label {
      color: #666;
    }

    .info-value {
      font-weight: 500;

      &.highlight {
        color: #ff6b00;
      }
    }
  }

  .receipt-footer-text {
    text-align: center;
    margin-top: 20px;
    color: #999;
    font-size: 12px;

    p {
      margin: 4px 0;
    }
  }

  .receipt-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    padding: 16px;
    border-top: 1px solid #eaeaea;
  }

  /* 表单字段样式占位符 */
  .field-placeholder {
    .van-field__control {
      color: #999;
    }
  }

  /* 调整按钮区域样式 */
  .other-charge-form__buttons {
    display: flex;
    justify-content: flex-end; /* 按钮向右对齐 */
    gap: 16px;
    margin-top: 20px;
    margin-bottom: 40px;
  }

  .other-charge-form__cancel-btn,
  .other-charge-form__confirm-btn {
    min-width: 110px;
    height: 40px;
  }

  /* 按钮样式 */
  .card-replacement-form__buttons {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .card-replacement-form__cancel-btn {
    border-color: #d1d5db;
    color: #4b5563;
  }

  .card-replacement-form__confirm-btn {
    background-color: #2563eb;
  }

  /* 移动端样式 */
  @media screen and (max-width: 767px) {
    .content-area {
      padding: 12px;
    }

    .info-card {
      margin-bottom: 12px;
    }

    .card-replacement-form__buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  /* 平板模式样式 */
  @media screen and (min-width: 768px) {
    .card-replacement-form__buttons {
      width: 85%;
      max-width: 960px;
      margin: 0 auto;
    }
  }
}
</style>
