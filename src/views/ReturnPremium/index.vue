<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import { post } from '@af-mobile-client-vue3/services/restTools'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { showDialog, showFailToast, showToast } from 'vant'
import { computed, defineEmits, onMounted, reactive, ref, watch } from 'vue'
import { ChargePrintSelectorAndRemarks, FileUploader, GridFileUploader, InfoCard, PaymentMethodSelectorCard } from '@/components/common'

import ReceiptModal from '@/components/common/ReceiptModal.vue'
import useBusinessStore from '@/stores/modules/business'
// 类型定义
interface RefundFormData {
  amount: string
  previousBalance: string
  currentBalance: string
  countTotal: boolean
}

// 定义组件事件
const emit = defineEmits<{
  (e: 'complete'): void
  (e: 'closeOperation'): void
}>()
// 获取用户信息
const businessStore = useBusinessStore()
const userStore = useUserStore()

// 配置对象
const config = reactive({
  fileTypes: [],
})

const gridFileUploaderRef = ref()

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// 计算属性获取用户信息
const userInfo = computed(() => {
  if (!businessStore.currentUser) {
    return {
      name: '',
      id: '',
      meterNumber: '',
      balance: 0,
    }
  }

  return {
    name: businessStore.currentUser.f_user_name,
    id: businessStore.currentUser.f_userinfo_code,
    meterNumber: businessStore.currentUser.f_meternumber,
    balance: Number(businessStore.currentUser.f_balance || '0'),
  }
})

// 子组件的响应组件引用
const printRef = ref()

// 退费表单数据
const refundData = reactive<RefundFormData>({
  amount: '',
  previousBalance: '0',
  currentBalance: '0',
  countTotal: false,
})

// 使用计算属性计算当前余额，确保同步更新
function refreshUserBalance() {
  if (businessStore.currentUser) {
    const balance = Number(businessStore.currentUser.f_balance || '0')
    refundData.previousBalance = balance.toFixed(2)
  }
}

// 监听用户信息变化，自动更新余额
watch(() => businessStore.currentUser, () => {
  refreshUserBalance()
}, { immediate: true })

// 收据打印选项
const receiptType = ref('普通收据')
const showPicker = ref(false)
const printOptions = [
  { text: '电子发票', value: '电子发票' },
  { text: '普通收据', value: '普通收据' },
  { text: '两者都需要', value: '两者都需要' },
]

// 支付相关数据
const paymentMethod = ref('现金退费')

// 退费备注
const remarks = ref('')

// 文件上传相关数据
const fileList = ref<FileItem[]>([])

const receiptData = reactive({
  transactionId: '',
  dateTime: '',
  userName: '',
  userId: '',
  cardNo: '',
  amount: '0.00',
  previousBalance: '0.00',
  currentBalance: '0.00',
  refundMethod: '',
  countTotal: '否',
  remarks: '',
  operator: '',
  printType: '',
})

// 更新小票用户信息
function updateReceiptUserInfo() {
  if (businessStore.currentUser) {
    receiptData.userName = businessStore.currentUser.f_user_name || ''
    receiptData.userId = businessStore.currentUser.f_userinfo_code || ''
    receiptData.cardNo = businessStore.currentUser.f_meternumber || ''
    receiptData.previousBalance = userInfo.value.balance.toFixed(2)
  }

  const currUser = userStore.getLogin().f
  if (currUser && currUser.resources) {
    receiptData.operator = currUser.resources.name || '营业员'
  }
}

// 监听用户数据变化，更新小票信息
watch(() => businessStore.currentUser, () => {
  updateReceiptUserInfo()
}, { immediate: true })

// 监听支付方式变化
watch(() => paymentMethod.value, (newValue) => {
  receiptData.refundMethod = newValue
})

// 计算退费后余额
function calculateBalance() {
  const amount = Number.parseFloat(refundData.amount) || 0
  const previousBalance = Number.parseFloat(refundData.previousBalance)

  if (amount <= 0) {
    refundData.currentBalance = refundData.previousBalance
    return false
  }

  if (amount > previousBalance) {
    refundData.currentBalance = refundData.previousBalance
    showToast('退费金额不能超过账户余额！')
    return false
  }
  refundData.currentBalance = (previousBalance - amount).toFixed(2)
  return true
}

// 验证退费金额
function validateRefundAmount() {
  return calculateBalance()
}

// 处理退费表单提交
function handleSubmit(event: Event) {
  event.preventDefault()

  const amount = Number.parseFloat(refundData.amount) || 0
  const previousBalance = Number.parseFloat(refundData.previousBalance)

  if (amount <= 0) {
    showToast('退费金额必须大于0！')
    return
  }

  if (amount > previousBalance) {
    showToast('退费金额不能超过账户余额！')
    return
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return
    }
  }

  const currUser = userStore.getLogin().f

  // 提取文件结果数组
  const fileResults = fileList.value
    .filter(file => file.result) // 过滤出有result属性的文件
    .map(file => file.result.id) // 提取result属性

  const param = {
    f_user_id: businessStore.currentUser?.f_user_id,
    f_userfiles_id: businessStore.currentUser?.f_userfiles_id,
    version: businessStore.currentUser?.version,
    f_refund_fee: refundData.amount,
    f_comments: remarks.value,
    f_payment: paymentMethod.value,
    f_print: receiptType.value,
    f_price_id: businessStore.currentUser?.f_price_id,
    f_add_gas: refundData.countTotal ? '计入' : '不计入',
    f_total_fee: businessStore.currentUser?.f_total_fee,
    f_stairprice_id: businessStore.currentUser?.f_stairprice_id,
    operInfo: {
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
    },
    files: fileResults.length > 0 ? fileResults : null,
  }
  if (businessStore.currentUser?.f_meter_type === '物联网表') {
    // 物联网表退费目前只支持 按金额退费，按气量退费核对业务后目前不支持，后续需要在进行开发
    const extendedParam = param as any
    extendedParam.f_price = 0
    extendedParam.f_refund_gas = ''
    extendedParam.f_total_gas = ''
    extendedParam.f_collection_type = businessStore.currentUser.f_meter_type
  }

  post('/api/af-revenue/logic/mobile_refund', param)
    .then(async (res) => {
      showFailToast('退费操作成功!')
      // 生成小票信息
      await printRef.value.printParams('mobile_refundPrintParams', res.id)
    })
    .catch((error) => {
      showFailToast(`退费操作失败,${error.msg}`)
      console.error('退费操作失败:', error)
    })
}

// 取消操作
function cancelOperation() {
  showDialog({
    title: '确认取消',
    message: '是否确认取消当前退费操作？',
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '返回',
    confirmButtonColor: '#2563eb',
  }).then((result) => {
    if (result === 'confirm') {
      // 通知父页面关闭操作界面
      emit('closeOperation')
    }
  })
}

// 文件上传相关
function onFileAdded(): void {
}

function onFileRemoved(): void {
}

function onConfirmReceiptType(option) {
  receiptType.value = option.selectedOptions[0].value
  showPicker.value = false
}

async function initConfig() {
  const res = await getConfigByNameAsync('mobile_refundConfig')
  if (res) {
    Object.assign(config, res)
  }
}

// 生命周期钩子
onMounted(async () => {
  await initConfig()
  // 初始化数据
  refreshUserBalance()
  updateReceiptUserInfo()
  console.log('businessStore==', businessStore)
  // 设置默认退费方式
  receiptData.refundMethod = paymentMethod.value
  receiptData.printType = receiptType.value
})
</script>

<template>
  <div class="return-premium">
    <!-- 信息提示 -->
    <InfoCard
      type="info"
      main-text="请填写退费信息，系统将自动为当前用户进行退费操作。"
      :sub-text="`当前账户余额：¥ ${userInfo.balance.toFixed(2)}`"
    />

    <form class="return-premium__container" @submit.prevent="handleSubmit">
      <!-- 退费金额区域 -->
      <CardContainer class="return-premium__section-margin">
        <CardHeader title="退费金额" />
        <div class="return-premium__field">
          <label class="return-premium__label">退费金额 (元)</label>
          <div class="return-premium__amount-input">
            <van-field
              v-model="refundData.amount"
              type="digit"
              placeholder="0.00"
              input-align="right"
              @input="validateRefundAmount"
            >
              <template #prefix>
                <span class="return-premium__prefix">¥</span>
              </template>
            </van-field>
          </div>
          <p class="return-premium__hint">
            退费金额不能超过账户余额
          </p>
        </div>
      </CardContainer>

      <!-- 退费信息 -->
      <CardContainer class="return-premium__section-margin">
        <CardHeader title="退费信息" />
        <div class="return-premium__field">
          <label class="return-premium__label">原账户余额</label>
          <div class="return-premium__amount-input">
            <van-field
              v-model="refundData.previousBalance"
              readonly
              input-align="right"
            >
              <template #prefix>
                <span class="return-premium__prefix">¥</span>
              </template>
              <template #suffix>
                <span class="return-premium__suffix">元</span>
              </template>
            </van-field>
          </div>
        </div>

        <div class="return-premium__field">
          <label class="return-premium__label">退费后余额</label>
          <div class="return-premium__amount-input">
            <van-field
              v-model="refundData.currentBalance"
              readonly
              input-align="right"
            >
              <template #prefix>
                <span class="return-premium__prefix">¥</span>
              </template>
              <template #suffix>
                <span class="return-premium__suffix">元</span>
              </template>
            </van-field>
          </div>
        </div>

        <div class="return-premium__checkbox-wrapper">
          <van-checkbox
            v-model="refundData.countTotal"
            shape="square"
            icon-size="18px"
          >
            <span class="return-premium__checkbox-label">计入累计购气量</span>
          </van-checkbox>
        </div>
      </CardContainer>

      <!-- 打印及备注 -->
      <ChargePrintSelectorAndRemarks
        v-model="receiptType"
        v-model:remarks="remarks"
        class="return-premium__section-margin"
      />

      <!-- 支付方式 -->
      <PaymentMethodSelectorCard
        v-model="paymentMethod"
        title="退费方式"
        class="return-premium__section-margin"
      />

      <!-- 上传附件 -->
      <!-- 宫格上传组件（多种文件类型时使用） -->
      <GridFileUploader
        v-if="useGridUploader"
        ref="gridFileUploaderRef"
        v-model:file-list="fileList"
        :file-types="config.fileTypes"
        title="上传附件"
        class="return-premium__section-margin"
      />
      <CardContainer
        v-else
        class="return-premium__section-margin"
      >
        <!-- 普通上传组件（单一文件类型或无配置时使用） -->
        <FileUploader
          v-model:file-list="fileList"
          title="上传附件"
          :multiple="true"
          :max-size="10 * 1024 * 1024"
          :allowed-types="['image/png', 'image/jpeg', 'application/pdf']"
          :user-type="config.fileTypes && config.fileTypes.length > 0 ? config.fileTypes[0].userType : '气费退款'"
          @file-added="onFileAdded"
          @file-removed="onFileRemoved"
        />
      </CardContainer>

      <!-- 退费金额展示 -->
      <CardContainer class="return-premium__summary">
        <div class="return-premium__total-content">
          <p class="return-premium__total-label">
            退费金额：<span class="return-premium__total-value">¥ {{ refundData.amount || '0.00' }}</span>
          </p>
          <p class="return-premium__total-balance">
            退费后余额：¥ {{ refundData.currentBalance }}
          </p>
        </div>
      </CardContainer>

      <!-- 按钮区域 -->
      <div class="return-premium__buttons">
        <van-button
          plain
          type="default"
          class="return-premium__cancel-btn"
          @click="cancelOperation"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          native-type="submit"
          class="return-premium__confirm-btn"
          :disabled="!refundData.amount || Number(refundData.amount) <= 0 || Number(refundData.amount) > Number(refundData.previousBalance)"
        >
          确认退费
        </van-button>
      </div>
    </form>

    <!-- 电子小票弹窗 -->
    <ReceiptModal ref="printRef" />

    <van-popup v-model:show="showPicker" position="bottom" round>
      <van-picker
        :columns="printOptions"
        show-toolbar
        title="选择打印方式"
        @confirm="onConfirmReceiptType"
        @cancel="showPicker = false"
      />
    </van-popup>
  </div>
</template>

<style lang="less" scoped>
.return-premium__container {
  margin-bottom: 16px;
}

.return-premium__section-margin {
  margin-bottom: 16px;
}

.return-premium__field {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.return-premium__label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 4px;
}

.return-premium__hint {
  font-size: 12px;
  color: #999;
  font-style: italic;
  margin: 4px 0 0;
  text-align: right;
}

.return-premium__amount-input {
  :deep(.van-field) {
    background-color: #f9fafb;
    border-radius: 6px;
  }
}

.return-premium__prefix,
.return-premium__suffix {
  color: #6b7280;
  font-size: 14px;
}

.return-premium__checkbox-wrapper {
  padding: 10px 0;
}

.return-premium__checkbox-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.return-premium__print-section {
  padding: 4px 0;
}

.return-premium__print-field {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.return-premium__print-selector {
  position: relative;

  :deep(.van-field) {
    background-color: #f9fafb;
    border-radius: 6px;
  }
}

.return-premium__textarea {
  width: 100%;
  padding: 1px;
  :deep(.van-field__control) {
    background-color: #f9fafb;
    border-radius: 6px;
    min-height: 64px;
  }

  :deep(.van-field__word-limit) {
    text-align: right;
    margin-top: 4px;
  }
}

.return-premium__summary {
  background: linear-gradient(to right, #e6f1ff, #ecf5ff);
  border: 1px solid #d1e2ff;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.return-premium__total-content {
  width: 100%;
}

.return-premium__total-label {
  font-size: 16px;
  font-weight: 500;
  color: #4b5563;
  margin: 0 0 4px 0;
}

.return-premium__total-value {
  font-size: 20px;
  font-weight: 600;
  color: #2563eb;
}

.return-premium__total-balance {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.return-premium__buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.return-premium__cancel-btn {
  border-color: #d1d5db;
  color: #4b5563;
}

.return-premium__confirm-btn {
  background-color: #2563eb;
}

// 小票样式
.receipt-content {
  width: 100%;
}

.receipt-section {
  margin-bottom: 16px;
  position: relative;

  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    background-image: linear-gradient(to right, #e8e8e8 50%, transparent 50%);
    background-size: 8px 1px;
    background-repeat: repeat-x;
  }

  &::before {
    top: 0;
  }

  &::after {
    bottom: 0;
  }

  &:first-child::before,
  &:last-child::after {
    display: none;
  }

  :deep(.van-cell) {
    padding: 8px 0;
    font-size: 14px;
  }

  :deep(.van-cell__title) {
    flex: 0 0 auto;
    width: 100px;
    color: #666;
  }

  :deep(.van-cell__value) {
    text-align: right;
    color: #333;
  }
}

.emphasis {
  :deep(.van-cell__value) {
    font-weight: 600;
    color: #2563eb;
  }
}

.receipt-qrcode {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.qrcode-placeholder {
  width: 96px;
  height: 96px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;
}

.receipt-hint {
  font-size: 12px;
  color: #999;
  text-align: center;
  margin-top: 12px;
}
</style>
