<script setup lang="ts">
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import { del } from 'af-mobile-client-vue3/src/services/restTools'
import { ref } from 'vue'

const configName = 'mobile_meterInfoManageCRUD'
const serviceName = 'af-revenue'
// 子组件的响应组件引用
const meterInfo = ref<any>(null)

async function delMeterInfo(record: any) {
  console.log('delMeterInfo', record.m_id)
  // 删除
  await del(`api/af-revenue/entity/t_meterinfo/${record.m_id}`, {}).then((res) => {
    console.log(res)
    if (res.msg === 1) {
      meterInfo.value.updateConditionAndRefresh()
    }
  })
}
</script>

<template>
  <XCellList
    ref="meterInfo"
    :config-name="configName"
    :service-name="serviceName"
    @delete-row="delMeterInfo"
  />
</template>
