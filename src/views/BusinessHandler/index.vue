<script setup lang="ts">
import type { searchType } from '@/components/OnlineRevenue/types'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { showConfirmDialog, showDialog, showToast } from 'vant'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import BusinessHandler from '@/components/OnlineRevenue/BusinessHandler.vue'
import useBoolean from '@/hooks/useBoolean'
import { commonCal, getNoWriteCardRecord, getUseOperations, getUserInfo, getUserOrgList } from '@/services/api/business'
import { readCard } from '@/services/cardService'
import useBusinessStore from '@/stores/modules/business'

const businessHandlerRef = ref<InstanceType<typeof BusinessHandler>>()
const router = useRouter()
const businessStore = useBusinessStore()
const orgList = ref([])
const currUser = useUserStore().getLogin().f
const userAvailableOperations = ref<string[]>([])
const { bool: loading, setTrue: setLoading, setFalse: endLoading } = useBoolean(false)
const userPermissions = useUserStore().getPermissions()

// 从Pinia存储中获取用户信息
onMounted(async () => {
  // 从业务存储中获取用户信息
  const storeUserInfo = businessStore.currentUser

  if (storeUserInfo) {
    // 获取用户可用的操作权限
    await loadUserOperations()
    await getOrgCondition()
    storeUserInfo.f_price_id && await getUserStairInfo(storeUserInfo.f_userinfo_id, storeUserInfo.f_userfiles_id)
    return
  }

  // 如果Pinia中没有用户信息，提示错误
  showToast('未获取到用户信息，请返回重新选择用户')
})

async function getOrgCondition() {
  orgList.value = [currUser.resources.orgid, ...await getUserOrgList()]
  console.log('orgList', orgList.value)
}

// 加载用户可用操作
async function loadUserOperations() {
  if (!businessStore.currentUser)
    return

  loading.value = true
  try {
    // 获取用户可用的操作权限
    await getUseOperations({
      f_userfiles_id: businessStore.currentUser.f_userfiles_id,
      f_userinfo_id: businessStore.currentUser.f_userinfo_id,
      f_userinfo_code: businessStore.currentUser.f_userinfo_code,
      cardInfo: businessStore.currentCard,
    }).then(async (res: any) => {
      userAvailableOperations.value = res

      // 判断是否有读卡信息
      if (businessStore.canRefundGas) {
        userAvailableOperations.value.push('购气撤销')
        await nextTick()
        const gasAmount = businessStore.currentCard.Gas || 0
        const moneyAmount = businessStore.currentCard.Money || 0

        let message = '检测到您的IC卡上有'
        if (gasAmount > 0 && moneyAmount > 0) {
          message += ` 气量${gasAmount}m³ 和 金额${moneyAmount}元 `
        }
        else if (gasAmount > 0) {
          message += ` 气量${gasAmount}m³ `
        }
        else if (moneyAmount > 0) {
          message += ` 金额${moneyAmount}元 `
        }

        if (userPermissions.includes('购气撤销')) {
          showConfirmDialog({
            title: '购气撤销提醒',
            message,
            confirmButtonText: '立即撤销',
            cancelButtonText: '稍后处理',
            confirmButtonColor: '#ff6b6b',
          }).then(
            () => {
              console.log('进入到这里了')
              businessHandlerRef.value?.selectOperation('购气撤销')
            },
          ).catch(() => {})
        }
        else {
          message += '，请核实后在进行业务操作'
          showDialog({
            message,
          })
        }
      }
      // 判断是否有未写卡记录
      if (businessStore.currentUser.f_meter_type?.indexOf('卡表')) {
        userAvailableOperations.value.push('线下写卡')
        await nextTick()
        // 尝试获取未写卡记录
        getNoWriteCardRecord({
          f_userinfo_id: businessStore.currentUser.f_userinfo_id,
        }).then((res) => {
          businessStore.setUnWriteCardRecord(res)
          if (res.length > 0) {
            if (res.length === 1) {
              showConfirmDialog({
                title: '提示',
                message: '检测到您的IC卡上有未写卡记录，是否需要进行写卡操作？',
              }).then(() => {
                businessHandlerRef.value?.selectOperation('线下写卡')
              })
            }
            else {
              showDialog({
                title: '提示',
                message: '检测到您的IC卡上有多条未写卡记录，请核实后处理！！',
              })
            }
          }
        })
      }
    })
  }
  catch (error) {
    console.error('获取用户操作权限失败', error)
    showToast('获取用户操作权限失败，请重试')
  }
  finally {
    loading.value = false
  }
}

// 获取用户阶梯信息
async function getUserStairInfo(f_userinfo_id: string, f_userfiles_id: string) {
  const res = await commonCal({ f_userinfo_id, f_userfiles_id, gas: 0 })
  businessStore.setCurrentUserStairInfo(res)
}

// 关闭业务办理（返回上一页）
function closeBusinessHandler() {
  // 清除Pinia中的业务数据
  businessStore.clearBusinessInfo()
  router.back()
}

// 业务操作完成
async function handleOperationComplete(operation: string, status: { status: boolean, readCard: boolean }) {
  console.warn(`业务 ${operation} 操作${status?.status ? '成功' : '失败'}`)
  setLoading()
  // 重新获取用户信息
  const params = {
    type: 'f_userinfo_id' as searchType,
    keyword: businessStore.currentUser.f_userinfo_id as string,
    pageNo: 1,
    orgList: orgList.value,
  }
  businessStore.clearBusinessInfo()
  // 如果是补卡完成 需要重新读卡
  if ((['补卡'].includes(operation) || status?.readCard) && status?.status) {
    await readCard().then((res) => {
    // 如果没有读取到卡号
      if (res.CardID) {
        businessStore.setCurrentCard(res)
        params.type = 'f_card_id' as searchType
        params.keyword = res.CardID
      }
    }).catch(() => {
      console.log('补卡之后读卡失败')
    })
  }
  // 发起API请求
  await getUserInfo(params)
    .then(async (res: any) => {
      // 如果有数据则追加到列表中
      if (res && Array.isArray(res) && res.length > 0) {
        businessStore.setCurrentUser(res[0])

        // 获取业务按钮
        loadUserOperations().then(async () => {
          // 获取用户阶梯信息
          res[0].f_price_id && getUserStairInfo(res[0].f_userinfo_id, res[0].f_userfiles_id).then(() => {
            endLoading()
          })
          if (['换表', '清零', '换控制器', '换主板', '换基表'].includes(operation) && userAvailableOperations.value.includes('补气购气')) {
            // 如果刚换表完成并且 重新获取的按钮里有补气购气
            // 直接跳转到补气购气页面
            showConfirmDialog({
              title: '提示',
              message: '换表成功，是否跳转到补气购气页面？',
            }).then(() => {
              businessHandlerRef.value?.selectOperation('补气购气')
            })
          }
          if (['补卡'].includes(operation) && status?.status && userAvailableOperations.value.includes('掉气补气')) {
            // 如果刚换表完成并且 重新获取的按钮里有补气购气
            // 直接跳转到补气购气页面
            showConfirmDialog({
              title: '提示',
              message: '补卡成功，是否跳转到掉气补气页面',
            }).then(() => {
              businessHandlerRef.value?.selectOperation('掉气补气')
            })
          }
        })
      }
      else {
        showToast('获取用户失败')
      }
    })
    .catch(() => {
      showToast('获取用户失败，请检查用户状态或者重新查询')
    })
}
</script>

<template>
  <main class="business-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="var(--van-primary-color)" />
      <p>正在加载用户数据...</p>
    </div>

    <!-- 使用BusinessHandler组件 -->
    <BusinessHandler
      v-else-if="businessStore.currentUser" ref="businessHandlerRef" :user="businessStore.currentUser"
      :operations="userAvailableOperations" @close="closeBusinessHandler"
      @operation-complete="handleOperationComplete"
    />
    <div v-else class="no-user-info">
      <p>未找到用户信息，请重新选择用户</p>
      <van-button type="primary" @click="router.push('/online-revenue')">
        返回搜索页面
      </van-button>
    </div>
  </main>
</template>

<style lang="less" scoped>
.business-page {
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  padding: 16px;
  padding-top: 56px;

  p {
    margin-top: 16px;
    color: #666;
  }
}

.no-user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  padding: 16px;
  padding-top: 56px;
  /* 为导航栏留出空间 */

  p {
    margin-bottom: 20px;
    color: #666;
  }
}

:deep(.van-nav-bar) {
  background-color: #fff;

  .van-nav-bar__title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .van-icon {
    color: #666 !important;
  }
}
</style>
