<script setup lang="ts">
import { getFunction } from '@af-mobile-client-vue3/utils/common'
import { indexedDB } from '@af-mobile-client-vue3/utils/indexedDB'
import { showToast } from 'vant'
import { useRoute, useRouter } from 'vue-router'

const curFunction = getFunction(useRoute().name as string)?.children

const router = useRouter()
function cleanConfigCache() {
  // 清除indexedDB缓存
  indexedDB.clear()
  showToast({
    message: '操作成功',
  })
}
</script>

<template>
  <main class="chat_main h-full w-full">
    <van-notice-bar
      left-icon="volume-o"
      text="从来没有真正的绝境，只有心灵的迷途"
    />
    <van-button v-for="(item, index) in curFunction" :key="index" type="primary" @click="router.push({ name: item.link, params: { id: 123 } })">
      {{ item.name }}
    </van-button>

    <!-- 在线操作入口 -->
    <van-button type="primary" @click="router.push('/OnlineRevenue')">
      在线操作
    </van-button>
    <van-button type="primary" @click="router.push('/OnlineRevenueBind')">
      在线操作绑定
    </van-button>
    <van-button type="primary" @click="router.push('/meter-reading')">
      在线抄表
    </van-button>
    <van-button type="primary" @click="router.push('/meterInfoManage')">
      表计管理
    </van-button>
    <van-button type="primary" @click="cleanConfigCache">
      清除配置缓存
    </van-button>
  </main>
</template>

<style scoped lang="less">
.van-button {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
