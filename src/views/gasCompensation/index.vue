<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import { debounce } from 'lodash-es'
import { showConfirmDialog, showToast } from 'vant'
import { computed, defineEmits, onMounted, reactive, ref, watch } from 'vue'
import { ChargePrintSelectorAndRemarks, FileUploader, FormField, GridFileUploader, InfoCard, PaymentMethodSelectorCard, ReceiptModal } from '@/components/common'
import { commonCal, remanent } from '@/services/api/business'
import { remanentinitCard } from '@/services/cardService'
import useBusinessStore from '@/stores/modules/business'
import 'vant/es/toast/style'
import 'vant/es/dialog/style'

const emit = defineEmits(['closeOperation', 'complete'])
const loading = ref(false)

// 用户信息（实际项目中应从状态管理或API获取）
const userInfo = useBusinessStore().currentUser

// 气价设置
const GAS_PRICE = userInfo.f_remanent_price

const uploadedFiles = ref<FileItem[]>([])

// 表单数据
interface FormData {
  f_fill_gas: number
  f_fill_money: number
  f_pregas: number
  f_preamount: number
  f_totalcost: number
  f_collection: number
  receiptType: string
  paymentMethod: string
  remarks: string
  writeCard_gas: number
  writeCard_fee: number
  f_total_gas: number
  f_total_fee: number
  files?: string[]
}

const formData = reactive<FormData>({
  f_fill_gas: userInfo.f_remanent_gas ?? 0,
  f_fill_money: userInfo.f_remanent_money ?? 0,
  f_pregas: 0,
  f_preamount: 0,
  f_totalcost: 0,
  f_collection: 0,
  receiptType: '普通收据',
  paymentMethod: '现金缴费',
  remarks: null,
  writeCard_gas: 0,
  writeCard_fee: 0,
  f_total_gas: 0,
  f_total_fee: 0,
})

// 定义配置接口
interface ConfigOptions {
  printType: string
  payment: string
  allowedMethods: string[] | null
  // 其他可能的配置项
  printLogic: string
  fileTypes?: {
    userType: string
    picMinNum: number
    description?: string
  }[]
}

// 定义内部配置对象
const config = reactive<ConfigOptions>({
  printType: '普通收据',
  payment: '现金缴费',
  allowedMethods: null,
  printLogic: '',
  fileTypes: [],
})

const isWriteCard = computed(() => {
  return userInfo.f_meter_type.indexOf('卡表') > 0 || (userInfo.f_meter_type === '物联网表' && userInfo.f_hascard === '是')
})

// 写卡总气量
const totalGasToWrite = computed(() => {
  return (Number(formData.f_fill_gas) || 0) + (Number(formData.f_pregas) || 0)
})

// 本期结余
const curbalance = computed(() => {
  if (formData.f_preamount && formData.f_preamount > 0) {
    return Number((Number(formData.f_collection) + Number(userInfo.f_balance) - Number(formData.f_preamount)).toFixed(2))
  }
  return userInfo.f_balance
})

// 子组件的响应组件引用
const printRef = ref()
const gridFileUploaderRef = ref()

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// 根据气量划价
async function calculateGas() {
  if (!formData.f_pregas || userInfo.f_collection_type !== '按气量') {
    return
  }
  // 根据预购气量划价
  const result = await commonCal({
    f_userinfo_id: userInfo.f_userinfo_id,
    f_card_id: userInfo.f_card_id,
    f_meternumber: userInfo.f_meternumber,
    f_userfiles_id: userInfo.f_userfiles_id,
    gas: formData.f_pregas,
  })
  formData.f_preamount = Number(result.money)
  formData.f_totalcost = Number((userInfo.f_balance > formData.f_preamount ? 0 : formData.f_preamount - userInfo.f_balance).toFixed(2))
  formData.f_collection = formData.f_totalcost
}

// 根据金额划价
async function calculateAmount() {
  if (!formData.f_preamount || userInfo.f_collection_type === '按气量') {
    return
  }
  // 根据预购金额划价
  const result = await commonCal({
    f_userinfo_id: userInfo.f_userinfo_id,
    f_card_id: userInfo.f_card_id,
    f_meternumber: userInfo.f_meternumber,
    f_userfiles_id: userInfo.f_userfiles_id,
    money: formData.f_preamount,
  })
  formData.f_pregas = Number(result.gas)
  formData.f_totalcost = Number((userInfo.f_balance > formData.f_preamount ? 0 : formData.f_preamount - userInfo.f_balance).toFixed(2))
  formData.f_collection = formData.f_totalcost
}

// 使用防抖包装计算函数
const debouncedCalculateGas = debounce(calculateGas, 500)
const debouncedCalculateAmount = debounce(calculateAmount, 500)

// 监听气量变化
watch(() => formData.f_pregas, (newVal) => {
  if (userInfo.f_collection_type === '按气量' && newVal !== undefined) {
    debouncedCalculateGas()
  }
})

// 监听金额变化
watch(() => formData.f_preamount, (newVal) => {
  if (userInfo.f_collection_type !== '按气量' && newVal !== undefined) {
    debouncedCalculateAmount()
  }
})

// 监听收款金额变化
watch(() => formData.f_collection, (newVal) => {
  if (newVal && Number(newVal) < Number(formData.f_totalcost)) {
    showToast('收款金额不能小于应收金额')
    formData.f_collection = formData.f_totalcost
  }
})

// 验证收款金额
function validateReceivedAmount() {
  if (Number(formData.f_collection) < Number(formData.f_totalcost)) {
    showToast('收款金额不能小于应收金额')
    formData.f_collection = formData.f_totalcost
    return false
  }
  return true
}

// 验证表单
function validateForm() {
  if (!validateReceivedAmount()) {
    return false
  }

  if (formData.f_collection > 0 && !formData.paymentMethod) {
    showToast('请选择支付方式')
    return false
  }

  if (!formData.receiptType) {
    showToast('请选择收据打印选项')
    return false
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return false
    }
  }

  return true
}

// 提交数据
async function submit() {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    // 提示插卡
    let message = `是否进行补气购气操作？`
    if (isWriteCard.value) {
      message = `对客户${userInfo.f_user_name}进行补气购气操作。请确保你已经插入了${userInfo.f_meter_brand}的卡？`
    }

    await showConfirmDialog({
      title: '确认',
      message,
    })

    let result = null
    const currUser = useUserStore().getLogin().f
    const operator = {
      f_orgid: currUser.resources.orgid,
      f_orgname: currUser.resources.orgs,
      f_depid: currUser.resources.depids,
      f_depname: currUser.resources.deps,
      f_operator: currUser.resources.name,
      f_operatorid: currUser.resources.id,
    }

    // 处理小数位数
    formData.f_fill_gas = Number(Number(formData.f_fill_gas || 0).toFixed(2))
    formData.f_fill_money = Number(Number(formData.f_fill_money || 0).toFixed(2))
    formData.f_pregas = Number(Number(formData.f_pregas || 0).toFixed(2))
    formData.f_preamount = Number(Number(formData.f_preamount || 0).toFixed(2))

    if (userInfo.f_remanent_type === 1) {
      formData.f_total_gas = Number((Number(userInfo.f_total_gas) + Number(formData.f_fill_gas) + Number(formData.f_pregas)).toFixed(2))
      formData.f_total_fee = Number((Number(userInfo.f_total_fee) + Number(formData.f_fill_money) + Number(formData.f_preamount)).toFixed(2))
    }
    else {
      formData.f_total_gas = Number((Number(userInfo.f_total_gas) + Number(formData.f_pregas)).toFixed(2))
      formData.f_total_fee = Number((Number(userInfo.f_total_fee) + Number(formData.f_preamount)).toFixed(2))
    }

    // 处理文件上传
    if (uploadedFiles.value && uploadedFiles.value.length > 0) {
      const fileResults = uploadedFiles.value
        .filter(file => file.result) // 过滤出有result属性的文件
        .map(file => file.result.id) // 提取result属性

      formData.files = fileResults.length > 0 ? fileResults : []
    }

    if (isWriteCard.value) {
      formData.writeCard_gas = Number((Number(formData.f_pregas) || 0) + (Number(formData.f_fill_gas) || 0).toFixed(2))
      formData.writeCard_fee = Number((Number(formData.f_preamount) || 0) + (Number(formData.f_fill_money) || 0).toFixed(2))
      result = await remanentinitCard(formData, userInfo, operator)
    }
    else {
      result = await remanent(formData, userInfo, operator)
    }

    if (result) {
      showToast('补气成功')
      await printRef.value.printParams(config.printLogic, result.sellid || result.fillgasid, result.fillgasid || '')
    }
  }
  catch (error) {
    if (error !== 'cancel') { // 用户取消确认对话框时不显示错误
      console.error('补气操作失败:', error)
      showToast('补气操作失败')
    }
  }
  finally {
    loading.value = false
  }
}

// 取消操作
function cancelOperation() {
  loading.value = true

  // 通知父页面关闭操作界面
  showConfirmDialog({
    title: '提示',
    message: '确定取消补气操作吗？',
  }).then(() => {
    // 确认取消
    emit('closeOperation')
    showToast('已取消补气')
  }).catch(() => {
    // 返回继续操作
  }).finally(() => {
    loading.value = false
  })
}

// 打印成功
function handlePrintOk() {
  emit('complete', { status: true })
}

// 初始化
onMounted(async () => {
  // 获取气补业务配置信息
  try {
    const gasCompensationConfig = await getConfigByNameAsync('mobile_GasCompensationConfig')
    if (gasCompensationConfig) {
      // 更新配置
      Object.assign(config, gasCompensationConfig)

      // 更新表单默认值
      formData.receiptType = config.printType
      formData.paymentMethod = config.payment
    }
  }
  catch (error) {
    console.error('获取气补业务配置失败', error)
  }
})
</script>

<template>
  <div class="gas-compensation-form">
    <form class="gas-compensation-form__container" @submit.prevent="submit">
      <!-- 顶部信息提示卡片 -->
      <InfoCard
        v-if="isWriteCard"
        type="info"
        main-text="换表后补气业务，系统会自动将用户剩余气量写入补气卡。"
        :sub-text="`当前气价：${GAS_PRICE} 元/m³`"
        class="gas-compensation-form__section-margin"
      />

      <!-- 补气基本信息区域 -->
      <CardContainer class="gas-compensation-form__section-margin" title="补气基本信息">
        <div v-if="userInfo.f_collection_type === '按气量'" class="gas-compensation-form__field">
          <label class="gas-compensation-form__label">应补气量</label>
          <div class="gas-compensation-form__field-with-unit">
            <van-field
              v-model="formData.f_fill_gas"
              type="number"
              readonly
              input-align="right"
            />
          </div>
        </div>

        <div v-else class="gas-compensation-form__field">
          <label class="gas-compensation-form__label">应补金额</label>
          <div class="gas-compensation-form__field-with-unit">
            <van-field
              v-model="formData.f_fill_money"
              type="number"
              readonly
              input-align="right"
            />
          </div>
        </div>
      </CardContainer>

      <!-- 购气信息区域 -->
      <CardContainer
        v-if="isWriteCard"
        class="gas-compensation-form__section-margin" title="购气信息" :collapsible="true"
        :default-collapsed="true"
      >
        <!-- 气量和金额计算器样式 -->
        <div class="gas-compensation-form__calculator-fields">
          <div class="gas-compensation-form__calculator-field" :class="{ 'field-active': userInfo.f_collection_type === '按气量', 'field-readonly': userInfo.f_collection_type !== '按气量' }">
            <FormField label="预购气量 (m³)">
              <van-field
                v-model="formData.f_pregas"
                type="number"
                input-align="right"
                :placeholder="userInfo.f_collection_type === '按气量' ? '请输入' : ''"
                :readonly="userInfo.f_collection_type !== '按气量'"
                :class="{ 'readonly-field': userInfo.f_collection_type !== '按气量', 'active-field': userInfo.f_collection_type === '按气量' }"
              />
            </FormField>
          </div>

          <div class="gas-compensation-form__exchange">
            <i class="i-fa-solid-sync-alt" />
          </div>

          <div class="gas-compensation-form__calculator-field" :class="{ 'field-active': userInfo.f_collection_type !== '按气量', 'field-readonly': userInfo.f_collection_type === '按气量' }">
            <FormField label="预购金额 (元)">
              <van-field
                v-model="formData.f_preamount"
                :readonly="userInfo.f_collection_type === '按气量'"
                type="number"
                input-align="right"
                :placeholder="userInfo.f_collection_type !== '按气量' ? '请输入' : ''"
                :class="{ 'readonly-field': userInfo.f_collection_type === '按气量', 'active-field': userInfo.f_collection_type !== '按气量' }"
              />
            </FormField>
          </div>
        </div>

        <div class="gas-compensation-form__field">
          <label class="gas-compensation-form__label">写卡总气量(m³)</label>
          <div class="gas-compensation-form__field-with-unit">
            <van-field
              v-model="totalGasToWrite"
              readonly
              input-align="right"
            />
          </div>
        </div>

        <div class="gas-compensation-form__field">
          <label class="gas-compensation-form__label">{{ userInfo.f_collection_type === '按气量' ? '应收金额(元)' : '本次缴费(元)' }}</label>
          <div class="gas-compensation-form__field-with-unit">
            <van-field
              v-model="formData.f_totalcost"
              :readonly="userInfo.f_collection_type === '按气量'"
              input-align="right"
              type="number"
            />
          </div>
        </div>
      </CardContainer>

      <!-- 收款信息区域 -->
      <CardContainer v-if="formData.f_pregas > 0" class="gas-compensation-form__section-margin">
        <CardHeader title="收款信息" />
        <div v-if="userInfo.f_collection_type === '按气量'" class="gas-compensation-form__field">
          <label class="gas-compensation-form__label">收款金额(元)<span class="text-red-500">*</span></label>
          <div class="gas-compensation-form__field-with-unit">
            <van-field
              v-model="formData.f_collection"
              type="number"
              placeholder="0.00"
              input-align="right"
              :min="formData.f_totalcost"
              @change="validateReceivedAmount"
              @keypress.enter="validateReceivedAmount"
            />
            <div class="gas-compensation-form__note">
              收款金额不能小于应收金额 {{ formData.f_totalcost }}
            </div>
          </div>
        </div>

        <div class="gas-compensation-form__field">
          <label class="gas-compensation-form__label">上期结余</label>
          <div class="gas-compensation-form__field-with-unit">
            <van-field
              v-model="userInfo.f_balance"
              readonly
              input-align="right"
            />
          </div>
        </div>

        <div v-if="userInfo.f_collection_type === '按气量'" class="gas-compensation-form__field">
          <label class="gas-compensation-form__label">本期结余</label>
          <div class="gas-compensation-form__field-with-unit">
            <van-field
              v-model="curbalance"
              readonly
              input-align="right"
            />
          </div>
        </div>
      </CardContainer>

      <!-- 支付方式区域 -->
      <PaymentMethodSelectorCard
        v-if="formData.f_pregas > 0"
        v-model="formData.paymentMethod"
        title="支付方式"
        :allowed-methods="config.allowedMethods"
        class="gas-compensation-form__section-margin"
      />

      <!-- 使用通用组件处理打印及备注 -->
      <ChargePrintSelectorAndRemarks
        v-model="formData.receiptType"
        :remarks="formData.remarks"
        @update:remarks="(val) => formData.remarks = val"
      />
      <!-- 宫格式文件上传器 -->
      <GridFileUploader
        v-if="useGridUploader"
        ref="gridFileUploaderRef"
        v-model:file-list="uploadedFiles"
        :file-types="config.fileTypes"
        title="上传附件"
      />

      <!-- 文件上传区域 -->
      <CardContainer v-else class="gas-compensation-form__section-margin">
        <!-- 传统文件上传器 -->
        <FileUploader
          v-model:file-list="uploadedFiles"
          title="上传附件"
          :multiple="true"
          accept=".jpg,.jpeg,.png,.pdf"
          :user-type="config.fileTypes && config.fileTypes.length > 0 ? config.fileTypes[0].userType : '补气'"
        />
      </CardContainer>

      <!-- 实收金额展示 -->
      <CardContainer class="gas-compensation-form__summary">
        <div v-if="userInfo.f_collection_type === '按气量'" class="gas-compensation-form__total-content">
          <p class="gas-compensation-form__total-label">
            实收金额：<span class="gas-compensation-form__total-value">¥ {{ formData.f_collection }}</span>
          </p>
          <p class="gas-compensation-form__total-sub">
            应补气量：{{ formData.f_fill_gas }} m³ + 购气：{{ formData.f_pregas || '0.00' }} m³
          </p>
        </div>

        <div v-if="userInfo.f_collection_type === '按金额'" class="gas-compensation-form__total-content">
          <p class="gas-compensation-form__total-label">
            实收金额：<span class="gas-compensation-form__total-value">¥ {{ formData.f_collection }}</span>
          </p>
          <p class="gas-compensation-form__total-sub">
            购气：{{ formData.f_pregas || '0.00' }} m³
          </p>
        </div>
      </CardContainer>

      <!-- 按钮区域 -->
      <div class="gas-compensation-form__buttons">
        <van-button
          plain
          type="default"
          class="gas-compensation-form__cancel-btn"
          :loading="loading"
          @click="cancelOperation"
        >
          取消
        </van-button>
        <van-button
          v-if="userInfo.f_collection_type === '按气量'"
          type="primary"
          native-type="submit"
          class="gas-compensation-form__confirm-btn"
          :loading="loading"
        >
          {{ formData.f_pregas > 0 ? '补气并购气' : '补气' }}
        </van-button>
        <van-button
          v-if="userInfo.f_collection_type === '按金额'"
          type="primary"
          native-type="submit"
          class="gas-compensation-form__confirm-btn"
          :loading="loading"
        >
          {{ formData.f_collection > userInfo.f_remanent_money ? '补气并购气' : '补气' }}
        </van-button>
      </div>
    </form>
    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" @ok="handlePrintOk" @close="handlePrintOk" />
  </div>
</template>

<style scoped lang="less">
.gas-compensation-form {
  &__container {
    margin-bottom: 16px;
  }

  // 添加模式指示器样式
  &__mode-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 8px;
    font-size: 14px;

    .mode-indicator-label {
      color: #666;
      font-weight: 500;
    }

    .mode-indicator-value {
      padding: 4px 12px;
      border-radius: 16px;
      font-weight: 600;
      font-size: 13px;

      &.mode-gas {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
      }

      &.mode-amount {
        background: linear-gradient(135deg, #10b981 0%, #047857 100%);
        color: white;
      }
    }
  }

  // 计算器样式（类似CardGasCalculator）
  &__calculator-fields {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__calculator-field {
    flex: 1;
    transition: all 0.3s ease;

    // 活跃字段样式
    &.field-active {
      transform: scale(1.02);

      :deep(.van-cell) {
        border: 2px solid var(--van-primary-color);
        border-radius: 8px;
        box-shadow: 0 0 0 3px rgba(25, 137, 250, 0.1);
        background: #fafbff;
      }

      :deep(.van-field__label) {
        color: var(--van-primary-color);
        font-weight: 600;
      }
    }

    // 只读字段样式
    &.field-readonly {
      :deep(.van-cell) {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
      }

      :deep(.van-field__control) {
        color: #6c757d;
      }

      :deep(.van-field__label) {
        color: #6c757d;
      }
    }
  }

  &__exchange {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 8px 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }

    i {
      font-size: 18px;
      color: var(--van-primary-color);
    }
  }

  &__unit {
    color: #666;
    font-size: 14px;

    &--left {
      margin-right: 4px;
    }
  }

  &__field {
    margin-bottom: 16px;
  }

  &__label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 4px;
  }

  &__label-with-tag {
    margin-bottom: 12px;
  }

  &__selector {
    position: relative;

    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__amount-input {
    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__prefix,
  &__suffix {
    color: #6b7280;
    font-size: 14px;
  }

  &__note {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;

    &--italic {
      font-style: italic;
    }

    &--small {
      font-size: 10px;
    }
  }

  &__section-header {
    margin-bottom: 16px;
  }

  &__header-with-checkbox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  &__header-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  &__divider {
    height: 1px;
    background-color: #f0f0f0;
    width: 100%;
  }

  &__checkbox {
    margin-right: 16px;
    display: flex;
    align-items: center;
    height: 24px;

    &-control {
      :deep(.van-checkbox__icon) {
        font-size: 16px;
        margin-top: -1px;
      }

      :deep(.van-icon) {
        transform: scale(1);
      }

      :deep(.van-checkbox__label) {
        line-height: 20px;
      }
    }

    &-text {
      font-size: 14px;
      font-weight: normal;
      color: #1f2937;
    }
  }

  &__info-card {
    margin-top: 8px;
    margin-bottom: 8px;
  }

  &__empty-placeholder {
    padding: 20px 0;
    text-align: center;
  }

  &__empty-text {
    color: #6b7280;
    font-size: 14px;
  }

  &__total-content {
    width: 100%;
  }

  &__total-label {
    font-size: 16px;
    font-weight: 500;
    color: #4b5563;
    margin: 0 0 4px 0;
  }

  &__total-value {
    font-size: 20px;
    font-weight: 600;
    color: #2563eb;
  }

  &__total-sub {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
  }

  &__buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  &__cancel-btn {
    border-color: #d1d5db;
    color: #4b5563;
  }

  &__confirm-btn {
    background-color: #2563eb;
  }

  &__section-margin {
    margin-bottom: 16px;
  }

  &__summary {
    background: linear-gradient(to right, #ebf4ff, #e0f2fe);
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  &__explanation {
    background-color: #f0f7ff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
  }

  &__explanation-title {
    color: #003366;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
  }

  &__explanation-list {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      margin-bottom: 6px;
      line-height: 1.4;
      font-size: 14px;
      color: #2196f3;
      position: relative;
      padding-left: 16px;
      display: flex;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: #2563eb;
        border-radius: 50%;
        margin-right: 10px;
        flex-shrink: 0;
        margin-top: 6px;
      }
    }
  }

  // 输入框样式
  :deep(.active-field) {
    .van-field__control {
      font-weight: 600;
      color: #1a1a1a;
    }
  }

  :deep(.readonly-field) {
    .van-field__control {
      background: transparent;
      color: #6c757d !important;
      cursor: not-allowed;
    }
  }

  &__field-with-unit {
    position: relative;

    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__input-wrapper {
    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  :deep(.van-field) {
    background-color: #f9fafb;
    border-radius: 6px;
  }

  &__unit {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 14px;
    pointer-events: none;
  }
}

.custom-tag {
  font-size: 15px;
  padding: 6px 12px;
  height: auto;
  line-height: 1.5;

  :deep(.van-icon) {
    font-size: 16px;
    margin-right: 4px;
  }
}

.user-gas-info {
  :deep(.van-field__value) {
    text-align: right;
  }
}
</style>
