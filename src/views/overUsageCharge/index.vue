<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import { showConfirmDialog, showDialog, showFailToast, showToast } from 'vant'
import { computed, onBeforeMount, reactive, ref } from 'vue'
import {
  ChargePrintSelectorAndRemarks,
  CodePayment,
  FileUploader,
  GridFileUploader,
  InfoCard,
  PaymentMethodSelectorCard,
  ReceiptModal,
} from '@/components/common'
import { commonCal, overUsage } from '@/services/api/business'
import useBusinessStore from '@/stores/modules/business'
import { formatTime } from '@/utils/date'
// 组件发出的事件
const emit = defineEmits(['closeOperation', 'complete'])

// 用户信息
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })

// 当前气价
const GAS_PRICE = ref(null)

// 气价信息
const chargePrice = ref({})

// 在状态变量部分添加
const loading = ref(true)

// 表单数据
interface FormData {
  overusedGas: number
  overusedAmount: number
  isAccumulative: boolean
  receivedAmount: number
  previousBalance: number
  currentBalance: number
  serial_id: string
  receiptType: string
  paymentMethod: string
  remarks: string
  fileList: any[]
}

const formData = reactive<FormData>({
  overusedGas: null, // 超用气量
  overusedAmount: null, // 超用金额
  isAccumulative: true, // 是否累计
  receivedAmount: 0, // 实收金额
  previousBalance: userInfo.value.f_user_balance, // 上次结余
  currentBalance: userInfo.value.f_user_balance, // 当前结余
  serial_id: '',
  receiptType: '', // 打印格式
  paymentMethod: '', // 支付方式
  remarks: '', // 备注
  fileList: [], // 文件列表
})

// 配置选项接口
interface ConfigOptions {
  hasPrint: boolean
  floor: boolean
  printType: string
  allowedMethods: string[] | null
  payment: string
  printLogic: string
  fileTypes?: {
    userType: string
    picMinNum: number
    description?: string
  }[]
}

// 配置对象
const config = reactive<ConfigOptions>({
  hasPrint: false,
  floor: true,
  printType: '普通收据',
  allowedMethods: null,
  payment: '现金缴费',
  printLogic: '',
  fileTypes: [],
})

const gridFileUploaderRef = ref()

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// 状态变量
const paymentValid = ref(false)
const clickConfirm = ref(false)
const fileList = ref<FileItem[]>([])
const printRef = ref()
// 是否显示金额警告
const amountWarningVisible = ref(false)
const showQrCodePayment = ref(false)

// 计算属性：计算超用金额
const calculateOverusedAmount = computed(() => {
  if (!formData.overusedGas || Number(formData.overusedGas) <= 0) {
    return 0
  }
  return Number((Number(formData.overusedGas) * GAS_PRICE.value).toFixed(2))
})

// 监听气量变化，更新金额
function updateOverusedAmount(gas: string) {
  formData.overusedGas = Number(gas)
  if (Number(gas) > 0) {
    formData.overusedAmount = calculateOverusedAmount.value
    formData.receivedAmount = Number(formData.previousBalance) >= Number(formData.overusedAmount) ? 0 : Number((Number(formData.overusedAmount) - Number(formData.previousBalance)).toFixed(2))
    if ((Number(formData.receivedAmount) + Number(formData.previousBalance)) < Number(formData.overusedAmount)) {
      amountWarningVisible.value = true
      paymentValid.value = false
    }
    else {
      amountWarningVisible.value = false
      paymentValid.value = true
    }
  }
  else {
    paymentValid.value = false
    formData.overusedAmount = 0
  }
}

// 验证收款金额
function validateReceivedAmount(amount: string) {
  formData.receivedAmount = Number(amount)
  if ((Number(amount) + Number(formData.previousBalance)) < Number(formData.overusedAmount)) {
    amountWarningVisible.value = true
    paymentValid.value = false
  }
  else {
    amountWarningVisible.value = false
    paymentValid.value = true
  }
}

// 计算本期结余
const currentBalance = computed(() => {
  if (formData.overusedAmount && formData.overusedAmount > 0) {
    return Number((Number(formData.previousBalance) + Number(formData.receivedAmount) - Number(formData.overusedAmount)).toFixed(4))
  }
  return userInfo.value.f_user_balance
})

// 文件上传处理函数
function onFileAdded() {

}

function onFileRemoved() {

}

// 取消操作
function handleCancel() {
  showDialog({
    title: '确认取消',
    message: '是否确认取消当前超用收费操作？',
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '返回',
    confirmButtonColor: '#2563eb',
  }).then((result) => {
    if (result === 'confirm') {
      emit('closeOperation')
    }
  })
}

// 提交表单
async function onSubmit() {
  try {
    // 验证必填字段
    if (!formData.overusedGas || Number(formData.overusedGas) <= 0) {
      showToast('请输入超用气量')
      return
    }

    if (!formData.receivedAmount || Number(formData.receivedAmount) < 0) {
      showToast('请输入收款金额')
      return
    }

    if (!formData.receiptType || formData.receiptType === '') {
      showToast('请选择打印格式')
      return
    }

    if (!formData.paymentMethod || formData.paymentMethod === '') {
      showToast('请选择付款方式')
      return
    }

    // 验证文件上传要求
    if (useGridUploader.value && gridFileUploaderRef.value) {
      const isValid = gridFileUploaderRef.value.validateAll()
      if (!isValid) {
        return
      }
    }

    clickConfirm.value = true

    showConfirmDialog({
      title: '确认',
      message: `确定对客户${userInfo.value.f_user_name}进行超用收费吗？`,
    }).then(async () => {
      // 微信/支付宝支付走单独的流程，通过事件回调处理支付结果
      if (formData.paymentMethod.includes('支付宝') || formData.paymentMethod.includes('微信')) {
        showQrCodePayment.value = true
      }
      // 其他支付方式直接调用收费接口进行处理
      else {
        await processPayment()
      }
    }).catch(() => {
      clickConfirm.value = false
    })
  }
  catch (error) {
    console.error(error)
    showToast(`超用收费失败: ${error.message || error.msg || '未知错误'}`)
    clickConfirm.value = false
  }
}

// 抽取支付处理逻辑为单独函数
async function processPayment(tradeNo = ''): Promise<any> {
  // 提取文件结果数组
  const fileResults = fileList.value
    .filter(file => file.result)
    .map(file => file.result.id)
  formData.currentBalance = currentBalance.value
  // 实际项目中应调用API处理超用收费
  try {
    // 组织参数
    const params = {
      f_curbalance: formData.currentBalance,
      f_preamount: Number(formData.overusedAmount),
      f_pregas: formData.overusedGas,
      f_totalcost: Number(formData.overusedAmount),
      f_total_gas: formData.isAccumulative === true ? Number((Number(userInfo.value.f_total_gas) + Number(formData.overusedGas)).toFixed(4)) : Number(userInfo.value.f_total_gas),
      f_total_fee: formData.isAccumulative === true ? Number((Number(userInfo.value.f_total_fee) + Number(formData.overusedAmount)).toFixed(4)) : Number(userInfo.value.f_total_fee),
      f_payment: formData.paymentMethod,
      f_bill_style: formData.receiptType,
      f_comments: formData.remarks,
      f_serial_id: tradeNo,
      f_collection: Number(formData.receivedAmount),
      chargeprice: [{
        f_price: GAS_PRICE.value,
        f_gas: formData.overusedGas,
        f_money: Number(formData.overusedAmount),
        f_price_name: userInfo.value.f_meter_type === '固定气价' ? '固定气价' : '阶梯1',
        f_user_id: userInfo.value.f_user_id,
        f_userfiles_id: userInfo.value.f_userfiles_id,
        f_operate_date: formatTime(new Date()),
        f_state: '有效',
        f_type: '超用收费',
      }],
      f_balance: formData.currentBalance,
      f_meter_base: userInfo.value.f_meter_base + Number(formData.overusedGas),
      f_balance_gas: userInfo.value.f_balance_gas + Number(formData.overusedGas),
      fileList: fileResults.length > 0 ? fileResults : null,
    }

    // 调用真实的业务API
    const result = await overUsage(params, userInfo.value)
    if (config.hasPrint) {
      await printRef.value.printParams(config.printLogic, result.value)
    }
    else {
      clickConfirm.value = false
      printRef.value.close()
    }
  }
  catch (error) {
    console.error(error)
    clickConfirm.value = false
    showFailToast('超用收费失败')
  }
}

async function handleQrCodePaymentSuccess(paymentResult) {
  try {
    // 使用公共处理函数处理支付
    await processPayment(paymentResult.tradeNo)
    showQrCodePayment.value = false
    clickConfirm.value = false
  }
  catch (error) {
    console.error(error)
    showFailToast('超用收费失败')
  }
}

function handleQrCodePaymentCancel() {
  showQrCodePayment.value = false
  clickConfirm.value = false
}

// 获取气价信息
async function getPrice() {
  try {
    const result = await commonCal({
      f_userinfo_id: userInfo.value.f_userinfo_id,
      f_card_id: userInfo.value.f_card_id,
      f_meternumber: userInfo.value.f_meternumber,
      f_userfiles_id: userInfo.value.f_userfiles_id,
      gas: 0,
    })
    console.log('获取气价信息：', result)
    if (userInfo.value.f_price_type === '固定气价') {
      GAS_PRICE.value = result.chargePrice[0].f_price - 0
    }
    else if (userInfo.value.f_price_type === '阶梯气价') {
      GAS_PRICE.value = result.f_stair1price - 0
    }
    chargePrice.value = result
  }
  catch (error) {
    console.error('获取气价失败', error)
    showFailToast('获取气价失败')
  }
}

// 初始化
onBeforeMount(async () => {
  console.log('用户信息:', userInfo.value)
  try {
    // 获取配置
    const overUsageConfig = await getConfigByNameAsync('mobile_overUsageConfig')

    if (overUsageConfig) {
      // 更新配置
      Object.assign(config, overUsageConfig)

      // 更新表单默认值
      formData.receiptType = config.printType
      formData.paymentMethod = config.payment
    }

    // 获取气价（实际项目中可能需要从API获取）
    await getPrice()
  }
  catch (error) {
    console.error('初始化超用收费配置失败', error)
  }
  finally {
    loading.value = false
  }
})
</script>

<template>
  <div v-if="loading" class="loading-overlay">
    <van-loading size="24px">
      加载中...
    </van-loading>
  </div>

  <div v-else class="over-usage-charge-form">
    <!-- 头部提示信息区域 -->
    <InfoCard
      type="warning"
      main-text="用户表具异常，请根据实际情况核算超用气量并进行收费。温馨提示: 若为阶梯价，只按照一阶进行计算，并不计入阶梯!!"
      :sub-text="`当前气价：${GAS_PRICE} 元/m³`"
      class="over-usage-charge-form__section-margin"
    />

    <form @submit.prevent="onSubmit">
      <div class="over-usage-charge-form__content">
        <!-- 超用气量核算区域 -->
        <CardContainer class="over-usage-charge-form__section-margin">
          <CardHeader title="超用气量核算" />
          <div class="over-usage-charge-form__field">
            <label class="over-usage-charge-form__label">超用气量(m³)<span class="text-red-500">*</span></label>
            <div class="over-usage-charge-form__field-with-unit">
              <van-field
                v-model="formData.overusedGas"
                type="number"
                placeholder="0.00"
                input-align="right"
                :formatter="(value) => !value.includes('.') ? value : value.slice(0, value.indexOf('.') + 3)"
                @update:model-value="updateOverusedAmount"
              />
            </div>
          </div>

          <div class="over-usage-charge-form__field">
            <label class="over-usage-charge-form__label">超用金额(元)</label>
            <div class="over-usage-charge-form__field-with-unit">
              <van-field
                v-model="formData.overusedAmount"
                type="number"
                placeholder="0.00"
                readonly
                input-align="right"
              />
            </div>
            <p class="over-usage-charge-form__note">
              超用气量 × 当前气价 ({{ GAS_PRICE }}元/m³)
            </p>
          </div>

          <!-- 是否累计选项 -->
          <div class="over-usage-charge-form__field">
            <van-checkbox
              v-model="formData.isAccumulative"
              shape="square"
              class="over-usage-charge-form__checkbox"
            >
              累计到总用气量
            </van-checkbox>
            <p class="over-usage-charge-form__note">
              勾选后，该超用气量将累计到用户总用气量中
            </p>
          </div>
        </CardContainer>

        <div class="over-usage-charge-form__row">
          <!-- 收款信息区域 -->
          <CardContainer>
            <CardHeader title="收款信息" />
            <div class="over-usage-charge-form__field">
              <label class="over-usage-charge-form__label">实收金额(元)<span class="text-red-500">*</span></label>
              <div class="over-usage-charge-form__field-with-unit">
                <van-field
                  v-model="formData.receivedAmount"
                  type="number"
                  placeholder="0.00"
                  input-align="right"
                  :min="formData.overusedAmount"
                  @update:model-value="validateReceivedAmount"
                />
              </div>
              <p v-if="amountWarningVisible" class="over-usage-charge-form__error">
                实收金额不能小于应收金额
              </p>
            </div>

            <div class="over-usage-charge-form__field">
              <label class="over-usage-charge-form__label">上期余额(元)</label>
              <div class="over-usage-charge-form__field-with-unit">
                <van-field
                  v-model="formData.previousBalance"
                  type="number"
                  readonly
                  input-align="right"
                />
              </div>
            </div>

            <div class="over-usage-charge-form__field">
              <label class="over-usage-charge-form__label">本期结余(元)</label>
              <div class="over-usage-charge-form__field-with-unit">
                <van-field
                  v-model="currentBalance"
                  type="number"
                  readonly
                  input-align="right"
                />
              </div>
            </div>
          </CardContainer>

          <!-- 打印及备注 -->
          <ChargePrintSelectorAndRemarks
            v-model="formData.receiptType"
            v-model:remarks="formData.remarks"
          />
        </div>

        <!-- 支付方式 -->
        <PaymentMethodSelectorCard
          v-model="formData.paymentMethod"
          title="支付方式"
          :allowed-methods="config.allowedMethods"
          class="over-usage-charge-form__section-margin"
        />

        <!-- 上传附件 -->
        <GridFileUploader
          v-if="useGridUploader"
          ref="gridFileUploaderRef"
          v-model:file-list="fileList"
          :file-types="config.fileTypes"
          title="上传附件"
          class="over-usage-charge-form__section-margin"
        />
        <CardContainer
          v-else
          class="over-usage-charge-form__section-margin"
        >
          <FileUploader
            v-model:file-list="fileList"
            title="上传附件"
            :multiple="true"
            :max-size="10 * 1024 * 1024"
            :allowed-types="['image/png', 'image/jpeg', 'application/pdf']"
            :user-type="config.fileTypes && config.fileTypes.length > 0 ? config.fileTypes[0].userType : '超用收费'"
            @file-added="onFileAdded"
            @file-removed="onFileRemoved"
          />
        </CardContainer>
      </div>

      <!-- 实收金额和信息区域 -->
      <div class="over-usage-charge-form__summary">
        <div class="over-usage-charge-form__summary-content">
          <p class="over-usage-charge-form__summary-main">
            实收金额：<span class="over-usage-charge-form__summary-amount">¥ {{ formData.receivedAmount || '0.00' }}</span>
          </p>
          <p class="over-usage-charge-form__summary-sub">
            超用：{{ formData.overusedGas || '0.00' }}m³ / {{ formData.overusedAmount || '0.00' }}元
          </p>
          <p class="over-usage-charge-form__summary-sub">
            余额抵扣：{{ formData.previousBalance || '0.00' }}元
          </p>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="over-usage-charge-form__actions">
        <van-button
          type="default"
          :loading="clickConfirm"
          size="normal"
          @click="handleCancel"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          native-type="submit"
          size="normal"
          :loading="clickConfirm"
          :disabled="!paymentValid"
        >
          确认收费
        </van-button>
      </div>
    </form>

    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" />

    <!-- 二维码支付弹窗 -->
    <CodePayment
      v-if="showQrCodePayment"
      v-model:show="showQrCodePayment"
      :payment="formData.paymentMethod"
      :collection="formData.receivedAmount"
      type="超用收费"
      @payment-success="handleQrCodePaymentSuccess"
      @payment-cancel="handleQrCodePaymentCancel"
    />
  </div>
</template>

<style lang="less" scoped>
.over-usage-charge-form {
  &__content {
    margin-bottom: 16px;
  }

  &__row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 16px;
    margin-bottom: 16px;

    @media screen and (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  &__field {
    margin-bottom: 16px;
  }

  &__label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 4px;
  }

  &__field-with-unit {
    position: relative;

    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__unit {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 14px;
    pointer-events: none;
  }

  &__note {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
  }

  &__error {
    font-size: 12px;
    color: #ef4444;
    margin-top: 4px;
  }

  &__checkbox {
    margin-top: 4px;

    :deep(.van-checkbox__label) {
      font-size: 14px;
      color: #374151;
    }
  }

  &__section-margin {
    margin-bottom: 16px;
  }

  &__summary {
    background: linear-gradient(to right, #fee2e2, #fef2f2);
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
  }

  &__summary-content {
    width: 100%;
  }

  &__summary-main {
    font-size: 16px;
    font-weight: 500;
    color: #4b5563;
    margin: 0 0 4px 0;
  }

  &__summary-amount {
    font-size: 20px;
    font-weight: 600;
    color: #dc2626;
  }

  &__summary-sub {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
  }
}
.loading-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style>
