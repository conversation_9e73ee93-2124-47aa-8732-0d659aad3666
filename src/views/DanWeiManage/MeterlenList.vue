<script setup lang="ts">
import { ref } from 'vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'

const idKey = ref('info_id')

const configName = ref('unit_meter_query')

// 显示充值框
const showRecharge = ref(false)
// 充值金额
const rechargeAmount = ref('')
// 余额
const balance = ref('')

function recharge(item) {
  console.log('充值！！！！！！！！')
  showRecharge.value = true
  balance.value = item.uf_f_balance_amount
  console.log(item)
}
</script>

<template>
  <div id="MeterlenList">
    <NormalDataLayout title="表具充值">
      <template #layout_content>
        <XCellList
          :config-name="configName"
          :id-key="idKey"
          @to-detail="recharge"
        />
      </template>
    </NormalDataLayout>
    <VanDialog v-model:show="showRecharge" title="充值金额" close-on-click-overlay>
      <VanCellGroup>
        <VanField label="可分配金额" :placeholder="balance" disabled />
        <VanField label="充值金额" v-model="rechargeAmount" placeholder="请输入充值金额" />
      </VanCellGroup>
    </VanDialog>
  </div>
</template>

<style scoped lang="less">
#MeterlenList {
  :deep(.van-dialog__header) {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  // 禁用输入框标注颜色
  :deep(.van-field--disabled .van-field__label) {
    color: var(--van-field-label-color);
  }
  // 禁用输入框文字提示颜色
  :deep(.van-field__control:disabled) {
    -webkit-text-fill-color: var(--van-field-input-text-color);
  }
}
</style>