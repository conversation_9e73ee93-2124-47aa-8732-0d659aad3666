<script setup lang="ts">
import { ref } from 'vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const idKey = ref('c_id')

const configName = ref('DanWei_Manage')

function toDetail(item) {
  router.push({
    name: 'DanWeiDetail',
    params: { id: item.c_id },
  })
}
</script>

<template>
  <div id="DanWeiManage">
    <NormalDataLayout title="单位充值">
      <template #layout_content>
        <XCellList
          :config-name="configName"
          :id-key="idKey"
          @to-detail="toDetail"
        />
      </template>
    </NormalDataLayout>
  </div>
</template>

<style scoped lang="less">
</style>
