<script setup lang="ts">
import XCellDetail from '@af-mobile-client-vue3/components/data/XCellDetail/index.vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import { runLogic } from 'af-mobile-client-vue3/src/services/api/common'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()

const router = useRouter()

const id = ref('')

const loading = ref(true)

const danwei = ref({} as any)

const detailData = ref([])

function getData() {
  id.value = route.params.id as string
  loading.value = true
  runLogic('unit_getById', {
    id: id.value,
  }, 'af-revenue').then((res: any) => {
    console.log(res)
    danwei.value = res
    detailData.value = [
      { title: '编号', value: res.f_customer },
      { title: '电话', value: res.f_user_phone },
      { title: '地址', value: res.f_address },
      { title: '打款账户号', value: res.f_bank_card_number },
      { title: '累计金额', value: res.f_total_fee },
      { title: '表具数量', value: res.f_meterlen === null ? 0 : res.f_meterlen },
      { title: '账户余额', value: res.f_balance },
      { title: '备注', value: res.f_comments },
    ]
    loading.value = false
  })
}

onMounted(() => {
  getData()
})
</script>

<template>
  <NormalDataLayout id="DanWeiDetail" title="单位详情">
    <template #layout_content>
      <XCellDetail
        :title="danwei.f_user_name"
        :describe="`单位名称: ${danwei.f_user_name}`"
        :extra="danwei.f_balance"
        :loading="loading"
      >
        <template #detailContent>
          <div v-for="(item, index) in detailData" :key="index" class="detail-content">
            <div class="detail-content-title">
              {{ item.title }}
            </div>
            <div class="detail-content-msg">
              {{ item.value }}
            </div>
          </div>
        </template>
        <template #extraAction>
          <van-cell
            title="充值" is-link class="recharge-cell" @click="router.push({
              name: 'MeterlenList',
            })"
          />
        </template>
      </XCellDetail>
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
#DanWeiDetail {
  :deep(.detail-header-title) {
    //font-weight: 600;
    //font-size: 18px;
  }
  :deep(.detail-header-extra) {
    //font-size: 30px;
  }
  .detail-content {
    margin: 5px 0 0 0;
    font-size: 15px;
    .detail-content-title {
      font-size: 13px;
      margin-bottom: 5px;
      color: var(--van-text-color-2);
    }
  }
  .recharge-cell {
    margin-top: 1vh;
  }
}
</style>
