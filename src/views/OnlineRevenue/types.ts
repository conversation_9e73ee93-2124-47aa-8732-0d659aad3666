export interface BusinessItem {
  name: string
  icon: string
  iconClass: string
}

export interface UserInfo {
  id: string
  name: string
  phone: string
  type: string
  address: string
  meterType: 'IC卡燃气表' | '物联网燃气表' | '机械燃气表'
  meterNumber: string
  remainingGas: string
  balance: string
  status: '正常' | '欠费' | '暂停'
  lastPurchaseDate: string
}

export interface BusinessOperation {
  name: string
  component: any
  props?: Record<string, any>
}
