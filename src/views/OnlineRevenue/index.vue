<script setup lang="ts">
// 由于使用了auto-import插件，无需手动导入Vue的API
import type { BusinessUser } from '@/components/OnlineRevenue/types'
import type { CardReadResponse } from '@/services/cardService/types'
import UserDetail from '@af-mobile-client-vue3/components/data/UserDetail/index.vue'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import UserSearch from '@/components/OnlineRevenue/UserSearch.vue'
import useBusinessStore from '@/stores/modules/business'

// 设置组件名称 keeplive要用
defineOptions({
  name: 'OnlineRevenue',
})

// 获取路由实例
const router = useRouter()
const route = useRoute()
const meta = route.meta

// 获取业务存储
const businessStore = useBusinessStore()

// 用户详情
const userInfo = ref<BusinessUser | null>(null)

// 根据 meta.type 计算模式配置
const searchModeConfig = computed(() => {
  if (meta.type === 'bind') {
    return {
      showSearch: true, // 搜索模
      showMeter: true, // 扫描
      showCard: false, // 读卡
      showNFC: true, // nfc
    }
  }
  return {
    showSearch: true,
    showCard: true,
    showMeter: true,
  }
})

// 查看用户详情
function viewUserDetail(user: BusinessUser, cardInfo: CardReadResponse | null) {
  if (user) {
    userInfo.value = user
    // 将用户信息存储到Pinia中
    businessStore.setCurrentUser(user)
    // 如果有卡片信息，也存储到Pinia中
    if (cardInfo) {
      businessStore.setCurrentCard(cardInfo)
    }
  }
}

// 关闭用户详情
function closeUserDetail() {
  userInfo.value = null
}

// 打开业务办理
function openBusinessHandler(user?: BusinessUser, cardInfo?: CardReadResponse | null) {
  if (user) {
    // 将用户信息存储到Pinia中
    businessStore.setCurrentUser(user)
    // 如果有卡片信息，也存储到Pinia中
    if (cardInfo) {
      businessStore.setCurrentCard(cardInfo)
    }
    router.push({
      name: 'BusinessHandler',
    })
  }
  else if (businessStore.currentUser) {
    router.push({
      name: 'BusinessHandler',
    })
  }
}
function handlePrint() {
  // 打印逻辑
  console.log('打印用户信息')
}
</script>

<template>
  <main class="online-revenue h-full w-full">
    <div class="page-content">
      <!-- 用户详情界面 -->
      <div v-show="userInfo" class="tab-content">
        <UserDetail
          :user-info-id="userInfo?.f_userinfo_id"
          :show-recent-time="true"
          :show-bottom-buttons="true"
          :show-header="true"
          @business-click="openBusinessHandler"
          @close="closeUserDetail"
          @print="handlePrint"
        />
      </div>
      <!-- 用户搜索界面 -->
      <div v-show="!userInfo" class="tab-content">
        <UserSearch
          :modes="searchModeConfig"
          @view-user-detail="viewUserDetail"
          @business-handler="openBusinessHandler"
        />
      </div>
    </div>
  </main>
</template>

<style lang="less" scoped>
.online-revenue {
  background-color: #f8f8f8;
  padding-bottom: 16px;
}

.page-content {
  background-color: #f8f8f8;
  padding: 13px 13px 16px;
  max-width: 800px;
  margin: 0 auto;
}

.tab-content {
  padding: 0;
}
</style>
