停用页面复用操作说明
====================

## 1. 需要复制的文件清单

### 主要组件文件
src/views/statusChange/index.vue                    # 停用页面主组件

### 依赖组件文件  
src/components/common/FormField.vue                # 表单字段组件
src/components/common/FileUploader.vue             # 文件上传组件
src/components/common/GridFileUploader.vue         # 宫格文件上传组件

## 2. 需要创建的新文件

### 业务状态管理文件
文件路径：src/stores/modules/business.ts
文件内容：
```typescript
import { defineStore } from 'pinia'
import { ref } from 'vue'

const useBusinessStore = defineStore('business', () => {
  // 当前业务用户信息
  const currentUser = ref(null)
  
  // 设置当前业务用户
  function setCurrentUser(user) {
    currentUser.value = user
  }
  
  // 清除当前业务信息
  function clearBusinessInfo() {
    currentUser.value = null
  }

  return {
    currentUser,
    setCurrentUser,
    clearBusinessInfo,
  }
}, {
  persist: true, // 持久化存储
})

export default useBusinessStore
```

## 3. 需要修改的文件

### API接口文件
文件路径：src/services/api/business.ts
需要添加的方法：
```typescript
/**
 * 停启用
 */
export function tableStatusChange(model, user, operator) {
  return post('/af-revenue/logic/mobile_tableStatusChange', { model, user, operator })
}
```

### 路由配置文件
文件路径：src/router/routes.ts
需要添加的路由：
```typescript
// 在文件顶部导入
import statusChange from '@/views/statusChange/index.vue'

// 在routes数组中添加
{
  path: '/statusChange',
  name: 'statusChange',
  component: statusChange,
  meta: {
    title: '停用&启用',
    keepAlive: false,
  },
}
```

## 4. 依赖检查

确保新项目已安装以下依赖：
- Vant UI组件库 (van-field, van-button, van-popup, van-picker等)
- Pinia状态管理
- @af-mobile-client-vue3 相关组件
- Vue 3 + TypeScript

## 5. 使用方式

### 路由跳转方式
```typescript
// 在组件中使用
import { useRouter } from 'vue-router'
const router = useRouter()

// 跳转到停用页面
router.push('/statusChange')
```

### 组件引用方式
```typescript
// 直接引用组件
import statusChange from '@/views/statusChange/index.vue'
```

## 6. 功能说明

停用页面主要功能：
- 显示当前表具状态（正常/停用）
- 根据状态自动判断操作类型（停用/启用）
- 选择停用/启用原因
- 填写操作备注
- 上传相关附件
- 提交停用/启用操作

## 7. 注意事项

1. 确保后端API接口 `/af-revenue/logic/mobile_tableStatusChange` 可用
2. 检查用户权限，确保有停用/启用表具的权限
3. 根据实际业务需求调整停用原因选项配置
4. 如需修改样式，可调整组件内的CSS样式
5. 文件上传功能需要配置对应的上传服务

## 8. 可能需要的调整

根据新项目具体情况，可能需要：
- 修改API接口地址
- 调整用户数据结构
- 修改样式主题
- 调整文件上传配置
- 修改权限验证逻辑

## 9. 测试建议

复用完成后建议测试：
- 页面正常加载
- 状态显示正确
- 原因选择功能
- 文件上传功能
- 提交操作功能
- 错误处理机制

完成以上步骤后，停用页面即可在新项目中正常使用。
