表具阀控页面复用操作说明
========================

## 1. 需要复制的文件清单

### 主要组件文件
src/components/IotMeterValveControl/index.vue                    # 阀控入口组件
src/components/IotMeterValveControl/IotMeterValveControl.vue     # 标准版阀控组件
src/components/IotMeterValveControl/IotMeterValveControlXy.vue   # 咸阳版阀控组件

### 工具文件
src/utils/date.ts                                               # 时间格式化工具

## 2. 需要的业务状态管理

### 业务Store（如果之前没有复制）
文件路径：src/stores/modules/business.ts
```typescript
import { defineStore } from 'pinia'
import { ref } from 'vue'

const useBusinessStore = defineStore('business', () => {
  // 当前业务用户信息
  const currentUser = ref(null)
  
  // 设置当前业务用户
  function setCurrentUser(user) {
    currentUser.value = user
  }
  
  // 清除当前业务信息
  function clearBusinessInfo() {
    currentUser.value = null
  }

  return {
    currentUser,
    setCurrentUser,
    clearBusinessInfo,
  }
}, {
  persist: true,
})

export default useBusinessStore
```

## 3. 需要的API接口

### 阀控相关API
在 src/services/api/business.ts 中添加（如果没有这些方法）：
```typescript
import { runLogic } from '@af-mobile-client-vue3/services/api/common'

// 注意：表具阀控主要使用 runLogic 调用后端逻辑，不需要额外的API方法
// 主要使用的逻辑包括：
// - openzdfk: 开启自动阀控
// - closezdfk: 关闭自动阀控  
// - mobile_getUserValveRecord: 获取阀门操作记录
// - mobile_sendInstruct: 发送指令（咸阳版）
```

## 4. 路由配置

### 添加路由（如果需要独立页面访问）
在 src/router/routes.ts 中添加：
```typescript
// 导入组件
import IotMeterValveControl from '@/components/IotMeterValveControl/index.vue'

// 在routes数组中添加
{
  path: '/valveControl',
  name: 'valveControl',
  component: IotMeterValveControl,
  meta: {
    title: '表具阀控',
    keepAlive: false,
  },
}
```

## 5. 依赖检查

确保新项目已安装以下依赖：
- Vant UI组件库 (van-button, van-loading, van-dialog, van-toast等)
- Pinia状态管理
- @af-mobile-client-vue3 相关组件
- dayjs 时间处理库
- Vue 3 + TypeScript

## 6. 组件功能说明

### 标准版阀控功能（IotMeterValveControl.vue）：
- 显示阀门当前状态（开启/关闭）
- 显示控制模式（自动/手动）
- 切换自动/手动控制模式
- 手动开启/关闭阀门
- 填写操作原因
- 查看操作记录

### 咸阳版阀控功能（IotMeterValveControlXy.vue）：
- 显示阀门当前状态
- 手动开启/关闭阀门
- 填写操作原因
- 发送指令到平台
- 查看操作记录

### 入口组件功能（index.vue）：
- 根据租户配置自动选择标准版或咸阳版
- 获取配置信息
- 统一的事件处理

## 7. 使用方式

### 作为独立页面使用
```typescript
// 路由跳转
import { useRouter } from 'vue-router'
const router = useRouter()
router.push('/valveControl')
```

### 作为组件使用
```typescript
// 在其他组件中引用
import IotMeterValveControl from '@/components/IotMeterValveControl/index.vue'

// 使用组件
<IotMeterValveControl 
  @complete="handleComplete" 
  @closeOperation="handleClose" 
/>
```

## 8. 必需的用户数据结构

组件需要 businessStore.currentUser 包含以下字段：
```typescript
{
  f_userfiles_id: string,      // 用户文件ID
  f_userinfo_id: string,       // 用户信息ID  
  f_user_id: string,           // 用户ID
  f_user_name: string,         // 用户姓名
  f_meternumber: string,       // 表号
  f_valve_state: string,       // 阀门状态（开启/关闭）
  f_network_valve: string,     // 网络阀控状态（0=自动，1=手动）
  f_alias: string,             // 表具品牌别名
}
```

## 9. 注意事项

1. 确保后端支持以下逻辑接口：
   - openzdfk（开启自动阀控）
   - closezdfk（关闭自动阀控）
   - mobile_getUserValveRecord（获取操作记录）
   - mobile_sendInstruct（发送指令，咸阳版需要）

2. 检查用户权限，确保有阀控操作权限

3. 根据实际租户情况选择使用标准版还是咸阳版

4. 确保表具支持阀控功能（物联网表）

## 10. 可能需要的调整

根据新项目具体情况，可能需要：
- 修改租户判断逻辑（tenantAlias配置）
- 调整API接口地址或参数
- 修改用户数据字段映射
- 调整UI样式和布局
- 修改权限验证逻辑

## 11. 测试建议

复用完成后建议测试：
- 页面正常加载和显示
- 阀门状态正确显示
- 自动/手动模式切换功能
- 开启/关闭阀门功能
- 操作原因填写验证
- 操作记录查看功能
- 错误处理和提示

完成以上步骤后，表具阀控功能即可在新项目中正常使用。
