<script setup lang="ts">
import type { FileItem } from '@/components/common/FileUploader.vue'
import type { BusinessUser } from '@/components/OnlineRevenue'
import type { CommonCalResponse } from '@/services/api/business'
import type { CardUser, MoveGasParam } from '@/services/cardService/types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
// import useUserStore from '@af-mobile-client-vue3/stores/modules/user'
import axios from 'axios'
import { showConfirmDialog, showFailToast, showToast } from 'vant'
import { computed, defineEmits, reactive, ref } from 'vue'
import { FileUploader, GridFileUploader } from '@/components/common'
import { commonCal, moveGas } from '@/services/api/business'
import { moveGasWritCard } from '@/services/cardService'
import useBusinessStore from '@/stores/modules/business'
import ReceiptModal from '../../components/common/ReceiptModal.vue'

interface ConfigOptions {
  fileTypes?: {
    userType: string
    picMinNum: number
    description?: string
  }[]
}

const emit = defineEmits(['closeOperation', 'complete'])

const config = reactive<ConfigOptions>({
  fileTypes: [],
})

// 登录用户信息
// const currUser = useUserStore().getLogin().f

// 用户信息（实际项目中应从状态管理或API获取）
const businessStore = useBusinessStore()
const userInfo = ref({ ...businessStore.currentUser })
console.log('用户信息', userInfo.value)

// 目标用户数据
const targetUserInfo = ref<BusinessUser>({
  f_userinfo_code: '',
} as BusinessUser)

// 子组件的响应组件引用
const printRef = ref()

const confirmLoading = ref(false)
// 划价
const pricing = ref<CommonCalResponse>(null)

// 表单数据
interface FormData {
  currentMeterReading: number
  totalGas: number
  moveFee: number
  moveGas: number
  remarks: string
}

const formData = reactive<FormData>({
  totalGas: userInfo.value.f_total_gas,
  moveFee: 0,
  moveGas: 0.00,
  currentMeterReading: userInfo.value.f_meter_base,
  remarks: '',
})

// 剩余余额
const remainingBalance = computed(() => {
  if (userInfo.value.f_meter_type === '物联网表') {
    return userInfo.value.f_balance_amount
  }
  else {
    return userInfo.value.f_user_balance
  }
})

// 按照金额计算
const isAmount = computed(() => {
  return userInfo.value.f_collection_type === '按金额'
})

const uploadedFiles = ref<FileItem[]>([])
const gridFileUploaderRef = ref()
// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// const router = useRouter()

// 计算属性 - 摘要信息
const summarySourceUser = computed(() => userInfo.value.f_user_name)
const summaryTargetUser = computed(() => targetUserInfo.value.f_user_name || '(待选择)')
// const summaryGasAmount = computed(() => formData.moveGas)

// 搜索目标用户
async function searchUser(val) {
  console.log('登录用户', userInfo.value)
  if (!targetUserInfo.value.f_userinfo_code) {
    showToast('请输入目标用户编号')
    return
  }

  if (targetUserInfo.value.f_userinfo_code === userInfo.value.f_userinfo_code) {
    showToast('不能转给自己转气')
    return
  }

  // API调用
  const res = await axios.post('/singlepage/api/af-revenue/sql/sale_getUser', {
    // 搜索该登录者所属公司下的用户
    // data: { condition: `i.f_userinfo_code='${val}' and i.f_orgid='${currUser.resources.orgid}'` },
    // 搜索全部用户
    data: { condition: `i.f_userinfo_code='${val}'` },
  })
  // API查询结果
  console.log('目标用户：', res.data[0])
  if (res.data.length > 0) {
    targetUserInfo.value = res.data[0] as BusinessUser
  }
  else {
    showToast('未找到用户')
    return
  }
  // 提示找到用户
  showToast('已找到用户')
}

// 计算可转气量
function calculateTransferAmount() {
  if (formData.currentMeterReading < userInfo.value.f_meter_base) {
    showToast('请输入有效的表底数')
    return
  }
  const meterReading = formData.currentMeterReading || 0
  const totalPurchased = formData.totalGas || 0

  // 计算可转气量，使用 toFixed 和 parseFloat 来避免浮点数精度问题
  let transferableAmount = Number.parseFloat((totalPurchased - meterReading).toFixed(4))

  // 确保不为负数
  transferableAmount = Math.max(0, transferableAmount)

  // 更新界面
  formData.moveGas = transferableAmount
  console.log('可转气量：', transferableAmount)
}

async function calculateMoveFee(gas) {
  let user = userInfo.value.f_userfiles_id
  if (userInfo.value.f_collection_type === '按气量' || (userInfo.value.f_meter_type === '气量卡表' && targetUserInfo.value.f_meter_type === '金额卡表')) {
    user = targetUserInfo.value.f_userfiles_id
  }
  await commonCal({
    f_userfiles_id: user,
    gas,
  }).then((res) => {
    pricing.value = res
    formData.moveFee = Number((Number(gas) * Number(pricing.value.f_stair1price || pricing.value.gasPrice)).toFixed(2))
    if (formData.moveFee > remainingBalance.value && isAmount.value) {
      showToast('转移金额不能大于剩余金额')
      formData.moveFee = 0
      formData.moveGas = 0
    }
  })
}

async function calculateMovegas(fee) {
  if (targetUserInfo.value.f_meter_type === '金额卡表') {
    // 金额卡表金额取整
    fee = Math.round(fee)
  }
  if (fee > remainingBalance.value && isAmount.value) {
    formData.moveFee = 0
    formData.moveGas = 0
    showToast('转移金额不能大于剩余金额')
    return
  }
  let user = userInfo.value.f_userfiles_id
  if (userInfo.value.f_collection_type === '按金额' || (userInfo.value.f_meter_type === '金额卡表' && targetUserInfo.value.f_meter_type === '气量卡表')) {
    user = targetUserInfo.value.f_userfiles_id
  }
  await commonCal({
    f_userfiles_id: user,
    money: fee,
  }).then((res) => {
    pricing.value = res
    formData.moveGas = Math.floor((Number(fee) / Number(pricing.value.f_stair1price || pricing.value.gasPrice)))
  })
}

// 校验表底数
function checkMeterReading(meterReading: number) {
  if (meterReading < userInfo.value.f_meter_base) {
    showToast('请输入有效的表底数')
    formData.moveFee = 0
    formData.moveGas = 0
    formData.currentMeterReading = userInfo.value.f_meter_base
    calculateTransferAmount()
  }
  calculateTransferAmount()
}

// 提交表单
async function submitForm(event: Event) {
  event.preventDefault()
  // 验证目标用户信息
  if (!targetUserInfo.value.f_user_name) {
    showToast('请先搜索并选择目标用户')
    return
  }

  // 验证表底数和可转气量
  const meterReading = formData.currentMeterReading || 0
  const transferableAmount = formData.moveGas || 0

  if (meterReading < userInfo.value.f_meter_base) {
    showToast('请输入有效的表底数')
    formData.currentMeterReading = userInfo.value.f_meter_base
    return
  }
  if (userInfo.value.f_meter_type === '气量卡表' && meterReading >= userInfo.value.f_meter_base) {
    calculateTransferAmount()
  }

  if (transferableAmount <= 0) {
    showToast('可转气量必须大于0')
    return
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return
    }
  }

  if (isAmount.value) {
    await calculateMovegas(formData.moveFee)
  }
  else {
    await calculateMoveFee(transferableAmount)
  }

  confirmLoading.value = true

  const param: MoveGasParam = {
    userfilesid: targetUserInfo.value.f_userfiles_id,
    f_userinfo_code: targetUserInfo.value.f_userinfo_code,
    f_move_fee: formData.moveFee,
    f_meter_base: Number(formData.currentMeterReading),
    f_move_gas: formData.moveGas,
    f_comments: formData.remarks,
    f_from_userfiles_id: userInfo.value.f_userfiles_id,
    f_to_userfiles_id: targetUserInfo.value.f_userfiles_id,
    f_from_gasbrand_id: (userInfo.value.f_gasbrand_id).toString(),
    f_to_gasbrand_id: (targetUserInfo.value.f_gasbrand_id).toString(),
    f_from_stariprice_id: userInfo.value.f_stairprice_id,
    f_to_stariprice_id: targetUserInfo.value.f_stairprice_id,
  }
  console.log('提交参数：', param)
  if (uploadedFiles.value && uploadedFiles.value.length > 0) {
    const fileResults = uploadedFiles.value
      .filter(file => file.result) // 过滤出有result属性的文件
      .map(file => file.result.id) // 提取result属性

    param.files = fileResults.length > 0 ? fileResults : []
  }

  try {
    const result = await executeGasTransfer(param)
    if (result) {
      await printRef.value.printParams('mobile_moveGasPrintParams', result.gasmoveinfo_id, result)
    }
  }
  catch (error) {
    console.error('转气操作失败', error)
    showFailToast(`转气失败：${error.message || error.msg || '未知错误'}`)
    throw error
  }
  finally {
    confirmLoading.value = false
  }
}

// 执行转气操作
async function executeGasTransfer(transferParam: any) {
  const needCardConfirm = isCardMeterType(targetUserInfo.value)

  if (needCardConfirm) {
    return await handleCardMeterTransfer(transferParam)
  }
  else {
    return await handleNormalMeterTransfer(transferParam)
  }
}

// 判断是否为卡表类型
function isCardMeterType(userInfo: any): boolean {
  const cardMeterTypes = ['金额卡表', '气量卡表']
  return cardMeterTypes.includes(userInfo.f_meter_type) || userInfo.f_alias === 'TianShunNB'
}

// 处理卡表转气
async function handleCardMeterTransfer(transferParam: any) {
  await showConfirmDialog({
    title: '提示',
    message: '请确保插入了金额卡表或气量卡表，否则转气失败',
    confirmButtonText: '已经插卡',
    cancelButtonText: '取消操作',
  })

  return await moveGasWritCard(
    targetUserInfo.value.f_card_id,
    targetUserInfo.value.f_alias,
    transferParam,
    userInfo.value,
    targetUserInfo.value,
  )
}

// 处理普通表转气
async function handleNormalMeterTransfer(transferParam: any) {
  return await moveGas(transferParam, userInfo.value as unknown as CardUser)
}

// 取消操作
function cancelOperation() {
  // 通知父页面关闭操作界面
  // 如果在实际环境中，还可能需要调用父组件中的方法
  showConfirmDialog({
    title: '提示',
    message: '确定取消转气操作吗？',
  }).then(() => {
    // 确认取消
    emit('closeOperation')
    showToast('已取消转气')
  }).catch(() => {
    // 返回继续操作
  })
}

// 文件上传相关
function onFileAdded() {
}

function onFileRemoved() {
}

async function initConfig() {
  try {
    // 使用 Promise API 获取配置
    const res = await getConfigByNameAsync('mobile_gasTransferConfig')

    console.log(res, '========')
    // 更新配置
    Object.assign(config, res)
  }
  catch (error) {
    console.error('初始化配置信息失败', error)
  }
}

// 初始化
onMounted(async () => {
  await initConfig()
})
</script>

<template>
  <div class="gas-transfer-form">
    <!-- 顶部信息提示卡片 -->
    <!-- <InfoCard -->
    <!-- type="info" -->
    <!-- main-text="转气操作 - 请填写转气信息" -->
    <!-- :sub-text="`当前用户: ${userInfo.f_user_name} (${userInfo.f_meternumber})`" -->
    <!-- class="gas-transfer-form__section-margin" -->
    <!-- /> -->

    <!-- 目标用户信息区域 -->
    <CardContainer class="gas-transfer-form__section-margin">
      <CardHeader title="目标用户信息" />

      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">用户编号 <span class="text-red-500">*</span></label>
        <div class="gas-transfer-form__search-wrapper">
          <input
            v-model="targetUserInfo.f_userinfo_code"
            type="text"
            class="gas-transfer-form__search-input"
            placeholder="输入用户编号搜索"
            required
          >
          <button
            type="button"
            class="gas-transfer-form__search-button"
            @click="searchUser(targetUserInfo.f_userinfo_code)"
          >
            <van-icon name="search" size="18" />
          </button>
        </div>
      </div>

      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">用户姓名</label>
        <div class="gas-transfer-form__input-wrapper">
          <van-field
            v-model="targetUserInfo.f_user_name"
            readonly
            placeholder="目标用户姓名"
            input-align="right"
          />
        </div>
      </div>

      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">表具类型</label>
        <div class="gas-transfer-form__input-wrapper">
          <van-field
            v-model="targetUserInfo.f_meter_type"
            readonly
            placeholder="目标用户表具类型"
            input-align="right"
          />
        </div>
      </div>

      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">用户地址</label>
        <div class="gas-transfer-form__input-wrapper">
          <van-field
            v-model="targetUserInfo.f_address"
            readonly
            placeholder="目标用户地址"
            type="textarea"
            rows="2"
            class="gas-transfer-form__address-field"
          />
        </div>
      </div>
    </CardContainer>

    <CardContainer v-if="isAmount" class="gas-transfer-form__section-margin">
      <CardHeader title="转气信息" />
      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">剩余金额<span class="text-red-500">*</span></label>
        <div class="gas-transfer-form__field-with-unit">
          <van-field
            v-model="remainingBalance"
            placeholder="0.00"
            type="number"
            readonly
            input-align="right"
          />
        </div>
      </div>
      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">转移金额<span class="text-red-500">*</span></label>
        <div class="gas-transfer-form__field-with-unit">
          <van-field
            v-model="formData.moveFee"
            placeholder="0.00"
            type="number"
            input-align="right"
            :max="remainingBalance"
            @blur="calculateMovegas(formData.moveFee)"
          />
        </div>
      </div>
      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">转移气量<span class="text-red-500">*</span></label>
        <div class="gas-transfer-form__field-with-unit">
          <van-field
            v-model="formData.moveGas"
            placeholder="0.00"
            type="number"
            input-align="right"
            @blur="calculateMoveFee(formData.moveGas)"
          />
        </div>
      </div>
    </CardContainer>
    <!-- 转气信息区域 -->
    <CardContainer v-else class="gas-transfer-form__section-margin">
      <CardHeader title="转气信息" />

      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">当前表底数 (m³)<span class="text-red-500">*</span></label>
        <div class="gas-transfer-form__field-with-unit">
          <van-field
            v-model="formData.currentMeterReading"
            placeholder="0.00"
            type="number"
            input-align="right"
            @blur="checkMeterReading(formData.currentMeterReading)"
          />
        </div>
      </div>

      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">累计购气量 (m³)</label>
        <div class="gas-transfer-form__field-with-unit">
          <van-field
            v-model="formData.totalGas"
            readonly
            input-align="right"
          />
        </div>
      </div>

      <div class="gas-transfer-form__field">
        <label class="gas-transfer-form__label">可转气量 (m³)</label>
        <div class="gas-transfer-form__field-with-unit">
          <van-field
            v-model="formData.moveGas"
            readonly
            input-align="right"
          />
        </div>
        <div class="gas-transfer-form__note gas-transfer-form__note--italic gas-transfer-form__note--small">
          累计购气量 - 当前表底数 = 可转气量
        </div>
      </div>
    </CardContainer>

    <!-- 备注区域 -->
    <CardContainer class="gas-transfer-form__section-margin">
      <CardHeader title="备注" />
      <div class="gas-transfer-form__field">
        <van-field
          v-model="formData.remarks"
          type="textarea"
          placeholder="可填写转气原因、用户关系等信息"
          rows="3"
          autosize
        />
      </div>
    </CardContainer>

    <!-- 宫格上传组件（多种文件类型时使用） -->
    <GridFileUploader
      v-if="useGridUploader"
      ref="gridFileUploaderRef"
      v-model:file-list="uploadedFiles"
      :file-types="config.fileTypes"
      title="上传附件"
    />
    <!-- 上传附件区域 -->
    <CardContainer v-else class="gas-transfer-form__section-margin">
      <FileUploader
        v-model:file-list="uploadedFiles"
        :user-type="config.fileTypes.length > 0 ? config.fileTypes[0].userType : '转气'"
        title="上传附件"
        :multiple="true"
        accept=".jpg,.jpeg,.png,.pdf"
        @file-added="onFileAdded"
        @file-removed="onFileRemoved"
      />
    </CardContainer>

    <!-- 业务说明区域 -->
    <div class="gas-transfer-form__explanation">
      <h3 class="gas-transfer-form__explanation-title">
        转气说明
      </h3>
      <ul class="gas-transfer-form__explanation-list">
        <li><span class="dot" />转气操作将用户剩余气量转入目标用户</li>
        <li><span class="dot" />可转气量 = 累计购气量 - 当前表底数</li>
        <li><span class="dot" />请确保表底数准确无误，一旦提交不可撤销</li>
        <li><span class="dot" />转气后原用户气量将被清零</li>
      </ul>
    </div>

    <!-- 确认信息区域 -->
    <CardContainer class="gas-transfer-form__summary">
      <div class="gas-transfer-form__total-content">
        <p class="gas-transfer-form__total-label">
          转气信息确认:
        </p>
        <p class="gas-transfer-form__total-sub">
          从 {{ summarySourceUser }} 转入 {{ summaryTargetUser }} 气量：
          <span class="gas-transfer-form__total-value">{{ formData.moveGas }}</span> m³
        </p>
      </div>
    </CardContainer>

    <!-- 底部操作区域 -->
    <div class="gas-transfer-form__buttons">
      <van-button plain :loading="confirmLoading" type="default" class="gas-transfer-form__cancel-btn" @click="cancelOperation">
        取消
      </van-button>
      <van-button :loading="confirmLoading" type="primary" native-type="submit" class="gas-transfer-form__confirm-btn" @click="submitForm">
        确认转气
      </van-button>
    </div>

    <!-- 电子小票弹窗 -->
    <ReceiptModal ref="printRef" />
  </div>
</template>

<style lang="less" scoped>
.gas-transfer-form {
  &__container {
    margin-bottom: 16px;
  }

  &__field {
    margin-bottom: 16px;
  }

  &__label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 4px;
  }

  &__field-with-unit {
    position: relative;

    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__unit {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 14px;
    pointer-events: none;
  }

  &__input-wrapper {
    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__address-field {
    :deep(.van-field__control) {
      min-height: 60px;
    }
  }

  &__search-wrapper {
    display: flex;
    width: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;
    background-color: #f9fafb;
  }

  &__search-input {
    flex: 1;
    height: 40px;
    padding: 0 12px;
    font-size: 14px;
    border: none;
    outline: none;
    background-color: transparent;

    &::placeholder {
      color: #999;
    }
  }

  &__search-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 40px;
    background-color: var(--van-primary-color);
    color: white;
    border: none;
    cursor: pointer;
  }

  &__note {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;

    &--italic {
      font-style: italic;
    }

    &--small {
      font-size: 10px;
    }
  }

  &__total-content {
    width: 100%;
  }

  &__total-label {
    font-size: 16px;
    font-weight: 500;
    color: #4b5563;
    margin: 0 0 4px 0;
  }

  &__total-value {
    font-size: 20px;
    font-weight: 600;
    color: #2563eb;
  }

  &__total-sub {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
  }

  &__buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  &__cancel-btn {
    border-color: #d1d5db;
    color: #4b5563;
  }

  &__confirm-btn {
    background-color: #2563eb;
  }

  &__section-margin {
    margin-bottom: 16px;
  }

  &__summary {
    background: linear-gradient(to right, #ebf4ff, #e0f2fe);
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  &__explanation {
    background-color: #f0f7ff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
  }

  &__explanation-title {
    color: #003366;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
  }

  &__explanation-list {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      margin-bottom: 6px;
      line-height: 1.4;
      font-size: 14px;
      color: #2196f3;
      position: relative;
      padding-left: 16px;
      display: flex;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: #2563eb;
        border-radius: 50%;
        margin-right: 10px;
        flex-shrink: 0;
        margin-top: 6px;
      }
    }
  }
}

.text-red-500 {
  color: #ef4444;
}
</style>
