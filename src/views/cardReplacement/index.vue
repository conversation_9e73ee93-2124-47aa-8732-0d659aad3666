<script setup lang="ts">
import type { PickerOption } from 'vant'
import type { FileItem } from '@/components/common/FileUploader.vue'
import type { FillCardParam } from '@/services/cardService/types'
import CardContainer from '@af-mobile-client-vue3/components/data/CardContainer/CardContainer.vue'
import CardHeader from '@af-mobile-client-vue3/components/data/CardContainer/CardHeader.vue'
import { getConfigByNameAsync } from '@af-mobile-client-vue3/services/api/common'
import { showConfirmDialog, showToast } from 'vant'
import { computed, onMounted, reactive, ref } from 'vue'
import {
  ChargePrintSelectorAndRemarks,
  CodePayment,
  FileUploader,
  GridFileUploader,
  PaymentMethodSelectorCard,
  ReceiptModal,
} from '@/components/common'
import useBusinessStore from '@/stores/modules/business'
import 'vant/es/toast/style'
import 'vant/es/dialog/style'

const emit = defineEmits(['closeOperation', 'complete'])
const businessStore = useBusinessStore()
const userInfo = businessStore.currentUser

const config = reactive<any>({
  printType: '普通收据',
  payment: '现金缴费',
  allowedMethods: null,
  printLogic: '',
  // 是否展示补卡次数
  showFillCardTime: false,
  // 补卡费用数组
  replacementFeeList: [0, 10, 30],
  // 文件上传类型配置
  fileTypes: [],
})

interface FormData {
  replacementReason: string
  replacementType: string
  replacementFee: number | null
  totalAmount: number
  receiptType: string
  remarks: string
  paymentMethod: string
  f_times: number
}

const formData = reactive<FormData>({
  replacementReason: '',
  replacementType: '',
  replacementFee: null,
  totalAmount: 0,
  receiptType: '',
  remarks: '',
  paymentMethod: '',
  f_times: Number(userInfo.f_times),
})

// 子组件的响应组件引用
const printRef = ref()
const gridFileUploaderRef = ref()

const uploadedFiles = ref<FileItem[]>([])

// 判断是否使用宫格上传组件
const useGridUploader = computed(() => {
  return config.fileTypes && config.fileTypes.length > 0
})

// 计算支付区域是否显示
const showPaymentSection = ref(true)

// 控制选择器显示
const showReasonPicker = ref(false)
const showTypePicker = ref(false)
const showFeePicker = ref(false)

const showQrCodePayment = ref(false)

// 补卡原因选项
const reasonColumns = ref<PickerOption[]>([
  { text: 'IC卡丢失或损坏', value: 'IC卡丢失或损坏' },
  { text: 'IC卡信息错乱', value: 'IC卡信息错乱' },
  { text: '其他原因', value: '其他原因' },
])

// 补卡类型选项
const typeColumns = ref<PickerOption[]>([
  { text: '新卡补卡', value: '新卡补卡' },
  { text: '原卡补卡', value: '原卡补卡' },
])

// 补卡费用选项
const feeColumns = ref<PickerOption[]>([
  { text: '免费', value: 0 },
  { text: '10元', value: 10 },
  { text: '30元', value: 30 },
])

// 选择补卡原因确认
function onReasonConfirm(event: any) {
  if (event && event.selectedOptions && event.selectedOptions.length > 0) {
    formData.replacementReason = event.selectedOptions[0].value
    showReasonPicker.value = false
    showToast(`已选择：${event.selectedOptions[0].text}`)
    updateFeeByReason()
  }
}

// 选择补卡类型确认
function onTypeConfirm(event: any) {
  if (event && event.selectedOptions && event.selectedOptions.length > 0) {
    formData.replacementType = event.selectedOptions[0].value
    showTypePicker.value = false
    showToast(`已选择：${event.selectedOptions[0].text}`)
    updateFeeByType()
  }
}

// 选择补卡费用确认
function onFeeConfirm(event: any) {
  if (event && event.selectedOptions && event.selectedOptions.length > 0) {
    formData.replacementFee = event.selectedOptions[0].value
    showFeePicker.value = false
    showToast(`已选择：${event.selectedOptions[0].text}`)
    updateTotalAmount()
  }
}

// 初始化
onMounted(async () => {
  // 获取补卡业务配置信息
  try {
    const replaceCardConfig = await getConfigByNameAsync('mobile_replaceCardConfig')
    if (replaceCardConfig) {
      // 更新配置
      Object.assign(config, replaceCardConfig)

      // 更新表单默认值
      formData.receiptType = config.printType
      formData.paymentMethod = config.payment
    }
    else {
      console.log('配置数据为空或格式不正确')
      console.log('完整的响应数据结构:', replaceCardConfig)
    }
  }
  catch (error) {
    console.error('获取补卡业务配置失败', error)
  }

  // 从配置获取补卡原因
  try {
    const reasonConfig = await getConfigByNameAsync('replaceCardReasonDic')
    if (reasonConfig && Array.isArray(reasonConfig.value) && reasonConfig.value.length > 0) {
      // 将配置转换为选择器所需的格式
      reasonColumns.value = reasonConfig.value.map(item => ({
        text: item.text || item.label || item.value,
        value: item.value || item.text || item.label,
      }))
    }

    // 从配置获取补卡类型
    const typeConfig = await getConfigByNameAsync('replaceCardTypeDic')
    if (typeConfig && Array.isArray(typeConfig.value) && typeConfig.value.length > 0) {
      // 将配置转换为选择器所需的格式
      typeColumns.value = typeConfig.value.map(item => ({
        text: item.text || item.label || item.value,
        value: item.value || item.text || item.label,
      }))
    }
  }
  catch (error) {
    console.error('获取补卡配置失败', error)
  }

  // 调用原有的初始化逻辑
  updateFeeByReason()
  updateFeeByType()

  // 根据配置初始化补卡费用选项
  if (config.replacementFeeList && Array.isArray(config.replacementFeeList)) {
    feeColumns.value = config.replacementFeeList.map(fee => ({
      text: fee === 0 ? '免费' : `${fee}元`,
      value: fee,
    }))
  }
})

// 根据补卡原因更新费用
function updateFeeByReason() {
  // 仍然根据补卡类型和原因决定是否显示支付部分（仅作为初始状态）
  if (formData.replacementType === '原卡补卡') {
    showPaymentSection.value = false
    return
  }

  // 如果是新卡补卡，根据补卡原因确定初始显示状态
  if (formData.replacementType === '新卡补卡') {
    if (formData.replacementReason === 'IC卡信息错乱') {
      showPaymentSection.value = false
    }
    else if (formData.replacementReason === 'IC卡丢失或损坏' || formData.replacementReason === '其他原因') {
      showPaymentSection.value = formData.replacementFee > 0
    }
    else {
      showPaymentSection.value = false
    }
  }
  else {
    showPaymentSection.value = false
  }

  // 同步总金额
  formData.totalAmount = formData.replacementFee || 0
}

// 根据补卡类型更新费用
function updateFeeByType() {
  // 仍然根据补卡类型和原因决定是否显示支付部分（仅作为初始状态）
  if (formData.replacementType === '原卡补卡') {
    showPaymentSection.value = false
    return
  }

  // 新卡补卡，根据补卡原因确定初始显示状态
  if (formData.replacementType === '新卡补卡') {
    if (formData.replacementReason === 'IC卡信息错乱') {
      showPaymentSection.value = false
    }
    else if (formData.replacementReason === 'IC卡丢失或损坏' || formData.replacementReason === '其他原因') {
      showPaymentSection.value = formData.replacementFee > 0
    }
    else {
      showPaymentSection.value = false
    }
  }
  else {
    showPaymentSection.value = false
  }

  // 同步总金额
  formData.totalAmount = formData.replacementFee || 0
}

// 当补卡费用变化时更新总金额及支付部分显示
function updateTotalAmount() {
  formData.totalAmount = formData.replacementFee || 0

  // 根据补卡费用决定是否显示支付部分
  showPaymentSection.value = formData.replacementFee > 0
}

// 生成收据
async function generateReceipt(event: Event) {
  event.preventDefault()

  // 验证必填字段
  if (!formData.replacementReason) {
    showToast('请选择补卡原因')
    return
  }

  if (!formData.replacementType) {
    showToast('请选择补卡类型')
    return
  }

  if (formData.replacementFee === undefined || formData.replacementFee === null) {
    showToast('请输入补卡费用')
    return
  }

  if (!formData.receiptType) {
    showToast('请选择收据打印选项')
    return
  }

  // 如果补卡费用大于0，验证支付方式
  if (formData.replacementFee > 0 && !formData.paymentMethod) {
    showToast('请选择支付方式')
    return
  }

  // 验证文件上传要求
  if (useGridUploader.value && gridFileUploaderRef.value) {
    const isValid = gridFileUploaderRef.value.validateAll()
    if (!isValid) {
      return
    }
  }

  try {
    // 显示加载提示
    const toast = showToast({
      type: 'loading',
      message: '正在处理补卡请求...',
      forbidClick: true,
      duration: 0,
    })

    // 微信/支付宝支付走单独的流程，通过事件回调处理支付结果
    if (formData.paymentMethod.includes('支付宝') || formData.paymentMethod.includes('微信')) {
      toast.close()
      showQrCodePayment.value = true
    }
    // 其他支付方式直接调用收费接口进行处理
    else {
      // 执行补卡操作
      await simulateCardWriting()
    }
  }
  catch (error) {
    // 显示错误提示
    // Toast.clear()
    showToast({
      type: 'fail',
      message: `补卡失败：${error.message || error.msg || '未知错误'}`,
    })
  }
}

async function handleQrCodePaymentSuccess(paymentResult) {
  try {
    // 使用公共处理函数处理支付
    await simulateCardWriting(paymentResult.tradeNo)
    showQrCodePayment.value = false
  }
  catch (error) {
    console.error(error)

    showToast(`补卡失败${error.message || error.msg || '未知错误'}`)
  }
}

function handleQrCodePaymentCancel() {
  showQrCodePayment.value = false
}

// 模拟IC卡写卡操作（提交）
async function simulateCardWriting(tradeNo = '') {
  try {
    console.log('正在进行补卡操作...')

    // 从cardService导入补卡服务
    const cardService = await import('@/services/cardService/index')

    // 准备补卡参数
    const cardParam: FillCardParam = {
      f_card_id: userInfo.f_card_id || '',
      f_fill_type: formData.replacementType === '原卡补卡' ? '原卡补卡' : '新卡补卡',
      f_type: formData.replacementReason,
      f_payment: formData.paymentMethod,
      f_cardfees: formData.replacementFee || 0,
      f_serial_id: tradeNo || '',
      f_comments: formData.remarks,
      f_bill_style: formData.receiptType,
      f_times: formData.f_times,
      f_ispick_table: '是', // 默认接表
    }
    if (uploadedFiles.value && uploadedFiles.value.length > 0) {
      cardParam.files = uploadedFiles.value
        .filter(file => file.result) // 过滤出有result属性的文件
        .map(file => file.result?.id)
        .filter(Boolean) // 过滤掉空值
    }
    console.log('补卡的参数：', cardParam)
    // 调用补卡服务
    const result = await cardService.replaceCard(cardParam, userInfo)
    console.log('补卡操作成功', result)

    // 显示成功提示
    showToast({
      type: 'success',
      message: '补卡操作成功',
    })

    // 显示收据
    await printRef.value.printParams(config.printLogic, result.id)
  }
  catch (error) {
    console.error('补卡操作失败', error)
    showToast({
      type: 'fail',
      message: `补卡失败：${error.message || error.msg || '未知错误'}`,
    })
    throw error
  }
}

// 取消操作
function cancelOperation() {
  // 通知父页面关闭操作界面
  showConfirmDialog({
    title: '提示',
    message: '确定取消补卡操作吗？',
  }).then(() => {
    // 确认取消
    emit('closeOperation')
    showToast('已取消补卡')
  }).catch(() => {
    // 返回继续操作
  })
}

// 文件上传相关
function onFileAdded() {
}

function onFileRemoved() {
}

function handleReceiptOk() {
  emit('complete', { status: true })
}
</script>

<template>
  <div class="card-replacement-form">
    <!-- 信息提示卡片被删除 -->

    <form class="card-replacement-form__container" @submit.prevent="generateReceipt">
      <!-- 补卡信息区域 -->
      <CardContainer class="card-replacement-form__section-margin">
        <CardHeader title="补卡信息" />
        <div class="card-replacement-form__field">
          <label class="card-replacement-form__label">补卡原因<span class="text-red-500">*</span></label>
          <div class="card-replacement-form__selector">
            <van-field
              v-model="formData.replacementReason"
              is-link
              readonly
              placeholder="请选择补卡原因"
              @click="showReasonPicker = true"
            />
          </div>
        </div>

        <div class="card-replacement-form__field">
          <label class="card-replacement-form__label">补卡类型<span class="text-red-500">*</span></label>
          <div class="card-replacement-form__selector">
            <van-field
              v-model="formData.replacementType"
              is-link
              readonly
              placeholder="请选择补卡类型"
              @click="showTypePicker = true"
            />
          </div>
        </div>

        <div v-if="config.showFillCardTime" class="card-replacement-form__field">
          <label class="card-replacement-form__label">补卡后次数<span class="text-red-500">*</span></label>
          <div class="card-replacement-form__selector">
            <van-field v-model="formData.f_times" />
          </div>
        </div>

        <div class="card-replacement-form__field">
          <label class="card-replacement-form__label">补卡费用(元)<span class="text-red-500">*</span></label>
          <div class="card-replacement-form__selector">
            <van-field
              :model-value="formData.replacementFee === 0 ? '免费' : formData.replacementFee ? `${formData.replacementFee}元` : ''"
              is-link
              readonly
              placeholder="请选择补卡费用"
              @click="showFeePicker = true"
            />
          </div>
        </div>
      </CardContainer>

      <!-- 收费信息 -->
      <CardContainer class="card-replacement-form__section-margin">
        <CardHeader title="收费信息" />
        <div class="card-replacement-form__field">
          <label class="card-replacement-form__label">应收金额(元)</label>
          <div class="card-replacement-form__amount-input">
            <van-field
              v-model="formData.totalAmount"
              readonly
              input-align="right"
            >
              <template #prefix>
                <span class="card-replacement-form__prefix">¥</span>
              </template>
            </van-field>
          </div>
        </div>
      </CardContainer>

      <!-- 打印及备注 -->
      <ChargePrintSelectorAndRemarks
        v-model="formData.receiptType"
        v-model:remarks="formData.remarks"
        class="card-replacement-form__section-margin"
      />

      <!-- 支付方式 -->
      <PaymentMethodSelectorCard
        v-if="showPaymentSection"
        v-model="formData.paymentMethod"
        title="支付方式"
        :allowed-methods="config.allowedMethods"
        class="card-replacement-form__section-margin"
      />

      <!-- 文件上传区域 -->
      <GridFileUploader
        v-if="useGridUploader"
        ref="gridFileUploaderRef"
        v-model:file-list="uploadedFiles"
        :file-types="config.fileTypes"
        title="上传附件"
        class="card-replacement-form__section-margin"
      />
      <CardContainer
        v-else
        class="card-replacement-form__section-margin"
      >
        <FileUploader
          v-model:file-list="uploadedFiles"
          title="上传附件"
          :multiple="true"
          :user-type="config.fileTypes.length > 0 ? config.fileTypes[0].userType : '补卡'"
          accept=".jpg,.jpeg,.png,.pdf"
          @file-added="onFileAdded"
          @file-removed="onFileRemoved"
        />
      </CardContainer>

      <!-- 应收金额展示 -->
      <CardContainer class="card-replacement-form__summary">
        <div class="card-replacement-form__total-content">
          <p class="card-replacement-form__total-label">
            应收金额：<span class="card-replacement-form__total-value">¥ {{ formData.totalAmount || 0 }}</span>
          </p>
        </div>
      </CardContainer>

      <!-- 按钮区域 -->
      <div class="card-replacement-form__buttons">
        <van-button
          plain
          type="default"
          class="card-replacement-form__cancel-btn"
          @click="cancelOperation"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          native-type="submit"
          class="card-replacement-form__confirm-btn"
        >
          确认补卡
        </van-button>
      </div>
    </form>

    <!-- 底部弹出选择器 -->
    <van-popup
      v-model:show="showReasonPicker"
      position="bottom"
      round
      teleport="body"
      :overlay="true"
      :close-on-click-overlay="false"
    >
      <van-picker
        title="选择补卡原因"
        :columns="reasonColumns"
        show-toolbar
        @confirm="onReasonConfirm"
        @cancel="showReasonPicker = false"
      />
    </van-popup>

    <van-popup
      v-model:show="showTypePicker"
      position="bottom"
      round
      teleport="body"
      :overlay="true"
      :close-on-click-overlay="false"
    >
      <van-picker
        title="选择补卡类型"
        :columns="typeColumns"
        show-toolbar
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>

    <van-popup
      v-model:show="showFeePicker"
      position="bottom"
      round
      teleport="body"
      :overlay="true"
      :close-on-click-overlay="false"
    >
      <van-picker
        title="选择补卡费用"
        :columns="feeColumns"
        show-toolbar
        @confirm="onFeeConfirm"
        @cancel="showFeePicker = false"
      />
    </van-popup>

    <!-- 收据弹窗 -->
    <ReceiptModal ref="printRef" @ok="handleReceiptOk" @close="handleReceiptOk" />
    <!-- 二维码支付弹窗 -->
    <CodePayment
      v-if="showQrCodePayment"
      v-model:show="showQrCodePayment"
      :payment="formData.paymentMethod"
      :collection="formData.replacementFee || 0"
      type="补卡"
      @payment-success="handleQrCodePaymentSuccess"
      @payment-cancel="handleQrCodePaymentCancel"
    />
  </div>
</template>

<style scoped lang="less">
.card-replacement-form {
  &__info-box {
    display: none; /* 隐藏信息提示卡片 */
  }

  &__container {
    margin-bottom: 16px;
  }

  &__field {
    margin-bottom: 16px;
  }

  &__label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 4px;
  }

  &__selector {
    position: relative;

    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__amount-input {
    :deep(.van-field) {
      background-color: #f9fafb;
      border-radius: 6px;
    }
  }

  &__prefix,
  &__suffix {
    color: #6b7280;
    font-size: 14px;
  }

  &__total-content {
    width: 100%;
  }

  &__total-label {
    font-size: 16px;
    font-weight: 500;
    color: #4b5563;
    margin: 0 0 4px 0;
  }

  &__total-value {
    font-size: 20px;
    font-weight: 600;
    color: #2563eb;
  }

  &__buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  &__cancel-btn {
    border-color: #d1d5db;
    color: #4b5563;
  }

  &__confirm-btn {
    background-color: #2563eb;
  }

  &__section-margin {
    margin-bottom: 16px;
  }

  &__summary {
    background: linear-gradient(to right, #ebf4ff, #e0f2fe);
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
}
</style>
