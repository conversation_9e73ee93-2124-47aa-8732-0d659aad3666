import App from '@af-mobile-client-vue3/App.vue'
import bootstrap from '@af-mobile-client-vue3/bootstrap'
import { i18n } from '@af-mobile-client-vue3/utils/i18n'
import { Icon } from '@iconify/vue'
import { createHead } from '@unhead/vue/client'
import { createApp } from 'vue'
import Plugins from '@/plugins'
import router from '@/router'
import pinia from '@/stores'
import { loadOfflineIcons } from '@/utils/iconLoader'
import 'virtual:uno.css'
import '@af-mobile-client-vue3/styles/app.less'
import '@af-mobile-client-vue3/styles/var.less'

// Vant 桌面端适配
import '@vant/touch-emulator'

/* --------------------------------
Vant 中有个别组件是以函数的形式提供的，
包括 Toast，Dialog，Notify 和 ImagePreview 组件。
在使用函数组件时，unplugin-vue-components
无法自动引入对应的样式，因此需要手动引入样式。
------------------------------------- */
import 'vant/es/toast/style'
import 'vant/es/dialog/style'
import 'vant/es/notify/style'
import 'vant/es/image-preview/style'

(async () => {
  const app = createApp(App)
  const head = createHead()

  // 全局注册 Icon 组件
  app.component('Icon', Icon)

  // 加载项目中实际使用的离线图标
  loadOfflineIcons()

  app.use(head)
  app.use(router)
  app.use(pinia)
  app.use(i18n)
  app.use(Plugins)

  await bootstrap(router)
  app.mount('#revenue-app')

  // 👇 将卸载操作放入 unmount 函数，就是上面步骤2中的卸载函数
  window.unmount = () => {
    app.unmount()
  }
})()
