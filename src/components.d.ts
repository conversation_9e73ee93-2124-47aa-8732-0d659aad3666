/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCol: typeof import('vant/es')['Col']
    VanConfigProvider: typeof import('vant/es')['ConfigProvider']
    VanDivider: typeof import('vant/es')['Divider']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanRow: typeof import('vant/es')['Row']
    VanSwipeCell: typeof import('vant/es')['SwipeCell']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
  }
}
