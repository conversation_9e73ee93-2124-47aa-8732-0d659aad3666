kind: Deployment
apiVersion: apps/v1
metadata:
  name: af-revenue-mobile-web
  namespace: service
  labels:
    app: af-revenue-mobile-web
  annotations:
    kubesphere.io/creator: admin
    kubesphere.io/description: V4 营收手机端前台
spec:
  replicas: 1
  selector:
    matchLabels:
      app: af-revenue-mobile-web
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: af-revenue-mobile-web
      annotations:
        kubesphere.io/creator: admin
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
        - name: logs
          hostPath:
            path: /var/af/v4service/af-revenue-mobile-web/logs
            type: ''
      containers:
        - name: af-revenue-mobile-web
          image: >-
            harborcdn.aofengcloud.com/afsoft_client/af-system-vue-web:202502171715
          ports:
            - name: tcp-8080
              containerPort: 8080
              protocol: TCP
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: logs
              mountPath: /var/log/nginx
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: harbor-inner-server
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
