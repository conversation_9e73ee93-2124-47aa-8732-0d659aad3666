import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import * as tar from 'tar'

// 当前文件所在目录
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 压缩源目录名（即 build 后生成的目录名）
const outputName = `dist_af-revenue-mobile-web`

// 源目录路径
const cwd = path.join(__dirname, 'dist', outputName)

// 输出 tar.gz 文件路径
const outputPath = path.join(__dirname, 'dist', `${outputName}.tar.gz`)

if (!fs.existsSync(cwd)) {
  console.error('❌ 目录不存在:', cwd)
  // eslint-disable-next-line node/prefer-global/process
  process.exit(1)
}

tar.c(
  {
    gzip: true,
    file: outputPath,
    cwd,
  },
  ['.'],
).then(() => {
  console.log(`✅ 压缩完成: ${outputPath}`)
}).catch((err) => {
  console.error('❌ 压缩失败:', err)
})
