import java.text.SimpleDateFormat

final String SERVICE_NAME = "af-revenue-mobile-web"
final String MASTER_IP_67 = "aote-office.8866.org -p 20302"
final String MASTER_IP_105 = "aote-office.8866.org -p 20303"
final String HARBOR = "*************:32767"
final String HARBOR_CDN = "harborcdn.aofengcloud.com"
final String DOCKER_SLAVE_URL = "tcp://*************:2375"
final String DOCKER_WORK_DIR_NAME = "afsoft_client_release"
final String KUBESPHERE_PROJECT_NAME = "v4service-release"
final String DIST_MAIN_PATH = "/var/af/standard-release/client/phone_dist"

pipeline {
    agent any
    environment {
        // 镜像仓库
        DOCKER_REGISTRY = "https://${HARBOR}"
        HARBOR_URL = "${HARBOR}"
        // 镜像名称
        IMAGE_NAME = "${SERVICE_NAME}"
        WORK_DIR = "${DOCKER_WORK_DIR_NAME}"
    }
    stages {
        stage("build") {
            tools {
              nodejs 'node20'
            }
            steps {
                script {
                    sh "pnpm install --registry https://registry.npmjs.org --proxy http://aote-office.8866.org:31691 --https-proxy http://aote-office.8866.org:31691"
                    sh "pnpm run build:pro"
                    // 自动从package.json中读取版本号
                    def packageJson = readJSON file: 'package.json'
                    env.BUILD_VERSION = packageJson.version
                }
            }
        }
        stage('ssh') {
            steps {
                script {
                    def remote = [
                        name         : '*************',
                        host         : '*************',
                        port         : '20302' as int,
                        allowAnyHosts: true
                    ]
                    withCredentials([sshUserPrivateKey(credentialsId: '0.106test', keyFileVariable: 'identity', passphraseVariable: '', usernameVariable: 'userName')]) {
                        remote.user = userName
                        remote.identityFile = identity
                        // 更新dist包
                        sshCommand remote: remote, command: "pwd"
                        sshCommand remote: remote, command: "mkdir -p ${DIST_MAIN_PATH}"
                        sshPut remote: remote, from: "dist/dist_${SERVICE_NAME}.tar.gz", into: "${DIST_MAIN_PATH}/"
                        sshCommand remote: remote, command: "cd ${DIST_MAIN_PATH} && rm -rf ${DIST_MAIN_PATH}/dist_${SERVICE_NAME}"
                    }
                }
            }
        }
        stage('docker') {
            steps {
                script {
                    def yamlFile = readYaml file: "${SERVICE_NAME}-release.yml"
                    yamlFile.metadata['namespace'] = "${KUBESPHERE_PROJECT_NAME}"
                    yamlFile.spec.template.spec.containers[0].image = "${HARBOR_CDN}/${WORK_DIR}/${IMAGE_NAME}:${env.BUILD_VERSION}"
                    writeYaml file: "${SERVICE_NAME}-release.yml", data: yamlFile, overwrite: true
                    docker.withServer("${DOCKER_SLAVE_URL}") {
                        buildAndPushDockerImage()
                    }
                }
            }
        }
        stage('50.67applyToK8S') {
            steps {
                sshPublisher(publishers: [
                        sshPublisherDesc(
                                configName: 'K8S',
                                transfers: [
                                        sshTransfer(
                                                sourceFiles: "${SERVICE_NAME}-release.yml",
                                                execCommand: "kubectl apply -f /root/deployment/${SERVICE_NAME}-release.yml"
                                        )
                                ]
                        )
                ])
            }
        }
        stage('50.105applyToK8S') {
            steps {
                sshPublisher(publishers: [
                        sshPublisherDesc(
                                configName: '105_K8S',
                                transfers: [
                                        sshTransfer(
                                                sourceFiles: "${SERVICE_NAME}-release.yml",
                                                execCommand: "kubectl apply -f /root/deployment/${SERVICE_NAME}-release.yml"
                                        )
                                ]
                        )
                ])
            }
        }
    }
}

def buildAndPushDockerImage() {
    try {
        sh "docker build -t ${IMAGE_NAME}:${env.BUILD_VERSION} ."
        withCredentials([usernamePassword(
                credentialsId: 'harbor-auth',
                usernameVariable: 'DOCKER_REGISTRY_USERNAME',
                passwordVariable: 'DOCKER_REGISTRY_PASSWORD'
        )]) {
            sh "docker login -u ${DOCKER_REGISTRY_USERNAME} -p ${DOCKER_REGISTRY_PASSWORD} ${DOCKER_REGISTRY}"
        }
        sh "docker tag ${IMAGE_NAME}:${env.BUILD_VERSION} ${HARBOR_URL}/${WORK_DIR}/${IMAGE_NAME}:${env.BUILD_VERSION}"
        sh "docker tag ${IMAGE_NAME}:${env.BUILD_VERSION} ${HARBOR_URL}/${WORK_DIR}/${IMAGE_NAME}:latest"
        sh "docker push ${HARBOR_URL}/${WORK_DIR}/${IMAGE_NAME}:${env.BUILD_VERSION}"
        sh "docker push ${HARBOR_URL}/${WORK_DIR}/${IMAGE_NAME}:latest"
    } finally {
        sh "docker rmi ${IMAGE_NAME}:${env.BUILD_VERSION}"
    }
}
