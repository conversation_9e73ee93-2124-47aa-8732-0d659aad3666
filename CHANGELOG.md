# 更新日志 (CHANGELOG)

本文档记录发卡售气组件库的所有重要变更。

## [1.0.0] - 2025-04-13

### 新增

- 完成通用组件库的开发，包含以下组件：
  - `CardContainer`：卡片容器组件
  - `CardHeader`：卡片标题组件
  - `FormField`：表单字段组件
  - `InfoCard`：信息卡片组件
  - `PaymentMethodSelector`：支付方式选择器组件
  - `FileUploader`：文件上传组件
  - `ReceiptModal`：收据弹窗组件

- 完成发卡售气业务组件库的开发，包含以下组件：
  - `SellGasForm`：发卡售气表单主组件
  - `GasExchangeCalculator`：气量和金额换算组件
  - `PaymentInfo`：收款信息组件
  - `PrintOptions`：打印及备注组件
  - `GasReceipt`：气表收据组件

- 开发完整的示例页面 `SellGas/index.vue`，展示组件库的使用方法
- 添加路由配置，支持通过 `/SellGas` 路径访问示例页面
- 为所有组件目录添加详细的 README 文档

### 优化

- 使用 Vue3 组合式 API (setup) 实现所有组件
- 使用 Vant4 组件库替代 Tailwind CSS
- 采用 Less 预处理器处理样式
- 优化组件设计，遵循低耦合高内聚原则
- 实现组件间数据通信，使用 v-model 自定义绑定

### 修复

- 修复了 `CollapseTransition` 组件样式导入问题
- 修复了 `GasExchangeCalculator` 组件中函数初始化顺序导致的引用错误
- 修复了 `ReceiptModal` 组件中 Vant Popup 样式加载问题

### 文档

- 添加详细的组件使用文档
- 为每个组件添加属性、事件、插槽说明
- 添加示例代码与使用说明

---

作者：江超 